import axios from "axios";

const compensateApi = {};

compensateApi.getCompensateInfo = () => {
    return axios({
        url: "/api/coupon/v3/compensate/getDetail"
    });
};

compensateApi.updateCompensateInfo = data => {
    return axios({
        url: "/api/coupon/v3/compensate/update",
        method: "POST",
        data
    });
};

compensateApi.getCompensateCouponList = () => {
    return axios({
        url: "/api/coupon/v3/compensate/getCompensateCoupon"
    });
};

export default compensateApi;
