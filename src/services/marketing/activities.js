import axios from "axios";

function getActivitiesList(data) {
    // 获取目录
    return axios({
        url: "/api/activity/v3/activity/list",
        method: "get",
        params: data
    });
}
function addActivities(data) {
    // 添加活动
    return axios({
        url: "/api/activity/v3/activity/create",
        method: "post",
        data
    });
}
function deleteActivitiesLabel(data) {
    // 删除活动标签
    return axios({
        url: "/api/activity/v3/label/delete",
        method: "post",
        data
    });
}

function editActivities(data) {
    // 编辑活动
    return axios({
        url: "/api/activity/v3/activity/update",
        method: "post",
        data
    });
}
function getActivityDetails(data) {
    //获取活动详情
    return axios({
        url: "/api/activity/v3/activity/detail",
        method: "get",
        params: data
    });
}
function isGoodsExist(data) {
    //获取活动详情
    return axios({
        url: "/api/activity/v3/label/isGoodsExist",
        method: "get",
        params: data
    });
}

function getGoodsList(data) {
    // 获取商品
    return axios({
        url: "/api/activity/v3/goods/list",
        method: "get",
        params: data
    });
}
function addGoods(data) {
    // 添加商品
    return axios({
        url: "/api/activity/v3/goods/create",
        method: "post",
        data
    });
}
function updateGoods(data) {
    // 添加商品
    return axios({
        url: "/api/activity/v3/goods/update",
        method: "post",
        data
    });
}
function getGoodsById(data) {
    return axios({
        url: "/api/commodities/v3/periods/getESPeriodInfoById",
        method: "get",
        params: data
    });
}

function getCustomActivityPackage(data) {
    return axios({
        url: "/api/commodities/v3/package/getCustomActivityPackage",
        method: "get",
        params: data
    });
}

function copyGoodsForActivity(data) {
    return axios({
        url: "/api/activity/v3/goods/copy",
        method: "post",
        data
    });
}
function activityCopy(data) {
    return axios({
        url: "/api/activity/v3/activity/copy",
        method: "post",
        data
    });
}
//获取酒云网小程序无限量二维码
function getMinappQrcode(data) {
    return axios({
        url: "/api/wechat/v3/minapp/qrcode/unlimit",
        method: "post",
        data
    });
}

function getPvUvTrend(params) {
    return axios({
        url: "/api/maidian/v3/report/GetPvUvByDateRange",
        method: "get",
        params: {
            ...params,
            channelId: 10,
            regionId: 999999
        }
    });
}

export default {
    getActivitiesList,
    addActivities,
    editActivities,
    getGoodsList,
    addGoods,
    updateGoods,
    getGoodsById,
    getCustomActivityPackage,
    getActivityDetails,
    isGoodsExist,
    deleteActivitiesLabel,
    copyGoodsForActivity,
    activityCopy,
    getMinappQrcode,
    getPvUvTrend
};
