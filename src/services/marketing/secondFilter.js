import axios from "axios";

// 秒发筛选配置列表
const secondFilterList = params => {
    return axios({
        url: "/api/commodities_server/v3/second/filters/list",
        method: "get",
        params
    });
};
// 添加秒发筛选配置
const secondFilterAdd = data => {
    return axios({
        url: "/api/commodities_server/v3/second/filters/add",
        method: "post",
        data
    });
};
// 编辑秒发筛选配置
const secondFilterEdit = data => {
    return axios({
        url: "/api/commodities_server/v3/second/filters/edit",
        method: "post",
        data
    });
};
// 更新秒发筛选配置
const secondFilterUpdate = data => {
    return axios({
        url: "/api/commodities_server/v3/second/filters/update",
        method: "post",
        data
    });
};

// 秒发筛选商品列表
const secondFilterGoodsList = params => {
    return axios({
        url: "/api/commodities_server/v3/second/filters_goods/list",
        method: "get",
        params
    });
};

// 添加秒发筛选商品
const secondFilterGoodsAdd = data => {
    return axios({
        url: "/api/commodities_server/v3/second/filters_goods/add",
        method: "post",
        data
    });
};

// 编辑秒发筛选商品
const secondFilterGoodsEdit = data => {
    return axios({
        url: "/api/commodities_server/v3/second/filters_goods/edit",
        method: "post",
        data
    });
};
// 删除秒发筛选商品
const secondFilterGoodsDel = data => {
    return axios({
        url: "/api/commodities_server/v3/second/filters_goods/delete",
        method: "post",
        data
    });
};
// 更新秒发筛选商品
const secondFilterGoodsUpdate = data => {
    return axios({
        url: "/api/commodities_server/v3/second/filters_goods/update",
        method: "post",
        data
    });
};
export default {
    secondFilterList,
    secondFilterAdd,
    secondFilterEdit,
    secondFilterUpdate,
    secondFilterGoodsList,
    secondFilterGoodsAdd,
    secondFilterGoodsEdit,
    secondFilterGoodsDel,
    secondFilterGoodsUpdate
};
