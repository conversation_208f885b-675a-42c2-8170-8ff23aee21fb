import axios from "axios";

//获取客户端路径列表
function getClientPathList(data) {
    return axios({
        url: "/api/marketing-conf/v3/clientpath/list",
        method: "get",
        params: data
    });
}

//添加客户端路径
function addClientPath(data) {
    return axios({
        url: "/api/marketing-conf/v3/clientpath/create",
        method: "post",
        data
    });
}

//编辑客户端路径
function editClientPath(data) {
    return axios({
        url: "/api/marketing-conf/v3/clientpath/update",
        method: "post",
        data
    });
}

//客户端路径过滤
function FilterClientPath(data) {
    return axios({
        url: "/api/marketing-conf/v3/clientpath/filterlist",
        method: "get",
        params: data
    });
}

//用于编辑广告区，金刚区时获取路径详情
function EditPathDetail(data) {
    return axios({
        url: "/api/marketing-conf/v3/ad/detail",
        method: "get",
        params: data
    });
}
//查询通过唯一标识
function getClientPathByUnique(data) {
    return axios({
        url: "/api/marketing-conf/v3/clientpath/codefilterlist",
        method: "get",
        params: data
    });
}


export default {
    getClientPathList,
    addClientPath,
    editClientPath,
    FilterClientPath,
    EditPathDetail,
    getClientPathByUnique
};
