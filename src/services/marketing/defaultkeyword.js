import axios from "axios";

function getDefaultKeywordList(data) {
    // 发送
    return axios({
        url: "/api/marketing-conf/v3/defaultkeyword/list",
        method: "get",
        params:data
    });    
}
function updateKeyword(data) {
    // 发送
    return axios({
        url: "/api/marketing-conf/v3/defaultkeyword/update",
        method: "post",
        data
    });    
}
function getKeywordDetails(data) {
    // 发送}
    return axios({
        url: "/api/marketing-conf/v3/defaultkeyword/getKeyword",
        method: "post",
        params:data
    });    
}
export default {
    getDefaultKeywordList,
    updateKeyword,
    getKeywordDetails
    
};
