import axios from "axios";

function getBulleinList(data) {
    // 快报列表
    return axios({
        url: "/api/marketing-conf/v3/bullein/list",
        method: "get",
        params:data
    });    
}
function addBullein(data) {
    // 快报列表
    return axios({
        url: "/api/marketing-conf/v3/bullein/create",
        method: "post",
        data
    });    
}
function updateBullein(data) {
    // 更新快报
    return axios({
        url: "/api/marketing-conf/v3/bullein/update",
        method: "post",
        data
    });    
}
function updateBulleinStatus(data) {
    // 更改快报状态
    return axios({
        url: "/api/marketing-conf/v3/bullein/status",
        method: "post",
        data
    });    
}

function getTagList(data) {
    // 标签别表
    return axios({
        url: "/api/marketing-conf/v3/tag/list",
        method: "get",
        params:data
    });    
}
function updateTagStatus(data) {
    // 更改标签状态
    return axios({
        url: "/api/marketing-conf/v3/tag/status",
        method: "post",
        data
    });  
}
    // 推荐标签列表

function getLabelList(data) {
    // 标签别表
    return axios({
        url: "/api/commodities/v3/label/labelList",
        method: "get",
        params:data
    });    
}

function addTag(data) {
    // 添加标签
    return axios({
        url: "/api/marketing-conf/v3/tag/create",
        method: "post",
        data
    });    
}
function updateTag(data) {
    // 更新标签
    return axios({
        url: "/api/marketing-conf/v3/tag/update",
        method: "post",
        data
    });    
}

export default {
    getBulleinList,
    addBullein,
    updateBullein,
    updateBulleinStatus,
    getTagList,
    updateTagStatus,
    getLabelList,
    addTag,
    updateTag,
};
