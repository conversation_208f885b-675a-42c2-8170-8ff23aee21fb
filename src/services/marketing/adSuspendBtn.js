import axios from "axios";

//获取悬浮按钮广告列表
function getAdSuspendBtnList(data) {
    return axios({
        url: "/api/marketing-conf/v3/hoverButton/list",
        method: "get",
        params: data
    });
}

//编辑悬浮按钮
function editAdSuspendBtn(data) {
    return axios({
        url: "/api/marketing-conf/v3/hoverButton/update",
        method: "post",
        data
    });
}
//添加
function addAdSuspendBtn(data) {
    return axios({
        url: "/api/marketing-conf/v3/hoverButton/create",
        method: "post",
        data
    });
}

export default {
    getAdSuspendBtnList,
    editAdSuspendBtn,
    addAdSuspendBtn
};
