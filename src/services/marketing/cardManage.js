import axios from "axios";

//获取卡片管理列表
function getCardManageList(data) {
    return axios({
        url: "/api/marketing-conf/v3/card/list",
        method: "get",
        params: data,
    });
}

//更改状态
function updateStatus(data) {
    return axios({
        url: "/api/marketing-conf/v3/card/status",
        method: "post",
        data,
    });
}

//添加卡片
function addCardManage(data) {
    return axios({
        url: "/api/marketing-conf/v3/card/create",
        method: "post",
        data,
    });
}

//编辑卡片
function editCardManage(data) {
    return axios({
        url: "/api/marketing-conf/v3/card/update",
        method: "post",
        data,
    });
}

//获取直播管理或商品管理列表
function getCardGoodsList(data) {
    return axios({
        url: "/api/marketing-conf/v3/cardgoodslive/list",
        method: "post",
        data,
    });
}

//添加直播或者商品管理
function addCardGoodsLive(data) {
    return axios({
        url: "/api/marketing-conf/v3/cardgoodslive/create",
        method: "post",
        data,
    });
}

//商品期数是否存在
function isExistRelationid(data) {
    return axios({
        url: "/api/commodities/v3/periods/list",
        method: "get",
        params: data,
    });
}

//直播ID是否存在
function isExistLiveId(data) {
    return axios({
        url: "/api/live/v3/live/getById",
        method: "get",
        params: data,
    });
}
//删除直播或商品管理列表数据
function deleteCardGoodsLive(data) {
    return axios({
        url: "/api/marketing-conf/v3/cardgoodslive/delete",
        method: "post",
        data,
    });
}

//更新排序
function updateSort(data) {
    return axios({
        url: "/api/marketing-conf/v3/cardgoodslive/update",
        method: "post",
        data,
    });
}

//删除卡片
function deleteCard(data) {
    return axios({
        url: "/api/marketing-conf/v3/card/delect",
        method: "post",
        data,
    });
}

//筛选列表
function getFilterList(data) {
    return axios({
        url: "/api/marketing-conf/v3/cardgoodslive/getfilter",
        method: "get",
        params: data,
    });
}

export default {
    getCardManageList,
    updateStatus,
    addCardManage,
    editCardManage,
    getCardGoodsList,
    addCardGoodsLive,
    isExistRelationid,
    isExistLiveId,
    deleteCardGoodsLive,
    updateSort,
    deleteCard,
    getFilterList,
};
