import axios from "axios";

function getNewcomersList(data) {
    return axios({
        url: "/api/commodities/v3/marketing/getNewcomerList",
        method: "get",
        params: data
    });
}
function addNewcomers(data) {
    return axios({
        url: "/api/commodities/v3/marketing/addNewcomer",
        method: "post",
        data
    });
}
function updateNewcomers(data) {
    return axios({
        url: "/api/commodities/v3/marketing/upNewcomer",
        method: "post",
        data
    });
}

// deleteNewcomers
function deleteNewcomers(data) {
    return axios({
        url: "/api/commodities/v3/marketing/delNewComer",
        method: "post",
        data
    });
}
export default {
    getNewcomersList,
    addNewcomers,
    updateNewcomers,
    deleteNewcomers
};
