import axios from "axios";

//获取banner广告列表
function getAdBannerList(data) {
    return axios({
        url: "/api/marketing-conf/v3/ad/list",
        method: "get",
        params: data
    });
}

//添加banner广告
function adBanner(data) {
    return axios({
        url: "/api/marketing-conf/v3/ad/create",
        method: "post",
        data
    });
}

//编辑banner广告
function editBanner(data) {
    return axios({
        url: "/api/marketing-conf/v3/ad/update",
        method: "post",
        data
    });
}

//更改状态
function updateStatus(data) {
    return axios({
        url: "/api/marketing-conf/v3/ad/status",
        method: "post",
        data
    });
}

export default {
    getAdBannerList,
    adBanner,
    editBanner,
    updateStatus
};
