import axios from "axios";

function getSmsList(data) {
    // 获取短信列表
    return axios({
        url: "/api/sms/v3/group/search",
        method: "post",
        data
    });
}
function sendSms(data) {
    // 发送短信
    return axios({
        url: "/api/sms/v3/group/create",
        method: "post",
        data
    });
}
function cancelSms(data) {
    // 取消发送
    return axios({
        url: "/api/sms/v3/group/cancel",
        method: "post",
        data
    });
}
function searchWineUserList(data) {
    // 查询用户
    return axios({
        url: "/api/user/v3/user/sendMassSms",
        method: "get",
        params: data
    });
}
function updateOss(data) {
    // 上传oss后回显号码
    return axios({
        url: "/api/sms/v3/group/downloadExcel",
        method: "post",
        data
    });
}
function getRegionalList(data) {
    // 省列表
    return axios({
        url: "/api/user/v3/regional/list",
        method: "get",
        params: data
    });
}
function getSendSmsForUser(data) {
    // 指定用户发送短信的搜索
    return axios({
        url: "/api/user/v3/user/getSendSmsPhone",
        method: "get",
        params: data
    });
}
function getProductListForKeywords(data) {
    // 关键字获取产品关键字
    return axios({
        url: "/api/wiki/v3/producttype/keywordquery",
        method: "get",
        params: data
    });
}

function importCouponUser(data) {
    // 优惠券发放导入用户
    return axios({
        url: "/api/coupon/v3/coupon/import",
        method: "post",
        data
    });
}
export default {
    importCouponUser,
    getSendSmsForUser,
    getSmsList,
    getRegionalList,
    getProductListForKeywords,
    updateOss,
    searchWineUserList,
    sendSms,
    cancelSms
};
