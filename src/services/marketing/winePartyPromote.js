import axios from "axios";

//获取酒会推广列表
function getWinePartyPromoteList(data) {
    return axios({
        url: "/api/invite/v3/winepartyActivity/list",
        method: "get",
        params: data
    });
}

//创建酒会推广活动
function addWinePartyActive(data) {
    return axios({
        url: "/api/invite/v3/winepartyActivity/add",
        method: "post",
        data
    });
}
//获取酒会推广活动详情
function getDetail(data) {
    return axios({
        url: "/api/invite/v3/winepartyActivity/detail",
        method: "get",
        params: data
    });
}
//编辑酒会推广活动
function editWinePartyActive(data) {
    return axios({
        url: "/api/invite/v3/winepartyActivity/edit",
        method: "post",
        data
    });
}

//获取活动商品列表
function winepartyActivityGoods(data) {
    return axios({
        url: "/api/invite/v3/winepartyActivityGoods/list",
        method: "get",
        params: data
    });
}

//新增活动商品
function addWinePartyActiveGoods(data) {
    return axios({
        url: "/api/invite/v3/winepartyActivityGoods/add",
        method: "post",
        data
    });
}
//编辑活动商品
function editWinePartyActiveGoods(data) {
    return axios({
        url: "/api/invite/v3/WinepartyActivityGoods/edit",
        method: "post",
        data
    });
}
//删除
function delGoods(data) {
    return axios({
        url: "/api/invite/v3/winepartyActivityGoods/del",
        method: "post",
        data
    });
}
export default {
    getWinePartyPromoteList,
    addWinePartyActive,
    getDetail,
    editWinePartyActive,
    winepartyActivityGoods,
    addWinePartyActiveGoods,
    editWinePartyActiveGoods,
    delGoods
};
