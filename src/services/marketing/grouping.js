import axios from "axios";

function getGroupingList(data) {
    return axios({
        url: "/api/commodities/v3/marketing/getGroupList",
        method: "get",
        params: data
    });
}
function getGoodsByPeriod(data) {
    return axios({
        url: "/api/commodities/v3/periods/list",
        method: "get",
        params: data
    });
}
function getGoodsPackage(data) {
    return axios({
        url: "/api/commodities/v3/package/productList",
        method: "get",
        params: data
    });
}
function addGroup(data) {
    return axios({
        url: "/api/commodities/v3/marketing/addGroup",
        method: "post",
        data
    });
}
function updateGroup(data) {
    return axios({
        url: "/api/commodities/v3/marketing/upGroup",
        method: "post",
        data
    });
}

// deleteGrouping
function deleteGrouping(data) {
    return axios({
        url: "/api/commodities/v3/marketing/delGroup",
        method: "post",
        data
    });
}

export default {
    getGroupingList,
    getGoodsByPeriod,
    getGoodsPackage,
    addGroup,
    updateGroup,
    deleteGrouping
};
