import axios from "axios";

function getRepurchasestatisticsList(data) {
    // 再来一单
    return axios({
        url: "/api/orders/v3/repurchasestatistics/period",
        method: "get",
        params: data
    });
}
function getRepurchasestatisticsUserlist(data) {
    // 用户列表
    return axios({
        url: "/api/orders/v3/repurchasestatistics/needlist",
        method: "get",
        params: data
    });
}
function getCollectionlist(data) {
    return axios({
        url: "/api/orders/v3/repurchasestatistics/collection/list",
        method: "get",
        params: data
    });
}
// 
function getCollectionUserlist(data) {
    return axios({
        url: "/api/orders/v3/repurchasestatistics/collection/userlist",
        method: "get",
        params: data
    });
}
export default {
    getRepurchasestatisticsList,
    getRepurchasestatisticsUserlist,
    getCollectionlist,
    getCollectionUserlist
};
