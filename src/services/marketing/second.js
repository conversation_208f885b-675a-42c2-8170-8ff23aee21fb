import axios from "axios";

function getSecondtypeList(data) {
    // 获取目录
    return axios({
        url: "/api/activity/v3/second/list",
        method: "get",
        params: data
    });
}
// /activity/v3/second/update
function updateSecondtype(data) {
    return axios({
        url: "/api/activity/v3/second/update",
        method: "post",
        data
    });
}
// /activity/v3/second/status
function updateSecondtypeStatus(data) {
    return axios({
        url: "/api/activity/v3/second/status",
        method: "post",
        data
    });
}
// 商品列表 /activity/v3/second_goods/list
function getSecondGoodsList(data) {
    return axios({
        url: "/api/activity/v3/second_goods/list",
        method: "get",
        params: data
    });
}
function getGoodsById(data) {
    return axios({
        url: "/api/commodities/v3/second/detail",
        method: "get",
        params: data
    });
}
function updateGoods(data) {
    return axios({
        url: "/api/activity/v3/second_goods/update",
        method: "post",
        data
    });
}
function addGoods(data) {
    return axios({
        url: "/api/activity/v3/second_goods/create",
        method: "post",
        data
    });
}
export default {
    getSecondtypeList,
    updateSecondtype,
    updateSecondtypeStatus,
    getSecondGoodsList,
    getGoodsById,
    updateGoods,
    addGoods
};
