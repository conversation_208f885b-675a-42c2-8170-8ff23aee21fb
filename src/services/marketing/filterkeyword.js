import axios from "axios";

function getFilterList(data) {
    // 筛选列表
    return axios({
        url: "/api/wiki/v3/screen/index",
        method: "get",
        params: data
    });
}

function addSreen(data) {
    // 筛
    return axios({
        url: "/api/wiki/v3/screen/index",
        method: "post",
        data
    });
}
function updateScreen(data) {
    // 筛
    return axios({
        url: "/api/wiki/v3/screen/update",
        method: "post",
        data
    });
}
function switchScreen(data) {
    // 筛
    return axios({
        url: "/api/wiki/v3/screen/status",
        method: "post",
        data
    });
}
function countryList(data) {
    // 筛
    return axios({
        url: "/api/wiki/v3/country",
        method: "get",
        params: data
    });
}
function typeList(data) {
    // 筛
    return axios({
        url: "/api/wiki/v3/productcategory/type",
        method: "get",
        params: data
    });
}
function keywordList(data) {
    // 筛
    return axios({
        url: "/api/wiki/v3/product/keywords",
        method: "get",
        params: data
    });
}
function getRegionsList(params) {
    return axios({
        url: "/api/wiki/v3/regions",
        params
    });
}

export default {
    getFilterList,
    addSreen,
    countryList,
    typeList,
    keywordList,
    updateScreen,
    switchScreen,
    getRegionsList
};
