import axios from "axios";

function gethotkeywordList(data) {
    // 发送
    return axios({
        url: "/api/marketing-conf/v3/hotkeyword/list",
        method: "get",
        params: data
    });
}
function createhotkeyword(data) {
    // 发送
    return axios({
        url: "/api/marketing-conf/v3/hotkeyword/create",
        method: "post",
        data
    });
}

function updatehotkeyword(data) {
    // 发送
    return axios({
        url: "/api/marketing-conf/v3/hotkeyword/update",
        method: "post",
        data
    });
}
function updateHotkeywordStatus(data) {
    // 发送
    return axios({
        url: "/api/marketing-conf/v3/hotkeyword/status",
        method: "post",
        data
    });
}
function deleteHotkeyword(data) {
    // 发送
    return axios({
        url: "/api/marketing-conf/v3/hotkeyword/delete",
        method: "post",
        data
    });
}
export default {
    gethotkeywordList,
    createhotkeyword,
    updatehotkeyword,
    updateHotkeywordStatus,
    deleteHotkeyword
};
