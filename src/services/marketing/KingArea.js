import axios from "axios";

//获取频道
function getChannel(data) {
    return axios({
        url: "/api/marketing-conf/v3/common/channel",
        method: "get",
        params: data
    });
}

//获取金刚区列表
function getKingAreaList(data) {
    return axios({
        url: "/api/marketing-conf/v3/label/list",
        method: "get",
        params: data
    });
}

//新增金刚区
function getAddKingArea(data) {
    return axios({
        url: "/api/marketing-conf/v3/label/create",
        method: "post",
        data
    });
}
//编辑金刚区
function editKingArea(data) {
    return axios({
        url: "/api/marketing-conf/v3/label/update",
        method: "post",
        data
    });
}

//更改状态
function updateStatus(data) {
    return axios({
        url: "/api/marketing-conf/v3/label/status",
        method: "post",
        data
    });
}

function KingDetailPath(data) {
    return axios({
        url: "/api/marketing-conf/v3/label/detail",
        method: "get",
        params: data
    });
}

//获取栏目管理列表
function getColumnManagerList(data) {
    return axios({
        url: "/api/marketing-conf/v3/column/list",
        method: "get",
        params: data
    });

}

//产区
function getRegionList(params) {
    return axios({
        url: "/api/wiki/v3/regions",
        method: "get",
        params,
    });
}
//酒庄
function getWineryList(params) {
    return axios({
        url: "/api/wiki/v3/winery",
        method: "get",
        params,
    });
}
//国家
function getCountryList(params) {
    return axios({
        url: "/api/wiki/v3/country",
        method: "get",
        params,
    });
}
//葡萄品种
function getGrapeList(params) {
    return axios({
        url: "/api/wiki/v3/grape",
        method: "get",
        params,
    });
}
// 推荐标签列表
const labelList = (params) => {
    return axios({
        url: "/api/commodities/v3/label/labelList",
        method: "get",
        params,
    });
};
function getProductCategory(params) {
    return axios({
        url: "/api/wiki/v3/productcategory/type",
        method: "get",
        params,
    });
}
//新增栏目
function addColumnRequest(data) {
    return axios({
        url: "/api/marketing-conf/v3/column/create",
        method: "post",
        data
    });
}

//编辑栏目
function updateColumnRequest(data) {
    return axios({
        url: "/api/marketing-conf/v3/column/update",
        method: "post",
        data
    });
}


function getColumnDetail(params) {
    return axios({
        url: "/api/marketing-conf/v3/column/detail",
        method: "get",
        params,
    });
}

//更改栏目状态
function updateCloumnStatus(data) {
    return axios({
        url: "/api/marketing-conf/v3/column/status",
        method: "post",
        data
    });
}

//更改栏目状态
function deleteColumn(data) {
    return axios({
        url: "/api/marketing-conf/v3/column/delect",
        method: "post",
        data
    });
}
function getGoodsMangerList(data) {
    return axios({
        url: "/api/marketing-conf/v3/columngoods/list",
        method: "post",
        data,
    });
}
function addGoods(data) {
    return axios({
        url: "/api/marketing-conf/v3/columngoods/create",
        method: "post",
        data
    });
}
function updateGoods(data) {
    return axios({
        url: "/api/marketing-conf/v3/columngoods/update",
        method: "post",
        data
    });
}
function deleteGoods(data) {
    return axios({
        url: "/api/marketing-conf/v3/columngoods/delete",
        method: "post",
        data
    });
}
//栏目筛选列表
function getcolumnFilterList(data) {
    return axios({
        url: "/api/marketing-conf/v3/columngoods/getfilter",
        method: "get",
        params: data
    });
}
export default {
    getChannel,
    getKingAreaList,
    getAddKingArea,
    editKingArea,
    updateStatus,
    KingDetailPath,
    getColumnManagerList,
    getRegionList,
    getWineryList,
    getCountryList,
    labelList,
    getProductCategory,
    addColumnRequest,
    getColumnDetail,
    updateColumnRequest,
    updateCloumnStatus,
    deleteColumn,
    getGoodsMangerList,
    addGoods,
    updateGoods,
    deleteGoods,
    getGrapeList,
    getcolumnFilterList
};
