import axios from "axios";

function getTemplateList(data) {
    // 获取模版列表
    return axios({
        url: "/api/apppush/v3/push/record",
        method: "get",
        params: { ...data }
    });
}
function addTemplate(data) {
    // 新增模版
    return axios({
        url: "/api/apppush/v3/push/create",
        method: "post",
        data
    });
}
function getExhibitionList(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/exhibitions",
        method: "get",
        params: data
    });
}
function addExhibitionList(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/create/exhibition",
        method: "post",
        data
    });
}
function editExhibitionList(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/update/exhibition",
        method: "post",
        data
    });
}
function deleteExhibition(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/delete/exhibition",
        method: "get",
        params: data
    });
}
function getBoothsList(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/booths",
        method: "get",
        params: data
    });
}
function deleteBooths(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/delete/booths",
        method: "get",
        params: data
    });
}

function addBooths(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/create/booths",
        method: "post",
        data
    });
}
function updateBooths(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/update/booths",
        method: "post",
        data
    });
}
function getBrandsList(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/brands",
        method: "get",
        params: data
    });
}
function getProductsList(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/products",
        method: "get",
        params: data
    });
}

function deleteBrands(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/delete/brands",
        method: "get",
        params: data
    });
}
function addBrands(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/create/brands",
        method: "post",
        data
    });
}
function updateBrands(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/update/brands",
        method: "get",
        params: data
    });
}
function deleteProducts(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/delete/products",
        method: "get",
        params: data
    });
}
function addGoods(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/create/products",
        method: "post",
        data
    });
}
function updateGoods(data) {
    return axios({
        url: "/api/marketing-conf/v3/exhib/update/products",
        method: "post",
        data
    });
}

export default {
    getBoothsList,
    deleteProducts,
    updateGoods,
    deleteBrands,
    addGoods,
    deleteBooths,
    getExhibitionList,
    getTemplateList,
    addTemplate,
    deleteExhibition,
    editExhibitionList,
    addExhibitionList,
    addBooths,
    updateBooths,
    getBrandsList,
    addBrands,
    updateBrands,
    getProductsList
};
