import axios from "axios";

function getCouponList(data) {
    // 获取优惠券列表
    return axios({
        url: "/api/coupon/v3/coupon/lists",
        method: "get",
        params: data
    });
}

function updateStatus(data) {
    // 修改优惠券状态
    return axios({
        url: "/api/coupon/v3/coupon/status",
        method: "post",
        data
    });
}
function getConfig() {
    // 获取优惠券Nacos Config
    return axios({
        url: "/api/coupon/v3/coupon/types",
        method: "get"
    });
}

function addCoupon(data) {
    // 添加优惠券
    return axios({
        url: "/api/coupon/v3/coupon/add",
        method: "post",
        data
    });
}
function pushCoupon(data) {
    // 发放优惠券
    return axios({
        url: "/api/coupon/v3/coupon/launchaudit",
        method: "post",
        data
    });
}
function couponissueList(data) {
    // 已发放优惠券列表
    return axios({
        url: "/api/coupon/v3/couponissue/lists",
        method: "get",
        params: data
    });
}
function getActivitiesList(data) {
    // 获取活动列表
    return axios({
        url: "/api/activity/v3/activity/list",
        method: "get",
        params: data
    });
}
//优惠券作废
function couponInvalid(data) {
    return axios({
        url: "/api/coupon/v3/couponissue/applyDelete",
        method: "post",
        data
    });
}
function exportCouponIssueList(data) {
    return axios({
        url: "/api/coupon/v3/couponissue/couponIssueExport",
        method: "post",
        data
    });
}

function getProductsByPage(data) {
    return axios({
        url: "/api/commodities/v3/evSort/getProductsByPage",
        method: "get",
        params: data
    });
}
function getPeriodsList(data) {
    return axios({
        url: "/api/commodities/v3/periods/periodsList",
        method: "get",
        params: data
    });
}
function getPeriodsSortList(data) {
    return axios({
        url: "/api/commodities/v3/evSort/sortList",
        method: "get",
        params: data
    });
}

function getSortConfig() {
    return axios({
        url: "/api/commodities/v3/evSort/config",
        method: "get"
    });
}

function getUserLimit() {
    return axios({
        url: "/api/commodities/v3/evSort/getUserLimit",
        method: "get"
    });
}

function addSort(data) {
    return axios({
        url: "/api/commodities/v3/evSort/addSort",
        method: "post",
        data
    });
}

function cancelSort(data) {
    return axios({
        url: "/api/commodities/v3/evSort/cancelSort",
        method: "post",
        data
    });
}
function saveWeightConfig(data) {
    return axios({
        url: "/api/commodities/v3/evSort/updateWeightConfig",
        method: "post",
        data
    });
}



function weightConfig() {
    return axios({
        url: "/api/commodities/v3/evSort/weightConfig",
        method: "get",
    });
}


export default {
    weightConfig,
    getCouponList,
    getActivitiesList,
    addCoupon,
    getProductsByPage,
    saveWeightConfig,
    couponissueList,
    updateStatus,
    addSort,
    cancelSort,
    pushCoupon,
    getConfig,
    couponInvalid,
    exportCouponIssueList,
    getPeriodsSortList,
    getSortConfig,
    getPeriodsList,
    getUserLimit
};
