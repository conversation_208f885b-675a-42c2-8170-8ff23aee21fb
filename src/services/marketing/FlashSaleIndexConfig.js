import axios from "axios";

//获取闪购首页配置列表
function getFlashSaleIndexList() {
    return axios({
        url: "/api/fullgift/v3/FlashSaleClass/list",
        method: "get"
    });
}

//添加闪购首页配置列表
function addFlashSaleIndex(data) {
    return axios({
        url: "/api/fullgift/v3/FlashSaleClass/add",
        method: "post",
        data
    });
}

//获取活动列表
function getActivitiesList(data) {
    return axios({
        url: "/api/activity/v3/activity/list",
        method: "get",
        params: data
    });
}

//编辑闪购分类
function updateFlashSale(data) {
    return axios({
        url: "/api/fullgift/v3/FlashSaleClass/update",
        method: "post",
        data
    });
}

//更新状态
function updateFlashSaleStatus(data) {
    return axios({
        url: "/api/fullgift/v3/FlashSaleClass/updateStatus",
        method: "post",
        data
    });
}

//更新排序
function updateFlashSaleSort(data) {
    return axios({
        url: "/api/fullgift/v3/FlashSaleClass/updateSort",
        method: "post",
        data
    });
}

export default {
    getFlashSaleIndexList,
    addFlashSaleIndex,
    getActivitiesList,
    updateFlashSale,
    updateFlashSaleStatus,
    updateFlashSaleSort
};
