import axios from "axios";

//获取券包列表
function getCouponPackageList(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/lists",
        method: "get",
        params: data
    });
}

//添加优惠券包
function addCouponPackage(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/add",
        method: "post",
        data
    });
}

//获取优惠券列表
function getCouponList(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/couponlists",
        method: "get",
        params: data
    });
}

// 券包编辑
function editCouponPackage(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/edit",
        method: "post",
        data
    });
}

//优惠券包详情
function couponPackageDetail(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/details",
        method: "get",
        params: data
    });
}

//删除券包里的优惠券
function delCoupon(data) {
    return axios({
        url: "/api/coupon/v3/couponPackageDetails/del",
        method: "post",
        data
    });
}

//优惠券包详情修改
function editCouponPackageDetail(data) {
    return axios({
        url: "/api/coupon/v3/couponPackageDetails/edit",
        method: "post",
        data
    });
}

//优惠券包状态更改
function updateCouponPackageStatus(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/changstatus",
        method: "post",
        data
    });
}

//发放优惠券包
function grantpackage(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/launchAudit",
        method: "post",
        data
    });
}

//优惠券包导入发放
function couponpackageImport(data) {
    return axios({
        url: "/api/coupon/v3/couponpackage/import",
        method: "post",
        data
    });
}
//企业微信上传临时素材
function uploadWeiXinTemporary(data) {
    return axios({
        url: "/api/orders/v3/offline/uploadWeiXinTemporary",
        method: "post",
        data
    });
}
export default {
    getCouponPackageList,
    addCouponPackage,
    getCouponList,
    editCouponPackage,
    delCoupon,
    couponPackageDetail,
    editCouponPackageDetail,
    updateCouponPackageStatus,
    grantpackage,
    couponpackageImport,
    uploadWeiXinTemporary
};
