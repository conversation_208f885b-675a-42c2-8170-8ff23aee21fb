import axios from "axios";

//获取拉新列表
function getPullNewList(data) {
    return axios({
        url: "/api/invite/v3/inviteActivity/list",
        method: "get",
        params: data
    });
}
//创建拉新活动
function addPullNew(data) {
    return axios({
        url: "/api/invite/v3/inviteActivity/add",
        method: "post",
        data
    });
}
//拉新详情
function pullNewDetail(data) {
    return axios({
        url: "/api/invite/v3/inviteActivity/detail",
        method: "get",
        params: data
    });
}
//编辑拉新活动
function editPullNew(data) {
    return axios({
        url: "/api/invite/v3/inviteActivity/edit",
        method: "post",
        data
    });
}
//获取拉新活动商品
function getPullNewGoodsList(data) {
    return axios({
        url: "/api/invite/v3/inviteActivityGoods/list",
        method: "get",
        params: data
    });
}

//新增拉新活动商品
function addPullNewGoods(data) {
    return axios({
        url: "/api/invite/v3/inviteActivityGoods/add",
        method: "post",
        data
    });
}
//编辑拉新活动商品
function editPullNewGoods(data) {
    return axios({
        url: "/api/invite/v3/inviteActivityGoods/edit",
        method: "post",
        data
    });
}
//删除拉新活动商品
function delPullNewGoods(data) {
    return axios({
        url: "/api/invite/v3/inviteActivityGoods/del",
        method: "post",
        data
    });
}

export default {
    getPullNewList,
    addPullNew,
    pullNewDetail,
    editPullNew,
    getPullNewGoodsList,
    addPullNewGoods,
    editPullNewGoods,
    delPullNewGoods
};
