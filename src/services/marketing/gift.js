import axios from "axios";

function getGiftList(data) {
    // 获取满减活动列表
    return axios({
        url: "/api/marketing/v3/reduction/list",
        method: "get",
        params: data
    });
}

function addGift(data) {
    // 新增满减活动
    return axios({
        url: "/api/marketing/v3/reduction/create",
        method: "post",
        data
    });
}

function changeStatus(data) {
    // 修改状态
    return axios({
        url: "/api/marketing/v3/reduction/onEnable",
        method: "post",
        data
    });
}

function getFullGiftList(data) {
    // 获取满减活动列表
    return axios({
        url: "/api/fullgift/v3/activity/list",
        method: "get",
        params: data
    });
}

function fullGiftAdd(data, type) {
    // 修改状态
    return axios({
        url:
            type == 1
                ? "/api/fullgift/v3/activity/created"
                : "/api/fullgift/v3/activity/update",
        method: "post",
        data
    });
}

function fullGiftUpdateStatus(data) {
    // 修改状态
    return axios({
        url: "/api/fullgift/v3/activity/updateStatus",
        method: "post",
        data
    });
}

function sendGift(data) {
    // 发送
    return axios({
        url: "/api/fullgift/v3/activity/sendGift",
        method: "post",
        data
    });
}
function getFullGiftDetails(data) {
    // 发送
    return axios({
        url: "/api/fullgift/v3/activity/getPackageList",
        method: "get",
        params: data
    });
}

export default {
    getGiftList,
    changeStatus,
    addGift,
    getFullGiftDetails,
    getFullGiftList,
    fullGiftAdd,
    sendGift,
    fullGiftUpdateStatus
};
