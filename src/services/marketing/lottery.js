import axios from "axios";

//获取抽奖配置
function getLotteryConfig() {
    return axios({
        url: "/api/marketing-conf/v3/game/getRotaryDraw",
        method: "get"
    });
}
//更新抽奖配置
function updateLotteryConfig(data) {
    return axios({
        url: "/api/marketing-conf/v3/game/updateRotaryDraw",
        method: "post",
        data
    });
}
//兔头 更新抽奖配置
function updateLotteryConfigRabbit(data) {
    return axios({
        url: "/api/marketing-conf/v3/game/updateRabbitRotaryDraw",
        method: "post",
        data
    });
}
//兔头 抽奖配置列表
function getLotteryConfigRabbit(data) {
    return axios({
        url: "/api/marketing-conf/v3/game/getRabbitRotaryDraw",
        method: "get",
        params: data
    });
}

function getNewUserLotteryConfig() {
    return axios({
        url: "/api/marketing-conf/v3/game/getNewRabbitRotaryDraw",
        method: "get"
    });
}

function updateNewUserLotteryConfig(data) {
    return axios({
        url: "/api/marketing-conf/v3/game/updateNewRotaryDraw",
        method: "post",
        data
    });
}

export default {
    updateLotteryConfig,
    updateLotteryConfigRabbit,
    getLotteryConfig,
    getLotteryConfigRabbit,
    getNewUserLotteryConfig,
    updateNewUserLotteryConfig
};
