export default {
    name: "TableListMixin",
    data: () => ({
        enableMixinDefaultLoad: true,
        tableData: [],
        loading: false,
        pageSize: 10,
        query: {
            page: 1,
            limit: 10,
        },
        pageInfo: {
            currentPage: 1,
            pageSizes: [10, 30, 50, 100, 200],
            pageSize: 10,
            total: 0,
        },
    }),
    methods: {
        load() {},
        search() {
            this.pageInfo.currentPage = 1;
            this.query.page = 1;
            this.load();
        },
        handleSizeChange(value) {
            console.log("handleSizeChange", value);
            this.pageInfo.currentPage = 1;
            this.query.page = 1;
            this.query.limit = value;
            this.load();
        },
        handleCurrentChange(value) {
            console.log("handleCurrentChange", value);
            this.pageInfo.currentPage = value;
            this.query.page = value;
            this.load();
        },
    },
    created() {
        this.query.limit = this.pageSize;
        this.pageInfo.pageSizes = [
            ...new Set([this.pageSize, ...this.pageInfo.pageSizes]),
        ].sort((prev, next) => prev - next);
        this.pageInfo.pageSize = this.pageSize;
        const load = this.load;
        function loadFun() {
            this.loading = true;
            return load(this.query)
                .then((res) => {
                    console.log("res", res);
                    if (res && res.total) {
                        this.pageInfo.total = res.total;
                    } else {
                        this.pageInfo.total = 0;
                    }
                    console.log("pageInfo", this.pageInfo);
                })
                .catch((err) => {
                    throw err;
                })
                .finally(() => {
                    this.loading = false;
                });
        }
        this.load = loadFun.bind(this);
        if (this.enableMixinDefaultLoad) {
            this.load();
        }
    },
};
