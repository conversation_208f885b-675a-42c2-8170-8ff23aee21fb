<template>
    <div>
        <div class="king_container">
            <el-form
                style="width:55%"
                :model="form"
                :rules="formRules"
                ref="ruleForm"
            >
                <el-form-item
                    label="频道"
                    :label-width="formLabelWidth"
                    prop="channel"
                >
                    <el-radio-group
                        :value="form.channel"
                        @input="changeChannel"
                    >
                        <el-radio label="0">首页</el-radio>
                        <!-- <el-radio  label="1">闪购</el-radio> -->
                        <!-- <el-radio  label="2">秒发</el-radio> -->
                        <el-radio label="5">个人中心</el-radio>
                        <el-radio label="1">闪购</el-radio>
                        <el-radio label="8">跨境</el-radio>
                        <el-radio label="7">烈酒</el-radio>
                        <el-radio label="9">尾货</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item
                    label="标签名称"
                    :label-width="formLabelWidth"
                    prop="label_name"
                >
                    <el-input
                        size="mini"
                        v-model="form.label_name"
                        autocomplete="off"
                        placeholder="请输入标签名称"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="区域"
                    prop="page_area"
                    :label-width="formLabelWidth"
                >
                    <el-select
                        v-model="form.page_area"
                        placeholder="请选择区域"
                        size="mini"
                    >
                        <el-option
                            v-for="item in pageAreaOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="图标"
                    :label-width="formLabelWidth"
                    prop="icon"
                >
                    <vos-oss
                        v-if="form.channel == 5"
                        key="1"
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                       
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                    <vos-oss
                        v-else
                        key="2"
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                        :limitWhList="[84, 84]"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
                <el-form-item
                    label="徽章"
                    :label-width="formLabelWidth"
                    prop="badge"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="badge_map"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
                <el-form-item
                    label="排序"
                    :label-width="formLabelWidth"
                    prop="sort"
                >
                    <el-input-number
                        size="mini"
                        v-model="form.sort"
                        controls-position="right"
                        @change="handleChange"
                        :min="0"
                    ></el-input-number>
                </el-form-item>
                <PathConfig
                    :path_id="form.path_id"
                    ref="pathConfig"
                ></PathConfig>
                <el-form-item> </el-form-item>
            </el-form>
            <div style="width:300px;">
                <img
                    v-if="form.channel == '0'"
                    style="width:100%"
                    :src="oss_url + '/vinehoo/vos/marketing/kingkong_index.png'"
                />
                <img
                    v-if="form.channel == '5'"
                    style="width:100%"
                    :src="
                        oss_url + '/vinehoo/vos/marketing/kingkong_presonal.png'
                    "
                />
                <img
                    v-if="form.channel == '1' || form.channel == '7' || form.channel == '8' || form.channel == '9'"
                    style="width:100%"
                    src="https://images.vinehoo.com/vinehoo/vos/marketing/kingkong_cross.png"
                />
            </div>
        </div>
        <div style="margin-top:50px;margin-left:40%">
            <el-button @click="closeDiog">取 消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm')"
                >确 定</el-button
            >
        </div>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import PathConfig from "../../components/pathConfig/pathConfig.vue";
export default {
    components: {
        VosOss,
        PathConfig
    },
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的数字"));
            } else {
                callback();
            }
        };
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
        };
        return {
            dialogFormVisible: false,
            icon_map: [],
            badge_map: [],
            dir: "vinehoo/vos/marketing/",
            form: {
                channel: "0",
                client: ["0", "1", "2", "3"],
                label_name: "",
                path_id: " ",
                icon: "",
                badge: "",
                sort: 1,
                param: [],
                page_area: ""
            },
            formRules: {
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur"
                    }
                ],
                client: [
                    {
                        required: true,
                        message: "请选择客户端",
                        trigger: "blur"
                    }
                ],
                label_name: [
                    {
                        required: true,
                        message: "请输入标签名称",
                        trigger: "blur"
                    }
                ],
                path_id: [
                    {
                        required: true,
                        message: "请选择路径",
                        trigger: "blur"
                    }
                ],
                icon: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ],
                sort: [
                    {
                        required: true,
                        validator: weightValidator,
                        trigger: "blur"
                    }
                ],
                page_area: [
                    {
                        required: true,
                        message: "请选择区域",
                        trigger: "change"
                    }
                ]
            },
            formLabelWidth: "150px",
            oss_url: ""
        };
    },
    computed: {
        pageAreaOptions() {
             if (this.form.channel == "5") {
                return [
                    {
                        label: "区域一",
                        value: 1
                    },
                    {
                        label: "区域二",
                        value: 2
                    },
                    {
                        label: "区域三",
                        value: 3
                    }
                    // {
                    //     label: "区域四",
                    //     value: 4
                    // }
                ];
            } else  {
                return [
                    {
                        label: "区域一",
                        value: 1
                    }
                ];
            } 
        }
    },
    mounted() {
        this.oss_url =
            process.env.NODE_ENV == "development"
                ? "https://images.wineyun.com"
                : "https://images.vinehoo.com";
    },
    methods: {
        changeChannel(value) {
            this.form.page_area = "";
            console.log("--------000");
            console.log(value);
            if(value==5) {
                this.icon_map = [];
            }
            this.form.channel = value;
        },
        //关闭弹窗
        closeDiog() {
            this.$emit("close");
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    this.form.icon = this.icon_map.join(","); //图片转为字符串
                    this.form.badge = this.badge_map.join(","); //图片转为字符串
                    let {
                        client,
                        arr,
                        pathNum1
                    } = this.$refs.pathConfig.getAddData(); //接受路径配置传过来的参数
                    this.form.path_id = pathNum1;
                    this.form.client = [];
                    this.form.client = client.join(",");
                    this.form.params = arr;
                    console.log("表单", this.form);
                    this.$request.KingArea.getAddKingArea(this.form).then(
                        res => {
                            console.log("返回的值", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("添加成功");
                                this.$emit("close");
                                this.$emit("getKingAreaList");
                            }
                        }
                    );
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        }
    }
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
/deep/ .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
/deep/ .el-form-item {
    margin-bottom: 10px;
}
/deep/ .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
.king_container {
    display: flex;
}
</style>
