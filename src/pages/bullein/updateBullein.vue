<template>
    <div>
        <el-form
            :model="updateBulleinForm"
            ref="updateBulleinForm"
            :rules="updateBulleinRules"
            label-width="120px"
            :inline="false"
            size="normal"
        >
            <el-form-item label="标题" prop="title">
                <el-input
                    v-model="updateBulleinForm.title"
                    style="width: 80%"
                ></el-input>
            </el-form-item>
            <el-form-item label="副标题">
                <el-input
                    v-model="updateBulleinForm.subtitle"
                    style="width: 80%"
                ></el-input>
            </el-form-item>
            <el-form-item label="状态">
                <el-radio-group v-model="updateBulleinForm.status">
                    <el-radio :label="1">
                        开启
                    </el-radio>
                    <el-radio :label="0">
                        禁用
                    </el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="快报内容" prop="context">
                <Tinymce
                    ref="editor"
                    v-model.trim="updateBulleinForm.context"
                    :height="300"
                />
            </el-form-item>
            <el-form-item label="上架时间" prop="start_time">
                <el-date-picker
                    v-model="updateBulleinForm.start_time"
                    type="datetime"
                    placeholder="请选择上架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="下架时间" prop="end_time">
                <el-date-picker
                    v-model="updateBulleinForm.end_time"
                    type="datetime"
                    placeholder="请选择下架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import Tinymce from "@/components/Tinymce";
export default {
    name: "Vue2MarketingUpdatebullein",
    components: {
        Tinymce
    },
    data() {
        return {
            updateBulleinForm: {
                end_time: "",
                start_time: "",
                title: "",
                context: "",
                status: 1,
                subtitle: ""
            },
            updateBulleinRules: {
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                context: [
                    {
                        required: true,
                        message: "请输入快报内容",
                        trigger: "blur"
                    }
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择时间",
                        trigger: "blur"
                    }
                ],
                start_time: [
                    {
                        required: true,
                        message: "请选择时间",
                        trigger: "blur"
                    }
                ]
            },
            isEdit: false
        };
    },

    mounted() {},

    methods: {
        editRow(row) {
            this.updateBulleinForm.title = row.title;
            this.updateBulleinForm.context = row.context;
            this.updateBulleinForm.start_time = row.start_time;
            this.updateBulleinForm.end_time = row.end_time;
            this.updateBulleinForm.subtitle = row.subtitle;
            this.updateBulleinForm.status = row.status;
            this.updateBulleinForm.id = row.id;
            this.isEdit = true;
            this.$refs.editor.setContent(row.context);
        },
        updateBullein() {
            this.$refs.updateBulleinForm.validate(valid => {
                if (valid) {
                    console.log(this.updateBulleinForm);
                    let isEditName = "";
                    if (this.isEdit) {
                        isEditName = "updateBullein";
                    } else {
                        isEditName = "addBullein";
                    }
                    this.$request.bullein[isEditName](
                        this.updateBulleinForm
                    ).then(result => {
                        if (result.data.error_code == 0) {
                            this.$emit("closeBulleinDialog");
                        }
                    });
                } else {
                    return false;
                }
            });
        }
    },
    beforeDestroy() {
        this.isEdit = false;
    }
};
</script>
