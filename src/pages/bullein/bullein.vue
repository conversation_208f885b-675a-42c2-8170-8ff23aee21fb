<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-button type="primary" size="mini" @click="addBullein"
                >新增快报</el-button
            >
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="bulleinListData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="快报ID" prop="id" width="80">
                </el-table-column>
                <el-table-column
                    label="标题"
                    width="200"
                    prop="title"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    label="副标题"
                    prop="subtitle"
                    width="200"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    label="快报内容"
                    show-overflow-tooltip
                    prop="context"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column label="时间" prop="start_time" width="220">
                    <template slot-scope="{ row }">
                        <div>
                            上架时间：
                            {{ row.start_time }}
                        </div>
                        <div>
                            下架时间：
                            {{ row.end_time }}
                        </div>
                        <div>
                            创建时间：
                            {{ row.created_at }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作人"
                    prop="operator_name"
                    width="100"
                >
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="110">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="editBullein(scope.row)"
                            >编辑
                        </el-button>
                        <el-button
                            type="text"
                            size="mini"
                            @click="updateBulleinStatus(scope.row)"
                            >{{ scope.row.status !== 1 ? "启用" : "禁用" }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryBulleinData.limit"
                :current-page="queryBulleinData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <div>
            <el-dialog
                title="快报内容配置"
                :visible.sync="updateBulleinVisible"
                width="1000px"
                :close-on-click-modal="false"
            >
                <updateBullein
                    @closeBulleinDialog="closeBulleinDialog"
                    v-if="updateBulleinVisible"
                    ref="updatebullein"
                ></updateBullein>
                <span slot="footer" style="display:flex;justify-content:center">
                    <el-button @click="updateBulleinVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateBullein"
                        >确定</el-button
                    >
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import updateBullein from "./updateBullein.vue";
export default {
    components: { updateBullein },
    name: "Vue2MarketingBullein",

    data() {
        return {
            queryBulleinData: {
                limit: 10,
                page: 1
            },
            bulleinListData: [],
            updateBulleinVisible: false,
            total: 0
        };
    },

    mounted() {
        this.getBulleinList();
    },

    methods: {
        addBullein() {
            this.updateBulleinVisible = true;
        },
        editBullein(row) {
            this.updateBulleinVisible = true;
            this.$nextTick(() => {
                this.$refs.updatebullein.editRow(row);
            });
        },
        closeBulleinDialog() {
            this.updateBulleinVisible = false;
            this.getBulleinList();
            this.$message({
                type: "success",
                message: "操作成功"
            });
        },
        getBulleinList() {
            this.$request.bullein
                .getBulleinList(this.queryBulleinData)
                .then(res => {
                    this.bulleinListData = res.data.data.list;
                    this.total = res.data.data.total;
                });
        },
        updateBulleinStatus(row) {
            let upStatus = JSON.parse(JSON.stringify(row));
            let status = upStatus.status == 0 ? 1 : 0;
            this.$request.bullein
                .updateBulleinStatus({
                    id: upStatus.id,
                    status
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.getBulleinList();
                        this.$message({
                            type: "success",
                            message: "操作成功"
                        });
                    }
                });
        },
        comfirmUpdateBullein() {
            this.$nextTick(() => {
                this.$refs.updatebullein.updateBullein();
            });
        },
        handleSizeChange(limit) {
            this.queryBulleinData.limit = limit;
            this.queryBulleinData.page = 1;
            this.getBulleinList();
        },
        handleCurrentChange(page) {
            this.queryBulleinData.page = page;
            this.getBulleinList();
        }
    }
};
</script>
