<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item
                label="标题"
                :label-width="formLabelWidth"
                prop="title"
            >
                <el-input
                    size="mini"
                    v-model="form.title"
                    placeholder="请输入标题"
                    style="width:400px"
                ></el-input>
            </el-form-item>

            <el-form-item
                label="图片"
                :label-width="formLabelWidth"
                prop="image"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                    :limitWhList="[1080, 1610]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <!-- <el-form-item
                label="排序"
                :label-width="formLabelWidth"
                prop="sort"
            >
                <el-input-number
                    size="mini"
                    v-model="form.sort"
                    controls-position="right"
                    @change="handleChange"
                    :min="0"
                ></el-input-number>
            </el-form-item> -->
            <PathConfig
                :path_id="form.path_id"
                :isEdit="isEdit"
                :rowData="rowData"
                ref="pathConfig"
            ></PathConfig>
            <el-form-item
                label="上架时间"
                :label-width="formLabelWidth"
                prop="start_time"
            >
                <el-date-picker
                    v-model="form.start_time"
                    type="datetime"
                    placeholder="请选择上架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="下架时间"
                :label-width="formLabelWidth"
                prop="end_time"
            >
                <el-date-picker
                    v-model="form.end_time"
                    type="datetime"
                    placeholder="请选择下架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import PathConfig from "../../components/pathConfig/pathConfig.vue";
export default {
    components: {
        VosOss,
        PathConfig
    },
    props: ["rowData", "isEdit"],
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的排序"));
            } else {
                callback();
            }
        };
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
        };
        return {
            dialogFormVisible: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            client: [],
            form: {
                client: ["0", "1", "2", "3"],
                title: "",
                path_id: "",
                image: "",
                // sort: 1,
                start_time: "",
                end_time: ""
            },
            pathOptions: [],
            formRules: {
                start_time: [
                    {
                        required: true,
                        message: "请选择模式",
                        trigger: "blur"
                    }
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择模式",
                        trigger: "blur"
                    }
                ],
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur"
                    }
                ],
                client: [
                    {
                        required: true,
                        message: "请选择客户端",
                        trigger: "blur"
                    }
                ],
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                path_id: [
                    {
                        required: true,
                        message: "请选择路径",
                        trigger: "blur"
                    }
                ],
                image: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ]
                // sort: [
                //     {
                //         required: true,
                //         validator: weightValidator,
                //         trigger: "blur"
                //     }
                // ]
            },
            formLabelWidth: "150px"
        };
    },
    beforeMount() {
        // this.form = this.rowData;
        // console.log("传过来的form", this.form);
        // this.icon_map = this.form.image.split(",");
        // // this.channel = this.form.channel.map(item => {
        // //     return String(item.id);
        // // });
        // this.form.path_id = this.rowData.client_path.id;
    },
    mounted() {
        setTimeout(() => {
            this.form = this.rowData;
            console.log("编辑form", this.form);
            this.icon_map = this.form.image.split(",");
            this.channel = this.form.channel.map(item => {
                return String(item.id);
            });
            this.form.path_id = this.rowData.path;
        }, 600);
    },
    methods: {
        closeDiog() {
            this.$emit("closeViewDialogStatus");
            this.$emit("getOpenTailList");
        },
        //改变排序
        handleChange(value) {
            // console.log("改变后的排序", value);
        },
        //表单提交，在父组件调用
        submitForm() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    let {
                        EditClientArr,
                        EditParamArr,
                        pathNum2
                    } = this.$refs.pathConfig.getEditData();
                    this.form.path_id = pathNum2;
                    let data = {
                        title: this.form.title,
                        image: this.icon_map.join(","),
                        path: pathNum2,
                        sort: 0,
                        type: 2,
                        client: EditClientArr,
                        // channel: this.channel,
                        params: EditParamArr,
                        id: this.form.id,
                        start_time: this.form.start_time,
                        end_time: this.form.end_time
                    };
                    console.log("表单", data);

                    this.$request.adBanner.editBanner(data).then(res => {
                        console.log("返回的值", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("编辑成功");
                            this.$emit("closeViewDialogStatus");
                            this.$emit("getOpenTailList");
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
