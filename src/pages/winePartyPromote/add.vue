<template>
    <div>
        <el-form
            :model="form"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            size="mini"
            class="demo-ruleForm"
        >
            <el-form-item label="活动名称" prop="active_name">
                <el-input
                    v-model="form.active_name"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item label="活动时间" prop="time">
                <el-date-picker
                    style="width:400px"
                    v-model="form.time"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="活动主题色" prop="theme_color">
                <el-color-picker
                    v-model="form.theme_color"
                    color-format="hex"
                ></el-color-picker>
            </el-form-item>
            <el-form-item label="图片" prop="img">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>

            <el-form-item
                style="display: flex;justify-content: center;margin-left:-130px"
            >
                <el-button @click="close()">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        const checkName = (rule, value, callback) => {
            let reg = /^[^\s]+[\s]*.*$/;
            if (!reg.test(value)) {
                callback(new Error("请输入活动名称"));
            } else if (value == "") {
                callback(new Error("请输入活动名称"));
            } else {
                callback();
            }
        };
        return {
            dir: "vinehoo/vos/marketing/",
            icon_map: [],
            couponOptions: [],
            form: {
                active_name: "",
                time: "",
                theme_color: "#D20A2B"
            },
            rules: {
                // active_name: [
                //     {
                //         validator: checkName,
                //         trigger: "blur"
                //     }
                // ],
                // time: [
                //     {
                //         required: true,
                //         message: "请选择活动时间",
                //         trigger: "blur"
                //     }
                // ],
                theme_color: [
                    {
                        required: true,
                        message: "请选择活动背景色",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    methods: {
        close() {
            this.$emit("close");
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = {
                        active_name: this.form.active_name,
                        effected_time: this.form.time[0],
                        invalidate_time: this.form.time[1],
                        theme_color: this.form.theme_color,
                        wine_party_img: this.icon_map.join(",")
                    };
                    console.log("参数", data);
                    this.$request.winePartyPromote
                        .addWinePartyActive(data)
                        .then(res => {
                            console.log("添加结果", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("添加成功");
                                this.close();
                            }
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
