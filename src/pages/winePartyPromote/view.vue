<template>
    <div>
        <el-form
            :model="form"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            size="mini"
            class="demo-ruleForm"
        >
            <el-form-item label="活动名称" prop="active_name">
                <el-input
                    v-model="form.active_name"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item label="活动时间" prop="time">
                <el-date-picker
                    style="width:400px"
                    v-model="form.time"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="活动主题色" prop="theme_color">
                <el-color-picker
                    v-model="form.theme_color"
                    color-format="hex"
                ></el-color-picker>
            </el-form-item>
            <el-form-item label="图片" prop="img">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>

            <el-form-item
                style="display: flex;justify-content: center;margin-left:-130px"
            >
                <el-button @click="close()">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    props: ["rowData"],
    data() {
        return {
            dir: "vinehoo/vos/marketing/",
            icon_map: [],
            couponOptions: [],
            form: {
                active_name: "",
                time: "",
                theme_color: "#D20A2B"
            },
            rules: {
                // active_name: [
                //     {
                //         required: true,
                //         message: "请输入活动名称",
                //         trigger: "blur"
                //     }
                // ],
                // time: [
                //     {
                //         required: true,
                //         message: "请选择活动时间",
                //         trigger: "blur"
                //     }
                // ],
                theme_color: [
                    {
                        required: true,
                        message: "请选择活动背景色",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    mounted() {
        this.getDetail();
    },
    methods: {
        close() {
            this.$emit("closeViewDialogStatus");
        },
        async getDetail() {
            let res = await this.$request.winePartyPromote.getDetail({
                id: this.rowData.id
            });
            console.log("详情", res);
            if (res.data.error_code == 0) {
                this.form.active_name = res.data.data.active_name;
                if (
                    res.data.data.effected_time &&
                    res.data.data.invalidate_time
                ) {
                    this.form.time = [
                        new Date(res.data.data.effected_time),
                        new Date(res.data.data.invalidate_time)
                    ];
                } else {
                    this.form.time = "";
                }

                this.form.theme_color = res.data.data.theme_color;

                this.icon_map = res.data.data.wine_party_img
                    ? res.data.data.wine_party_img.split(",")
                    : [];
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    // console.log("111111111", this.form.time);
                    let data = {
                        id: this.rowData.id,
                        active_name: this.form.active_name,
                        effected_time: this.form.time ? this.form.time[0] : "",
                        invalidate_time: this.form.time
                            ? this.form.time[1]
                            : "",
                        theme_color: this.form.theme_color,
                        wine_party_img: this.icon_map.join(",")
                    };
                    console.log("参数", data);
                    this.$request.winePartyPromote
                        .editWinePartyActive(data)
                        .then(res => {
                            console.log("编辑结果", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("更新成功");
                                this.close();
                            }
                        });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
