<template>
    <el-form ref="form" :model="datas" label-width="100px" :rules="rules">
        <el-card class="box-card" shadow="hover" style="margin: 0 0 10px">
            <el-form-item label="活动名称" prop="name">
                <el-col :span="12">
                    <el-input
                        v-model="datas.name"
                        placeholder="请输入活动名称"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="活动时间" prop="times">
                <el-col :span="12">
                    <el-date-picker
                        v-model="datas.times"
                        type="datetimerange"
                        :default-time="['00:00:00', '23:59:59']"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        align="right"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    >
                    </el-date-picker>
                </el-col>
            </el-form-item>
            <el-form-item label="活动备注" prop="reduce">
                <el-col :span="12">
                    <el-input
                        v-model="datas.remark"
                        placeholder="请输入活动备注"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="活动对象" prop="activity_object">
                <el-select v-model="datas.activity_object" clearable>
                    <el-option label="酒云" :value="1" />
                    <el-option label="第三方平台" :value="2" />
                </el-select>
            </el-form-item>

            <el-form-item label="活动范围" prop="activity_range">
                <el-col :span="20">
                    <el-radio-group
                        v-model="datas.activity_range"
                        @change="handelChange"
                    >
                        <el-radio
                            v-for="(item, key) in activity_rangeOptions"
                            :key="key"
                            :label="item.label"
                        >
                            {{ item.value }}
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-form-item>
            <el-form-item label="赠送规则">
                <el-col :span="20">
                    <el-radio-group v-model="datas.andsel_type">
                        <el-radio
                            v-for="(item, key) in andsel_typeOptions"
                            :key="key"
                            :label="item.value"
                        >
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-form-item>
            <el-form-item
                label="关联活动ID"
                prop="goods_aid"
                v-if="datas.activity_range == 2"
            >
                <el-col :span="12">
                    <el-select
                        v-model="datas.goods_aid"
                        placeholder="请输入关联活动"
                        multiple
                    >
                        <el-option
                            v-for="item in activitieList"
                            :key="item.id"
                            :label="item.activity_name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-col>
            </el-form-item>
            <div
                class="rulllabel"
                v-show="
                    datas.activity_range == 1 ||
                        datas.activity_range == 2 ||
                        datas.activity_range == 3
                "
            >
                优惠内容
            </div>
            <div
                class="discount_content_wrap"
                v-show="
                    datas.activity_range == 1 ||
                        datas.activity_range == 2 ||
                        datas.activity_range == 3
                "
            >
                <div
                    v-for="(v, i) in datas.rule"
                    :key="i"
                    style="padding: 10px;"
                >
                    <div
                        style="display: flex;justify-content: space-between;width: 680px;"
                    >
                        <div
                            style="font-weight: bold;font-size: 16px;margin-bottom:10px;"
                        >
                            优惠{{ i + 1 }}
                        </div>
                        <div>
                            <el-button
                                type="danger"
                                @click="del(1, i)"
                                v-if="datas.rule.length > 1"
                                icon="el-icon-delete"
                                size="mini"
                                slot="reference"
                                >删除</el-button
                            >
                        </div>
                    </div>
                    <div
                        style="display: flex;line-height: 50px;margin-left: 17px;"
                        v-if="
                            datas.activity_range == 2 ||
                                datas.activity_range == 1
                        "
                    >
                        <el-form-item
                            label-width="93"
                            style="display: flex;"
                            label="单笔订单满"
                            :prop="'rule.' + i + '.money'"
                            :rules="rules.money"
                        >
                            <el-input
                                v-model="v.money"
                                style="width: 150px;padding-top: 5px;"
                                placeholder="请输入金额"
                            />
                        </el-form-item>
                        元
                        <div style="display: flex;margin-left: 30px;">
                            合并订单
                            <el-radio-group
                                size="mini"
                                v-model="v.merge_order"
                                style="padding-top: 17px;margin-left: 5px;"
                            >
                                <el-radio
                                    v-for="(item, key) in is_enabledOptions"
                                    :key="key"
                                    :label="item.label"
                                >
                                    {{ item.value }}
                                </el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div
                        style="display: flex;margin-top: 20px;margin-left: 14px;line-height: 50px;"
                        v-if="datas.activity_range == 3"
                    >
                        <label style="width: 88px;text-align: right;">
                            商品期数</label
                        >
                        <el-input
                            size="mini"
                            v-model="v.goods_id"
                            style="width: 150px;margin: 0 5px;"
                            oninput="value=value.replace(/[^\d]/g,'')"
                            placeholder="请输入商品ID"
                        />
                        <label
                            style="margin-left: 39px;width: 66px;text-align: right;"
                        >
                            商品数量</label
                        >
                        <el-input
                            v-model="v.goods_nums"
                            size="mini"
                            style="width: 150px;margin: 0 5px;"
                            oninput="value=value.replace(/[^\d]/g,'')"
                            placeholder="请输入商品数量"
                        />
                        <div style="display: flex;margin-left: 10px;">
                            合并订单
                            <el-radio-group
                                v-model="v.merge_order"
                                style="padding-top: 17px;margin-left: 25px;"
                            >
                                <el-radio
                                    v-for="(item, key) in is_enabledOptions"
                                    :key="key"
                                    :label="item.label"
                                >
                                    {{ item.value }}
                                </el-radio>
                            </el-radio-group>
                        </div>
                    </div>

                    <div
                        style="margin-left: 14px;line-height: 50px;"
                        v-for="(k, j) in v.giveaway_info"
                        :key="j"
                    >
                        <div>
                            赠送商品期数
                            <el-input
                                v-model="k.goods_id"
                                @blur="getFullGiftDetails(k.goods_id, i, j)"
                                class="full_inp w-mini"
                                size="mini"
                                @keyup.enter.native="
                                    getFullGiftDetails(k.goods_id, i, j)
                                "
                                oninput="value=value.replace(/[^\d]/g,'')"
                                placeholder="请输入赠送商品期数"
                            />
                            <div>
                                <label v-show="k.name">
                                    商品名称:{{ k.name }}</label
                                >
                                <div>
                                    <span
                                        v-if="
                                            !k.goodsPackageList ||
                                                k.goodsPackageList.length == 0
                                        "
                                    >
                                        套餐：{{ k.package_name }}
                                    </span>
                                    <el-radio-group
                                        @change="packageChange(i, j)"
                                        v-model="k.package_id"
                                        size="mini"
                                        class="package"
                                    >
                                        <el-radio-button
                                            v-for="item in k.goodsPackageList"
                                            :key="item.id"
                                            :label="item.id"
                                            >{{
                                                item.package_name
                                            }}</el-radio-button
                                        >
                                    </el-radio-group>
                                </div>
                            </div>
                            <!-- <el-input
                                v-model="k.name"
                                class="full_inp"
                                disabled
                                placeholder="请输入商品名称"
                            /> -->
                            <!--  实物简码
              <el-input v-model="k.short_code" class="full_inp" placeholder="请输入商品条码" /> -->
                            <!--   商品条码
              <el-input v-model="k.goods_code" class="full_inp" placeholder="请输入商品条码" /> -->
                        </div>
                        <div style="display: flex;">
                            <!--  京东编码
              <el-input v-model="k.jd_eclp_code" class="full_inp" placeholder="请输入京东编码" /> -->
                            <!-- 仓库编码
              <el-input v-model="k.warehouse_id" class="full_inp" placeholder="请输入仓库编码" /> -->
                            <!-- 商品名称
              <el-input v-model="k.name" class="full_inp" placeholder="请输入商品名称" /> -->
                        </div>

                        <div style="display: flex;margin-top: 10px;">
                            <label style="width: 84px;text-align: right;">
                                数量</label
                            >
                            <el-input
                                v-model="k.nums"
                                size="mini"
                                class="full_inp"
                                oninput="value=value.replace(/[^\d]/g,'')"
                                placeholder="请输入数量"
                            />

                            <label
                                style="margin-left: 39px;width: 55px;text-align: right;"
                            >
                                限量</label
                            >
                            <el-input
                                size="mini"
                                v-model="k.limited"
                                class="full_inp"
                                oninput="value=value.replace(/[^\d]/g,'')"
                                placeholder="请输入限量"
                            />

                            <!-- <el-button
                                type="primary"
                                @click="adds(2, i)"
                                v-if="j == 0"
                                icon="el-icon-circle-plus-outline"
                                style="height: 36px;margin-top: 7px;margin-left: 5px;"
                                >添加</el-button
                            > -->
                            <el-button
                                type="danger"
                                @click="del(2, i, j)"
                                v-if="v.giveaway_info.length > 1 && j > 0"
                                icon="el-icon-delete"
                                style="height: 36px;margin-top: 7px;"
                                size="mini"
                                slot="reference"
                                >删除</el-button
                            >
                        </div>
                    </div>
                </div>
                <div class="FullAddDiscount" @click="adds(1)">
                    添加优惠
                </div>
            </div>
        </el-card>
    </el-form>
</template>
<script>
export default {
    data() {
        return {
            // 赠送类型：1单个规则每个ID仅限一次，2优惠赠送次数不限制，3同一活动每个ID仅限参加一次
            andsel_typeOptions: [
                {
                    label: "同一优惠每个ID仅限参加一次",
                    value: 1
                },
                {
                    label: "赠送次数不限制",
                    value: 2
                },
                {
                    label: "每个ID仅限一次",
                    value: 3
                }
            ],
            datas: {
                andsel_type: 1,
                id: "",
                name: "",
                times: [],
                remark: "",
                aid: "",
                activity_object: "",

                activity_range: "",
                merge_order: 0,
                rule: [
                    {
                        money: "",
                        merge_order: 0,
                        giveaway_info: [
                            {
                                goodsPackageList: [],
                                name: "",
                                nums: "",
                                limited: "",
                                goods_code: "",
                                jd_eclp_code: "",
                                store_code: ""
                            }
                        ]
                    }
                ]
            },
            is_enabledOptions: [
                {
                    label: 1,
                    value: "是"
                },
                {
                    label: 0,
                    value: "否"
                }
            ],
            activity_rangeOptions: [
                {
                    label: 1,
                    value: "酒云全场商品（跨境除外）"
                },
                {
                    label: 2,
                    value: "酒云指定活动"
                },
                {
                    label: 3,
                    value: "酒云指定商品"
                }
            ],
            rules: {
                name: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur"
                    }
                ],
                times: [
                    {
                        required: true,
                        message: "请选择活动时间",
                        trigger: "change"
                    }
                ],
                activity_range: [
                    {
                        required: true,
                        message: "请选择活动范围",
                        trigger: "change"
                    }
                ],
                activity_object: [
                    {
                        required: true,
                        message: "请选择活动对象",
                        trigger: "change"
                    }
                ],
                goods_aid: [
                    {
                        required: true,
                        message: "请输入关联活动ID",
                        trigger: "blur"
                    }
                ],
                money: [
                    {
                        required: true,
                        message: "请输入单笔订单金额",
                        trigger: "blur"
                    },
                    {
                        pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
                        message: "请输入正确的单笔订单"
                    }
                ]
            },
            ac_typeOptions: [
                {
                    value: 1,
                    label: "双11"
                },
                {
                    value: 2,
                    label: "重庆美院"
                },
                {
                    value: 4,
                    label: "运通黑金"
                },
                {
                    value: 5,
                    label: "双12"
                },
                {
                    value: 6,
                    label: "仲量行"
                },
                {
                    value: 7,
                    label: "年货必囤"
                },
                {
                    value: 8,
                    label: "送礼首选"
                },
                {
                    value: 9,
                    label: "拉新列表0116"
                },
                {
                    value: 10,
                    label: "春节不打烊"
                },
                {
                    value: 11,
                    label: "新人必抢"
                },
                {
                    value: 12,
                    label: "雷司令"
                },
                {
                    value: 13,
                    label: "春日酒语"
                },
                {
                    value: 14,
                    label: "拉曼恰(秒发)"
                },
                {
                    value: 15,
                    label: "拉曼恰(闪购)"
                },
                {
                    value: 16,
                    label: "甜渣节（0-199）"
                },
                {
                    value: 17,
                    label: "甜渣节（199-399）"
                },
                {
                    value: 18,
                    label: "甜渣节（399+）"
                },
                {
                    value: 19,
                    label: "甜渣节福利"
                },
                {
                    value: 20,
                    label: "夏不为利"
                },
                {
                    value: 21,
                    label: "618活动"
                },
                {
                    value: 22,
                    label: "自然酒"
                },
                {
                    value: 23,
                    label: "大放暑价"
                },
                {
                    value: 24,
                    label: "无雷司令不夏天-闪购"
                },
                {
                    value: 25,
                    label: "无雷司令不夏天-秒发"
                },
                {
                    value: 26,
                    label: "七夕"
                },
                {
                    value: 27,
                    label: "兔子疯了"
                },
                {
                    value: 28,
                    label: "酒水节特别推荐"
                },
                {
                    value: 29,
                    label: "酒水节中秋礼遇"
                },
                {
                    value: 30,
                    label: "酒水节钜惠狂欢"
                },
                {
                    value: 31,
                    label: "壹元购茅台"
                }
            ],
            activitieList: []
        };
    },
    mounted() {
        this.getActivity();
        // if(this.activity_id){
        //     this.getActivityDetail();
        // }
    },
    methods: {
        packageChange(i, c) {
            console.log(this.datas.rule[i].giveaway_info[c]);
            let id = this.datas.rule[i].giveaway_info[c].package_id;
            // let package = ;
            this.datas.rule[i].giveaway_info[c][
                "package_name"
            ] = this.datas.rule[i].giveaway_info[c].goodsPackageList.find(
                i => i.id == id
            ).package_name;
        },
        getActivity() {
            this.$request.activities
                .getActivitiesList({
                    page: 1,
                    limit: 10000
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.activitieList = result.data.data.list;
                        this.total = result.data.data.total;
                    }
                });
        },
        handelChange() {
            if (this.datas.activity_range == 2) {
                this.datas.goods_aid = "";
            }
        },
        async getFullGiftDetails(period, p_index, c_index) {
            // console.log(p_item, p_index, c_index);
            console.log(this.datas.rule[p_index].giveaway_info[c_index]);
            this.datas.rule[p_index].giveaway_info[
                c_index
            ].goodsPackageList = [];
            this.datas.rule[p_index].giveaway_info[c_index].package_id = "";
            this.datas.rule[p_index].giveaway_info[c_index].name = "";
            const data = {
                period
            };
            const res = await this.$request.gift.getFullGiftDetails(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                this.datas.rule[p_index].giveaway_info[
                    c_index
                ].goodsPackageList = res.data.data.list;
                // if (
                //     this.datas.rule[p_index].giveaway_info[c_index]
                //         .goodsPackageList.length > 0
                // ) {
                //     this.datas.rule[p_index].giveaway_info[
                //         c_index
                //     ].package_id = this.datas.rule[p_index].giveaway_info[
                //         c_index
                //     ].goodsPackageList[0].id;
                // }

                this.datas.rule[p_index].giveaway_info[c_index].name =
                    res.data.data.goods_name;
            }
        },
        adds(type, index) {
            if (type == 1) {
                const data = {
                    money: "",
                    merge_order: 0,
                    giveaway_info: [
                        {
                            name: "",
                            nums: "",
                            limited: "",
                            store_code: "",
                            jd_eclp_code: "",
                            goods_code: ""
                        }
                    ]
                };
                this.datas.rule.push(data);
            } else if (type == 2) {
                const data = {
                    name: "",
                    nums: "",
                    limited: ""
                };
                this.datas.rule[index].giveaway_info.push(data);
            }
        },
        del(type, index, k) {
            if (type == 1) {
                this.datas.rule.splice(index, 1);
            } else if (type == 2) {
                this.datas.rule[index].giveaway_info.splice(k, 1);
            }
        },
        //提交
        async submits() {
            if (this.validateForm()) {
                this.datas.stime = this.datas.times[0];
                this.datas.etime = this.datas.times[1];

                this.datas.rule.map(item => {
                    item.is_capped = 0;
                    item.is_overlay = 0;
                    item.giveaway_info.map(child => {
                        child.type = 1;
                    });
                });
                let type = this.datas.id > 0 ? 0 : 1;
                this.$request.gift.fullGiftAdd(this.datas, type).then(res => {
                    if (res.data.error_code == 0) {
                        this.$emit("isShowDialog", 2);
                        // this.clearform();
                        this.$message({
                            type: "success",
                            message: "操作成功"
                        });
                    }
                });
            }
        },
        //数据回显
        async dataEcho(rows) {
            if (rows) {
                rows.times = [];
                rows.times[0] = rows.stime;
                rows.times[1] = rows.etime;
                this.datas = JSON.parse(JSON.stringify(rows));
            }
        },
        clearform() {
            this.datas.id = "";
            this.datas.name = "";
            this.datas.times = [];
            this.datas.remark = "";
            this.datas.activity_object = "";
            this.datas.activity_range = "";
            this.datas.rule = [
                {
                    money: "",
                    merge_order: 0,
                    giveaway_info: [
                        {
                            name: "",
                            nums: "",
                            limited: ""
                        }
                    ]
                }
            ];
            setTimeout(() => {
                console.log(111111);
                this.$refs["form"].clearValidate();
            }, 100);
        },
        // 表单验证
        validateForm() {
            let flag = null;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        }
    }
};
</script>
<style scoped lang="scss">
/deep/ .package {
    display: flex;
    margin-left: 10px;
    align-items: center;
}
.discount_content_wrap {
    border: 1px solid #ccc;

    margin-left: 31px;
    margin-top: 15px;
}

.rulllabel {
    text-align: right;
    vertical-align: middle;
    font-size: 14px;
    color: #606266;
    padding: 0 12px 0 0;
    box-sizing: border-box;
    width: 100px;
    font-weight: bold;
}

.FullAddDiscount {
    border-top: 1px solid #cccccc;
    text-align: center;
    padding: 12px 0;
    cursor: pointer;
}

.full_inp {
    width: 200px !important;
    margin: 0 10px;
}
</style>
