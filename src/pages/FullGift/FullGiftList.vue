<template>
    <div>
        <!-- <el-card shadow="hover"> -->
        <!-- 高级查询 -->
        <el-input
            v-model="query.name"
            @keyup.enter.native="search"
            class="m-r-10 w-large"
            size="mini"
            placeholder="请输入活动名称"
        />
        <el-select
            v-model="query.activity_object"
            clearable
            size="mini"
            class="m-r-10"
        >
            <el-option label="酒云" value="1" />
            <el-option label="第三方平台" value="2" />
        </el-select>
        <el-button type="success" @click="search" size="mini">查询</el-button>
        <el-button type="primary" @click="isShowDialog()" size="mini"
            >添加</el-button
        >
        <!-- </el-card> -->
        <el-card shadow="hover">
            <el-table
                border
                stripe
                size="mini"
                :data="DataList"
                fit
                highlight-current-row
                style="width: 100%"
            >
                <el-table-column
                    label="ID"
                    prop="id"
                    width="80px"
                    align="center"
                />
                <el-table-column
                    label="活动名称"
                    prop="name"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    label="活动生效时间"
                    prop="stime"
                    width="340"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.stime + " 至 " + row.etime }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="活动启用来源"
                    width="120px"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.activity_object == 1 ? "酒云" : "第三方平台" }}
                    </template>
                </el-table-column>
                <el-table-column label="是否启用" width="80px" align="center">
                    <template slot-scope="scope">
                        <el-switch
                            slot="reference"
                            v-model="scope.row.status"
                            @change="changeEnabled(scope.row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    fixed="right"
                    width="80px"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        <el-button type="text" size="mini" @click="edit(row)"
                            >编辑</el-button
                        >
                        <!-- <el-popconfirm confirmButtonText='确定' cancelButtonText='取消' icon="el-icon-question" title="你确定删除吗？"
              @onConfirm="del(row)">
              <el-button type="danger" :loading="row.deleteBtnLoading" icon="el-icon-delete" size="mini" slot="reference"
                v-action="'FullReduce_Del'">删除</el-button>
            </el-popconfirm> -->
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 分页 -->
        <div class="page-layout">
            <el-pagination
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="pageSize"
                :current-page="currentPage"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <el-dialog
            :visible.sync="dialogVisible"
            title="基本信息"
            custom-class="dialogwid"
            width="900px"
            top="50px"
        >
            <el-form label-width="80px">
                <FullGiftOp
                    v-if="dialogVisible"
                    ref="operation"
                    @isShowDialog="isShowDialog"
                ></FullGiftOp>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submits">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import FullGiftOp from "./FullGiftOp.vue";
export default {
    components: {
        FullGiftOp
    },
    name: "FullReduce",
    data() {
        return {
            currentPage: 1, // 当前页
            pageSize: 10, // 每页条数
            total: 0, // 总条数
            query: {
                activity_object: "",
                name: ""
            },
            dialogVisible: false,
            DataList: [],
            loading: false,
            is_enabledOption: [
                {
                    label: "开启",
                    value: 1
                },
                {
                    label: "关闭",
                    value: 0
                }
            ],
            goods_typeOption: [
                {
                    label: "闪购",
                    value: 0
                },
                {
                    label: "秒发",
                    value: 20
                }
            ]
        };
    },
    created() {
        this.getData();
    },
    methods: {
        async getData() {
            const params = {
                activity_object: this.query.activity_object,
                name: this.query.name,
                page: this.currentPage,
                limit: this.pageSize
            };

            this.$request.gift.getFullGiftList(params).then(res => {
                if (res.data.error_code == 0) {
                    res.data.data.list.map(item => {
                        item.status = item.status == 1 ? true : false;
                    });
                    this.total = res.data.data.total;
                    this.DataList = res.data.data.list;
                }
            });
        },
        async changeEnabled(row) {
            const params = {
                id: row.id,
                status: row.status ? 1 : 2
            };
            this.$request.gift.fullGiftUpdateStatus(params).then(res => {
                if (res.data.error_code == 0) {
                    this.getData();
                    this.$message.success("操作成功！");
                } else {
                    row.status = !row.status;
                }
            });
        },
        // 编辑
        edit(row) {
            this.isShowDialog(1);
            this.id = row.id;
            this.$nextTick(function() {
                this.$refs["operation"].dataEcho(row);
            });
        },
        isShowDialog(type) {
            this.dialogVisible = !this.dialogVisible;
            // this.$nextTick(function() {
            //     this.$refs["operation"].clearform();
            // });
            if (type == 2) {
                this.getData();
            }
        },
        //提交
        submits() {
            this.$refs["operation"].submits();
        },
        queryStatus() {
            this.currentPage = 1;
            this.getData();
        },
        // 高级查询
        search() {
            this.currentPage = 1;
            this.getData();
        },
        // 改变每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.getData();
        },
        // 改变当前页
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getData();
        },
        // 清除查询
        clearSelect() {
            this.alllistsearch = {
                goods: "", // 闪购名称
                goodsid: "", // 闪购ID
                kind: "" // 类别
            };
            this.currentPage = 1;
            this.pageSize = 10; // 每页条数
            this.getData();
        }
    }
};
</script>

<style lang="scss" scoped>
.demo-table-expand {
    font-size: 0;
}

.demo-table-expand label {
    width: 90px;
    color: #99a9bf;
}

.demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 100%;
}
.page-layout {
    text-align: center;
}
</style>
