<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            
            <el-select
                v-model="queryBulleinData.channel"
                placeholder="请选择频道"
                size="mini"
                clearable
            >
                <el-option
                    v-for="item in sell_type_options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
            <el-button type="primary" size="mini" style="margin-left: 20px;" @click="searchClick()"
            >查询</el-button
          >
            <el-button type="primary" size="mini" @click="addBullein"
                >添加标签</el-button
            >
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="bulleinListData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="标签名称" prop="label_name"  min-width="100">
                </el-table-column>
                <el-table-column
                    label="频道"
                    width="100"
                    prop="channelName"
                    show-overflow-tooltip
                   
                >
                </el-table-column>
                <el-table-column
                    label="排序"
                    prop="sort"
                    width="80"
                    show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                    label="状态"
                    show-overflow-tooltip
                    width="100"
                >
                <template slot-scope="scope">
                    {{ scope.row.status === 1 ? "启用" : "禁用" }}
                    </template>
                </el-table-column>
                
                <el-table-column label="操作" fixed="right" width="110">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="editBullein(scope.row)"
                            >编辑
                        </el-button>
                        <el-button
                            type="text"
                            size="mini"
                            @click="updateBulleinStatus(scope.row)"
                            >{{ scope.row.status !== 1 ? "启用" : "禁用" }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryBulleinData.limit"
                :current-page="queryBulleinData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <div>
            <el-dialog
                title="标签设置"
                :visible.sync="updateBulleinVisible"
                width="1000px"
                :close-on-click-modal="false"
            >
                <updateBullein
                    @closeBulleinDialog="closeBulleinDialog"
                    v-if="updateBulleinVisible"
                    ref="updatebullein"
                ></updateBullein>
                <span slot="footer" style="display:flex;justify-content:center">
                    <el-button @click="updateBulleinVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateBullein"
                        >确定</el-button
                    >
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import updateBullein from "./updateBullein.vue";
export default {
    components: { updateBullein },
    name: "Vue2MarketingBullein",

    data() {
        return {
            queryBulleinData: {
                limit: 10,
                page: 1,
                channel:"",
            },
            sell_type_options: [
               
                {
                    label: "闪购",
                    value: 1
                },
                {
                    label: "跨境",
                    value: 8
                },
                {
                    label: "烈酒",
                    value: 7
                },
                {
                    label: "尾货",
                    value: 9
                }
            ],
            bulleinListData: [],
            updateBulleinVisible: false,
            total: 0
        };
    },

    mounted() {
        this.getBulleinList();
    },

    methods: {
        searchClick(){
           this.queryBulleinData.page=1;
            this.getBulleinList();
        },

        addBullein() {
            this.updateBulleinVisible = true;
        },
        editBullein(row) {
            this.updateBulleinVisible = true;
            this.$nextTick(() => {
                this.$refs.updatebullein.editRow(row);
            });
        },
        closeBulleinDialog() {
            this.updateBulleinVisible = false;
            this.getBulleinList();
            this.$message({
                type: "success",
                message: "操作成功"
            });
        },
        getBulleinList() {
            this.$request.bullein
                .getTagList(this.queryBulleinData)
                .then(res => {
                    this.bulleinListData = res.data.data.list.map(item => ({ ...item, channelName: this.sell_type_options.find(option => option.value === item.channel).label }));
                    this.total = res.data.data.total;
                });
        },
        updateBulleinStatus(row) {
            let upStatus = JSON.parse(JSON.stringify(row));
            let status = upStatus.status == 0 ? 1 : 0;
            this.$request.bullein
                .updateTagStatus({
                    id: upStatus.id,
                    status
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.getBulleinList();
                        this.$message({
                            type: "success",
                            message: "操作成功"
                        });
                    }
                });
        },
        comfirmUpdateBullein() {
            this.$nextTick(() => {
                this.$refs.updatebullein.updateBullein();
            });
        },
        handleSizeChange(limit) {
            this.queryBulleinData.limit = limit;
            this.queryBulleinData.page = 1;
            this.getBulleinList();
        },
        handleCurrentChange(page) {
            this.queryBulleinData.page = page;
            this.getBulleinList();
        }
    }
};
</script>
