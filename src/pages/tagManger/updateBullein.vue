<template>
    <div>
        <el-form
            :model="updateBulleinForm"
            ref="updateBulleinForm"
            :rules="updateBulleinRules"
            label-width="120px"
            :inline="false"
            size="normal"
        >
            <el-form-item label="标签名称" prop="label_id">
                <el-select
                    class="m-r-10 "
                    v-model="updateBulleinForm.label_id"
                    filterable
                    size="mini"
                    placeholder="请选择"
                    clearable
                    @change="tagNameChange"
                >
                    <el-option
                        v-for="(item, index) in labelList"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="频道">
                <el-radio-group v-model="updateBulleinForm.channel">
                    <el-radio :label="1">
                        闪购
                    </el-radio>
                    <el-radio :label="8">
                        跨境
                    </el-radio>
                    <el-radio :label="7">
                        烈酒
                    </el-radio>
                    <el-radio :label="9">
                        尾货
                    </el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="排序值" prop="sort">
                <el-input-number
                    v-model="updateBulleinForm.sort"
                    label="描述文字"
                ></el-input-number>
            </el-form-item>
            <el-form-item label="状态">
                <el-radio-group v-model="updateBulleinForm.status">
                    <el-radio :label="1">
                        开启
                    </el-radio>
                    <el-radio :label="0">
                        禁用
                    </el-radio>
                </el-radio-group>
            </el-form-item>

        </el-form>
    </div>
</template>
<script>
import Tinymce from "@/components/Tinymce";
export default {
    name: "Vue2MarketingUpdatebullein",
    components: {
        Tinymce
    },
    data() {
        return {
            updateBulleinForm: {
                channel: 1,
                label_id: "",
                sort: 0,
                label_name: "",
                status: 1,
                
            },
            labelList:[],
            updateBulleinRules: {
                label_id: [
                    {
                        required: true,
                        message: "请选择标签",
                        trigger: "blur"
                    }
                ],
                
                sort: [
                    {
                        required: true,
                        message: "请输入排序值",
                        trigger: "blur"
                    }
                ],
               
            },
            isEdit: false
        };
    },

    mounted() {
        this.getLabelList();
    },

    methods: {
        tagNameChange(value){
           
            // console.log('ddd',row);
            this.updateBulleinForm.label_name = this.labelList.find(option => option.id === value).name;
        },
        editRow(row) {
            this.updateBulleinForm.id = row.id;
            this.updateBulleinForm.channel = row.channel;
            this.updateBulleinForm.label_id = row.label_id;
            this.updateBulleinForm.sort = row.sort;
            // this.updateBulleinForm.label_name = row.label_name;
            this.updateBulleinForm.status = row.status;
            this.isEdit = true;
        },
        getLabelList() {
            const params = {type:2,page:1,limit:1000};
            this.$request.bullein.getLabelList(params)
                .then(res => {
                    this.labelList = res.data.data.list;
                    
                });
        },
        updateBullein() {
            this.$refs.updateBulleinForm.validate(valid => {
                if (valid) {
                    this.updateBulleinForm.label_name = this.labelList.find(option => option.id === this.updateBulleinForm.label_id).name;
                    console.log(this.updateBulleinForm);
                    let isEditName = "";
                    if (this.isEdit) {
                        isEditName = "updateTag";
                    } else {
                        isEditName = "addTag";
                    }
                    this.$request.bullein[isEditName](
                        this.updateBulleinForm
                    ).then(result => {
                        if (result.data.error_code == 0) {
                            this.$emit("closeBulleinDialog");
                        }
                    });
                } else {
                    return false;
                }
            });
        }
    },
    beforeDestroy() {
        this.isEdit = false;
    }
};
</script>
