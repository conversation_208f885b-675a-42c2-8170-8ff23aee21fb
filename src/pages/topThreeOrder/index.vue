<template>
    <div>
        <el-card shadow="hover">
            <el-table
                :data="tableData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="阶段" prop="stage" width="100">
                    <template slot-scope="{ row }">
                        {{ StageText[row.stage] }}
                    </template>
                </el-table-column>

                <el-table-column label="赠送方式" prop="type" width="200">
                    <template slot-scope="{ row }">
                        {{
                            GiftOptions.find(item => item.value === row.type)[
                                "label"
                            ]
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="期数/优惠券ID"
                    prop="data_id"
                    width="200"
                >
                    <template slot-scope="{ row }">
                        <div>{{ row.data_id }}</div>
                        <div>{{ row.coupon_name }}</div>
                    </template>
                </el-table-column>
                <el-table-column
                    label="更新时间"
                    prop="update_time"
                    width="200"
                />
                <el-table-column label="操作人" prop="operator" width="200" />
                <el-table-column label="状态" prop="status" width="100">
                    <template slot-scope="{ row }">
                        <el-tag
                            :type="row.status === 1 ? `success` : `info`"
                            size="mini"
                            effect="dark"
                        >
                            {{ row.status === 1 ? "生效" : "失效" }}</el-tag
                        >
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="300">
                    <template slot-scope="{ row }">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleEdit(row)"
                        >
                            修改
                        </el-button>
                        <el-button
                            :type="row.status === 2 ? `success` : `info`"
                            size="mini"
                            @click="handleUpdateStatus(row)"
                        >
                            {{ row.status === 2 ? "生效" : "失效" }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <el-dialog
            title="修改优惠内容"
            :visible.sync="editVisible"
            :close-on-click-modal="false"
            width="40%"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                size="mini"
                label-width="120px"
            >
                <el-form-item label="阶段" prop="stage">
                    <el-input
                        disabled
                        v-model="StageText[ruleForm.stage]"
                    ></el-input>
                </el-form-item>
                <el-form-item label="赠送方式" prop="type">
                    <el-select
                        v-model="ruleForm.type"
                        placeholder="请选择活动区域"
                        width="100%"
                    >
                        <el-option
                            v-for="item in GiftOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="优惠券ID" prop="data_id">
                    <el-input
                        placeholder="请输入优惠券ID"
                        v-model.number="ruleForm.data_id"
                    ></el-input>
                </el-form-item>
                <el-form-item label="是否生效" prop="status">
                    <el-switch
                        v-model="ruleForm.status"
                        :active-value="1"
                        :inactive-value="2"
                        active-color="#13ce66"
                    ></el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button plane @click="editVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >确认</el-button
                    >
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>
<script>
import listMixin from "@/mixins/listMixin";
const StageText = Object.freeze({
    1: "第一单",
    2: "第二单",
    3: "第三单"
});
const GiftOptions = Object.freeze([{ label: "优惠券", value: 1 }]);
export default {
    mixins: [listMixin],
    data: () => ({
        StageText,
        GiftOptions,
        editVisible: false,
        ruleForm: {
            stage: "",
            type: "",
            data_id: 0,
            status: 2
        },
        rules: {
            stage: [
                {
                    required: true,
                    message: "请输入阶段",
                    trigger: "blur"
                }
            ],
            type: [
                {
                    required: true,
                    message: "请选择赠送方式",
                    trigger: "blur"
                }
            ],
            data_id: [
                {
                    required: true,
                    message: "请输入优惠券ID",
                    trigger: "blur"
                },
                { type: "number", message: "请输入数字", trigger: "blur" }
            ]
        }
    }),
    methods: {
        async load() {
            const res = await this.$request.topThreeOrder.list(this.query);
            if (res?.data?.error_code === 0) {
                this.tableData = res?.data?.data?.list || [];
            }
        },

        handleEdit(row) {
            this.ruleForm = { ...row };
            this.editVisible = true;
        },

        handleUpdateStatus(row) {
            const { id, type, data_id, status } = { ...row };
            // if (!type || !data_id)
            //     return this.$message.error("信息填充完整后方可切换状态~");
            const data = {
                id,
                status: status === 1 ? 2 : 1
            };
            this.$request.topThreeOrder.update(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功！");
                    this.load();
                }
            });
        },

        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    const { id, type, data_id, status } = this.ruleForm;
                    this.$request.topThreeOrder
                        .update({ id, type, data_id, status })
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功！");
                                this.load();
                                this.editVisible = false;
                            }
                        });
                }
            });
        }
    }
};
</script>
