<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <!-- <el-select
                    class="m-r-10"
                    v-model="form.sms_scene"
                    filterable
                    size="mini"
                    placeholder="短信场景"
                    clearable
                >
                    <el-option
                        v-for="item in sms_sceneOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value" -->
                <!-- </el-option> -->
                <!-- </el-select>  -->
                <el-select
                    class="w-mini m-r-10 "
                    v-model="form.sms_category"
                    filterable
                    size="mini"
                    placeholder="短信类型"
                    clearable
                >
                    <el-option
                        v-for="item in sms_categoryOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="w-mini m-r-10"
                    size="mini"
                    v-model="form.sms_status"
                    filterable
                    placeholder="发送状态"
                    clearable
                >
                    <el-option
                        v-for="item in sms_statusOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
                <el-input
                    class="w-mini m-r-10"
                    size="mini"
                    clearable
                    @keyup.enter.native="search"
                    v-model="form.sms_creator"
                    placeholder="操作人"
                ></el-input>
                <el-input
                    class="w-normal m-r-10"
                    size="mini"
                    @keyup.enter.native="search"
                    clearable
                    v-model="form.sms_content"
                    placeholder="短信内容"
                ></el-input>
                <el-button @click="reset" size="mini">重置</el-button>
                <div class="action-btn">
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button type="primary" size="mini" @click="opendialog()"
                        >发送短信</el-button
                    >
                </div>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column align="center" label="平台" min-width="90">
                        <template slot-scope="row">
                            {{ row.row.sms_platform | sms_platformFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sms_category"
                        align="center"
                        label="短信类型"
                        min-width="90"
                    >
                        <template slot-scope="row">
                            {{ row.row.sms_category | sms_categoryFormat }}
                        </template>
                    </el-table-column>
                    <!-- <el-table-column
                        label="短信场景"
                        min-width="100"
                        align="center"
                    >
                        <template slot-scope="row">
                            {{ row.row.sms_scene | sms_sceneFormat }}
                        </template>
                    </el-table-column> -->

                    <!-- <el-table-column
                        label="模版类型"
                        min-width="100"
                        align="center"
                    >
                        <template slot-scope="row">
                            {{ row.row.sms_template | sms_templateFormat }}
                        </template>
                    </el-table-column> -->

                    <el-table-column
                        prop="sms_content"
                        label="短信内容"
                        min-width="300"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="sms_creator"
                        align="center"
                        label="操作人"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="sms_count"
                        align="center"
                        label="接受人数"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="sms_status"
                        align="center"
                        label="发送状态"
                        min-width="100"
                    >
                        <template slot-scope="row">
                            {{ row.row.sms_status | sms_statusFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sms_schedule"
                        align="center"
                        label="发送时间"
                        min-width="240"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="120"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                v-if="row.row.sms_status === 2"
                                size="mini"
                                type="text"
                                @click="cancelSend(row.row)"
                                >取消发送</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>

        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- Send -->
        <el-dialog
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            title="短信发送"
            :visible.sync="dialogVisible"
            width="70%"
        >
            <Send
                @closeDialog="closeDialog"
                @submitForm="submitForm"
                v-if="dialogVisible"
            ></Send>
        </el-dialog>
    </div>
</template>
<script>
import Send from "./Send.vue";
export default {
    components: {
        Send
    },
    filters: {
        sms_statusFormat(val) {
            let status = Number(val);
            switch (status) {
                case 1:
                    return "审批中";
                case 2:
                    return "待发送";
                case 3:
                    return "审批拒绝";
                case 4:
                    return "已发送";
                case 5:
                    return "已取消";
                case 6:
                    return "发送失败";
                default:
                    return "-";
            }
        },
        sms_templateFormat(val) {
            let status = Number(val);
            switch (status) {
                case 1:
                    return "普通模版";
                case 2:
                    return "用户模版";
                default:
                    return "-";
            }
        },
        sms_platformFormat(val) {
            if (val == 1) {
                return "亿美";
            } else {
                return "腾域";
            }
        },
        sms_categoryFormat(val) {
            if (val == 1) {
                return "通知";
            } else if (val == 2) {
                return "营销";
            } else {
                return "-";
            }
        },
        sms_sceneFormat(val) {
            if (val == 1) {
                return "新品推荐";
            } else {
                return "-";
            }
        }
    },
    data() {
        return {
            dialogVisible: false,
            tableData: [],
            sms_sceneOptions: [
                {
                    value: 1,
                    label: "新品推荐"
                }
            ],
            sms_statusOptions: [
                {
                    label: "审批中",
                    value: 1
                },
                {
                    label: "待发送",
                    value: 2
                },
                {
                    label: "审批拒绝",
                    value: 3
                },
                {
                    label: "已发送",
                    value: 4
                },
                {
                    label: "取消发送",
                    value: 5
                }
            ],
            sms_categoryOptions: [
                {
                    label: "通知",
                    value: 1
                },
                {
                    label: "营销",
                    value: 2
                }
            ],
            form: {
                page: 1,
                sms_category: "",
                limit: 10,
                sms_creator: "",
                sms_content: "",
                sms_scene: "",
                sms_status: ""
            },
            total: 0
        };
    },
    methods: {
        async cancelSend(row) {
            console.log(row);
            let data = {
                message_id: row.id
            };
            let res = await this.$request.sms.cancelSms(data);
            if (res.data.error_code == 0) {
                this.getSmsList();
                this.$message.success("取消成功");
            }
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        submitForm() {
            this.dialogVisible = false;
            this.getSmsList();
        },
        getSmsList() {
            let data = {
                ...this.form
            };
            this.$request.sms.getSmsList(data).then(res => {
                if (res.data.error_code == 0) {
                    console.warn(res.data);
                    this.total = res.data.data.total;
                    this.tableData = res.data.data.list;
                }
            });
        },
        search() {
            this.form.page = 1;
            this.getSmsList();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.form.sms_category = "";
                this.form.sms_creator = "";
                this.form.sms_content = "";
                this.form.sms_scene = "";
                this.form.sms_status = "";
                this.getSmsList();
            });
        },
        opendialog() {
            this.dialogVisible = true;
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getSmsList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getSmsList();
        }
    },
    mounted() {
        this.getSmsList();
    }
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
