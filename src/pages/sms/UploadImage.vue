<template>
    <!--在此处添加渲染的内容-->
    <div>
        <!--        <el-upload class="upload-demo" ref="upload" drag :before-upload="beforeUpload" :on-success="handleSuccess"
            :http-request="handleHttpRequest" :headers="uploadHeaders" :limit="files" :disabled="disabled" multiple
            action="" :file-list="fileList">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">上传文件大小不能超过 1G</div>
        </el-upload> -->

        <el-upload
            action
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :http-request="handleHttpRequest"
            ref="upload"
        >
            <!-- accept="image/jpeg, image/png,image/gif" -->

            <!-- <div v-if="imgUrl" class="bigImg_wrap">
                <img :src="imgUrl" class="avatar" />
                <div class="bigImg_mask" v-if="type == 1">
                    <i
                        class="el-icon-zoom-in zoom_big"
                        @click.stop="handlePreview"
                    ></i>
                </div>
            </div> -->
            <slot></slot>
            <!-- <i v-else class="el-icon-plus avatar-uploader-icon" /> -->
        </el-upload>
        <el-dialog :visible.sync="imgVisible" append-to-body>
            <img width="100%" :src="imgUrl" alt="" />
        </el-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
import OSS from 'ali-oss';
//将渲染的内容导出
export default {
    props: {
        value: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            uploadHeaders: {
                authorization: '*'
            },
            type: 1,
            imgVisible: false,
            tempUrl: ""
        };
    },
    computed: {
        imgUrl() {
            return this.value;
        }
    },
    methods: {
        beforeUpload(file) {
            // const isJPG = file.type === "image/jpeg";
            // const isPNG = file.type === "image/png";
            // const isGIF = file.type === 'image/gif';
            const isLt1M = file.size / 1024 / 1024 < 1;
            // if (!(isJPG || isPNG || isGIF)) {
            //     this.$message.error("上传头像图片只能是 JPG/PNG 格式!");
            //     return false;
            // }
            if (!isLt1M) {
                this.$message.error("不能上传大于1M的图片");
                return false;
            }
            // (isJPG || isPNG || isGIF) &&
            return  isLt1M;
        },
        handleHttpRequest(option) { //上传OSS
             let oss = {
                region: "oss-cn-zhangjiakou.aliyuncs.com",
                accessKeyId: "LTAI5t88V4fhDjxmuuvmmV5r",
                accessKeySecret: "******************************",
                bucket: "vinehoo-test"
            };
            if (process.env.NODE_ENV == "staging") {
                oss.region = "oss-cn-zhangjiakou-internal.aliyuncs.com";
            }
            if (process.env.NODE_ENV == "production") {
                oss.region = "oss-accelerate.aliyuncs.com";
                oss.accessKeyId = "LTAI5tCTTF2TderhYBGjCHQ6";
                oss.accessKeySecret = "******************************";
                oss.bucket = "vinehoo";
            }
            try {
                let vm = this;
                vm.disabled = true;
                const client = new OSS(oss);
                const file = option.file;
                // console.log(file,file.name.split('.').pop(),5555555555)
                client.put("vinehoo/news/" + file.name, file).then(({
                    res
                }) => {
                    console.log(res);
                    if (res.statusCode === 200) {
                        this.tempUrl = res.requestUrls && res.requestUrls[0];
                        // this.imgUrl = res.requestUrls && res.requestUrls[0];
                        this.$emit('input', this.tempUrl);
                        this.dispatch('ElFormItem', 'el.form.change', res.requestUrls);
                        // this.$refs.upload.clearValidate();
                    } else {
                        vm.disabled = false;
                        option.onError('上传失败');
                    }
                }).catch(error => {
                    console.log(error);
                    vm.disabled = false;
                    option.onError('上传失败');
                });

            } catch (error) {
                console.log(error);
                this.disabled = false;
                option.onError('上传失败');
            }
        },
        handleRemove(file, fileList) {
            console.log(file, fileList);
            this.imgUrl = "";
        },
        handlePreview() {
            this.imgVisible = !this.imgVisible;
        },
        dispatch(componentName, eventName, params) {
            var parent = this.$parent || this.$root;
            var name = parent.$options.componentName;

            while (parent && (!name || name !== componentName)) {
                parent = parent.$parent;

                if (parent) {
                    name = parent.$options.componentName;
                }
            }
            if (parent) {
                parent.$emit.apply(parent, [eventName].concat(params));
            }
        },
    },

    mounted() {},

};
</script>

<style>
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px !important;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
}

.bigImg_wrap {
    position: relative;
}

.bigImg_mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    color: #ffffff;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 3px;
}

.bigImg_mask:hover {
    background-color: rgba(0, 0, 0, 0.5);
}

.zoom_big {
    display: none;
}

.bigImg_mask:hover .zoom_big {
    display: block;
}
</style>
