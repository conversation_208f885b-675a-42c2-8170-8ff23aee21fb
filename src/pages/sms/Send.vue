<template>
    <div>
        <el-form ref="form" :model="form" label-width="120px">
            <el-form-item label="短信平台">
                <!-- <el-radio v-model="form.sms_platform" :label="1">亿美</el-radio> -->
                <el-radio v-model="form.sms_platform" :label="2">腾域</el-radio>
            </el-form-item>
            <el-form-item label="短信类型">
                <el-radio v-model="form.sms_category" :label="1">通知</el-radio>
                <el-radio v-model="form.sms_category" :label="2">营销</el-radio>
            </el-form-item>
            <!-- <el-form-item label="短信场景">
                <el-select
                    size="mini"
                    v-model="form.sms_scene"
                    filterable
                    placeholder="请选择场景"
                >
                    <el-option
                        v-for="item in sms_sceneOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="短信模版">
                <el-radio-group
                    v-model="form.sms_type"
                    @change="sms_typeChange"
                >
                    <el-radio :label="1">普通短信</el-radio>
                    <el-radio disabled :label="2">个性化短信</el-radio>
                </el-radio-group>
                <div v-if="form.sms_type == 2">
                    <el-radio v-model="form.sms_template" :label="1"
                        >普通模板</el-radio
                    >
                    <el-radio v-model="form.sms_template" :label="2"
                        >用户模板</el-radio
                    >
                </div>
            </el-form-item> -->
            <el-form-item label="短信内容">
                <el-input
                    type="textarea"
                    :autosize="{ minRows: 8, maxRows: 6 }"
                    placeholder="请输入内容"
                    v-model="form.sms_content"
                >
                </el-input>
            </el-form-item>
            <el-form-item v-if="form.sms_category == 2" label="长连接">
                <el-input
                    type="textarea"
                    :autosize="{ minRows: 3, maxRows: 5 }"
                    placeholder="请输入长连接URL"
                    v-model="form.url"
                >
                </el-input>
            </el-form-item>
            <el-form-item v-if="form.sms_category == 2" label="短连接有效天数">
                <el-input-number
                    v-model="form.days"
                    :min="1"
                    :max="180"
                    placeholder="请输入有效天数（1-180天）"
                    controls-position="right"
                    style="width: 240px"
                >
                </el-input-number>
                <span style="margin-left: 10px; color: #999; font-size: 12px"
                    >限制为1-180天</span
                >
            </el-form-item>
            <el-form-item label="发送对象">
                <el-radio-group
                    v-model="searchForm.send_type"
                    @change="userTypeChange"
                >
                    <!-- <el-radio :label="2">所有用户</el-radio> -->
                    <el-radio :label="1">指定用户</el-radio>
                    <el-radio :label="3">导入用户</el-radio>
                </el-radio-group>
            </el-form-item>
            <div v-if="searchForm.send_type === 1">
                <el-card shadow="always" class="m-b-20">
                    <el-form-item label="期数">
                        <el-input
                            placeholder="请输入期数,多个期数请用英文逗号隔开"
                            v-model="searchForm.period"
                        >
                        </el-input>
                    </el-form-item>
                    <el-form-item label="地区">
                        <el-select
                            style="width: 90px"
                            v-model="searchForm.province_contain"
                            placeholder="包含、不包含"
                        >
                            <el-option label="包含" :value="1"> </el-option>
                            <el-option label="不包含" :value="2"> </el-option>
                        </el-select>
                        <el-select
                            class="w-mini"
                            filterable
                            clearable
                            v-model="searchForm.province_id"
                            placeholder="地区省级"
                            multiple
                        >
                            <el-option
                                v-for="(item, index) in regional"
                                :key="index"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品类型">
                        <el-select
                            style="width: 90px"
                            v-model="searchForm.product_type_contain"
                            placeholder="包含、不包含"
                        >
                            <el-option label="包含" :value="1"> </el-option>
                            <el-option label="不包含" :value="2"> </el-option>
                        </el-select>

                        <el-select
                            class="w-normal"
                            v-model="searchForm.product_type"
                            filterable
                            remote
                            clearable
                            :loading="loadings"
                            reserve-keyword
                            placeholder="请输入产品类型"
                            :remote-method="remoteMethod"
                            multiple
                        >
                            <el-option
                                v-for="(item, index) in product_typeOptions"
                                :key="index"
                                :label="item.name"
                                :value="item.name"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否暂存">
                        <el-select
                            style="width: 90px"
                            clearable
                            v-model="searchForm.is_ts"
                        >
                            <el-option label="否" :value="0"> </el-option>
                            <el-option label="是" :value="1"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="时间">
                        <el-date-picker
                            v-model="searchForm.createTime"
                            type="datetimerange"
                            range-separator="至"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="注册-开始日期"
                            end-placeholder="注册-结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                        >
                        </el-date-picker>
                        <el-date-picker
                            style="margin: 6px 0"
                            v-model="searchForm.payTime"
                            type="datetimerange"
                            range-separator="至"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="下单-开始日期"
                            end-placeholder="下单-结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                        >
                        </el-date-picker>
                        <!-- <el-button
                            type="warning"
                            style="margin-left:4px"
                            @click="getSmsForUserTotal"
                            >查询</el-button
                        > -->
                    </el-form-item>
                    <!-- <el-form-item label="发送人数">
                        {{ userTotal }} 人
                    </el-form-item> -->
                </el-card>
            </div>
            <el-form-item label="导入客户" v-if="searchForm.send_type === 3">
                <!-- <el-radio-group
                    v-model="userForm.userType"
                    @change="userTypeChange"
                >
                    <el-radio :label="1">酒云</el-radio>
                    <el-radio disabled :label="2">酒历</el-radio>
                    <el-radio disabled :label="3">公社</el-radio>
                </el-radio-group> -->

                <div>
                    <el-button size="mini" @click="downloadTemplate">
                        下载模版
                    </el-button>
                    <Vososs
                        :limit="99"
                        list-type="text"
                        :dir="dir"
                        :fileList="file.add"
                        :showFileList="false"
                        @on-success="importUpload"
                        filesType="application"
                    >
                        <el-button size="mini" type="primary"
                            >批量导入客户</el-button
                        >
                    </Vososs>
                    <!-- <uploadFile @input="importUpload">
                        
                    </uploadFile> -->
                    <Vososs
                        :limit="99"
                        list-type="text"
                        :dir="dir"
                        :fileList="file.del"
                        :showFileList="false"
                        @on-success="deleteUpload"
                        filesType="application"
                    >
                        <el-button size="mini" type="danger"
                            >批量移除客户</el-button
                        >
                    </Vososs>

                    <!-- <uploadFile @input="deleteUpload">
                        <el-button size="mini" type="danger"
                            >批量移除客户</el-button
                        >
                    </uploadFile> -->
                </div>
                <!-- <div v-if="userForm.userType == 1">
                    <div>
                        <el-input
                            size="mini"
                            v-model="userForm.wine.period"
                            placeholder="期数；以英文，隔开"
                        ></el-input>
                    </div>
                    <div>
                        <el-date-picker
                            v-model="time_date"
                            type="datetimerange"
                            size="mini"
                            range-separator="至"
                            clearable
                            @change="time_dateChange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="注册-开始日期"
                            end-placeholder="注册-结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                        >
                        </el-date-picker>
                    </div>
                    <div>
                        <el-select
                            size="mini"
                            clearable
                            v-model="userForm.wine.level"
                            filterable
                            placeholder="用户等级"
                        >
                            <el-option
                                v-for="item in levelOptions"
                                :key="item"
                                :label="'等级' + item"
                                :value="item"
                            >
                            </el-option>
                        </el-select>
                    </div>

                    <div>
                        <el-input
                            size="mini"
                            v-model="userForm.wine.activity_id"
                            placeholder="活动ID；以英文，隔开"
                        ></el-input>
                    </div>
                </div> -->
                <!-- <div v-if="userForm.userType == 2">
                    <el-input
                        size="mini"
                        v-model="userForm.calendar.activity_id"
                        placeholder="活动ID；以英文，隔开"
                    ></el-input>
                </div>
                <div v-if="userForm.userType == 3">
                    <el-input
                        size="mini"
                        v-model="userForm.calendar.company"
                        placeholder="公司；以英文，隔开"
                    ></el-input>
                    <el-input
                        size="mini"
                        v-model="userForm.calendar.position"
                        placeholder="职位；以英文，隔开"
                    ></el-input>
                </div> -->
                <!-- <el-button
                    size="mini"
                    type="primary"
                    @click="searchWineUserList"
                    >查询</el-button
                > -->
                <el-link
                    :underline="false"
                    type="info"
                    style="margin-left: 10px; font-size: 12px"
                    >共计 {{ userTotal + importUserTotal }} 人</el-link
                >
            </el-form-item>
            <el-form-item label="发送时间">
                <el-date-picker
                    v-model="form.sms_schedule"
                    type="datetime"
                    placeholder="选择日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    align="right"
                    :picker-options="pickerOptions"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="onSubmit">提交</el-button>
                <el-button @click="closeDialog">取消</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import uploadFile from "./UploadImage.vue";
import Vososs from "vos-oss";
export default {
    components: {
        uploadFile,
        Vososs,
    },
    data() {
        return {
            dir: "vinehoo/vos/marketing/",
            file: {
                add: [],
                del: [],
            },
            headers: {
                "vinehoo-client": "client",
                "vinehoo-client-version": "1.0.0",
            },
            uploadData: {
                sms_phonestr: "ddd",
            },
            time_date: [],
            register_timeOptions: [
                {
                    value: 7,
                    label: "一周",
                },
                {
                    value: 15,
                    label: "半个月",
                },
                {
                    value: 30,
                    label: "一个月",
                },
                {
                    value: 90,
                    label: "三个月",
                },
                {
                    value: 180,
                    label: "半年",
                },
                {
                    value: 365,
                    label: "一年",
                },
                {
                    value: 500,
                    label: "一年以上",
                },
            ],
            loadings: false,
            product_typeOptions: [],
            levelOptions: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
            // userTypeOptions: [
            //     {
            //         value: 1,
            //         label: "酒云"
            //     },
            //     {
            //         value: 2,
            //         label: "酒历"
            //     },
            //     {
            //         value: 3,
            //         label: "公社"
            //     }
            // ],
            searchForm: {
                period: "",
                province_contain: 1,
                send_type: 2,
                province_id: [],
                product_type_contain: 1,
                product_type: [],
                is_ts: "",
                createTime: [],
                create_stime: "",
                create_etime: "",
                payTime: [],
                stime: "",
                etime: "",
            },
            userForm: {
                userType: 1,

                calendar: {
                    activity_id: "",
                },
                community: {
                    company: "",
                    position: "",
                },
                wine: {
                    end_time: "",
                    start_time: "",
                    // order_money: "",
                    sms_phonestr: "",
                    period: "",
                    // address: "",
                    activity_id: "",
                    level: "",
                },
            },
            userTotal: 0,
            regional: [],
            form: {
                sms_platform: 2,
                sms_category: 1,
                sms_creator: "操作人",
                sms_type: 1,
                sms_phonestr: "", // redis Key
                sms_template: 1,
                sms_scene: "",
                sms_content: "",
                sms_schedule: "",
                url: "",
                days: 1,
            },
            importUserTotal: 0,
            userTotal: 0,
            sms_sceneOptions: [
                {
                    value: 1,
                    label: "新品推荐",
                },
            ],
            pickerOptions: {
                shortcuts: [
                    {
                        text: "今天",
                        onClick(picker) {
                            picker.$emit("pick", new Date());
                        },
                    },
                    {
                        text: "明天",
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24);
                            picker.$emit("pick", date);
                        },
                    },
                    {
                        text: "后天",
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24 * 2);
                            picker.$emit("pick", date);
                        },
                    },
                    {
                        text: "三天后",
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24 * 3);
                            picker.$emit("pick", date);
                        },
                    },
                    {
                        text: "七天后",
                        onClick(picker) {
                            const date = new Date();
                            date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
                            picker.$emit("pick", date);
                        },
                    },
                ],
            },
        };
    },
    mounted() {
        this.getRegionalList();
        this.getRedisKey();
    },
    methods: {
        remoteMethod(query) {
            if (query.length >= 1) {
                this.loadings = true;
                setTimeout(() => {
                    let data = {
                        keywords: query,
                    };
                    this.$request.sms
                        .getProductListForKeywords(data)
                        .then((res) => {
                            this.loadings = false;
                            if (res.data.error_code == 0) {
                                console.log(res.data);
                                this.product_typeOptions = res.data.data.list;
                            }
                        });
                }, 300);
            } else {
                this.goodsOptions = [];
            }
        },
        async getRegionalList() {
            const data = {
                type: 0,
            };
            const res = await this.$request.sms.getRegionalList(data);
            if (res.data.error_code === 0) {
                this.regional = res.data.data.list;
            }
        },
        searchWineUserList() {
            let data = { ...this.userForm.wine };
            this.$request.sms.searchWineUserList(data).then((res) => {
                if (res.data.error_code == 0) {
                    console.log(res);
                    this.userTotal = res.data.data.users;
                }
            });
        },
        time_dateChange(val) {
            if (val) {
                this.userForm.wine.start_time = val[0];
                this.userForm.wine.end_time = val[1];
            } else {
                this.userForm.wine.start_time = "";
                this.userForm.wine.end_time = "";
            }
        },
        async getSmsForUserTotal() {
            const data = {
                ...this.searchForm,
                stime:
                    this.searchForm.payTime &&
                    this.searchForm.payTime.length === 2
                        ? this.searchForm.payTime[0]
                        : "",
                etime:
                    this.searchForm.payTime &&
                    this.searchForm.payTime.length === 2
                        ? this.searchForm.payTime[1]
                        : "",
                create_stime:
                    this.searchForm.createTime &&
                    this.searchForm.createTime.length === 2
                        ? this.searchForm.createTime[0]
                        : "",
                create_etime:
                    this.searchForm.createTime &&
                    this.searchForm.createTime.length === 2
                        ? this.searchForm.createTime[1]
                        : "",
            };
            delete data["payTime"];
            delete data["createTime"];
            const res = await this.$request.sms.getSendSmsForUser(data);
            if (res.data.error_code === 0) {
                console.log(res.data.data);
                this.userTotal = res.data.data.total;
                this.form.sms_phonestr = res.data.data.sms_phonestr;
            }
        },
        async getRedisKey() {
            this.form.sms_phonestr = this.userForm.wine.sms_phonestr =
                "vinehoo.sms.phones." + parseInt(new Date().getTime() / 1000);
        },
        sms_typeChange(val) {
            if (val == 1) {
                this.form.sms_template = 1;
            }
        },
        downloadTemplate() {
            window.open(
                "https://images.vinehoo.com/vinehoo/vos/marketing/template/template.xls"
            );
        },
        deleteUpload(url) {
            // sms.updateOss
            this.updateOss(undefined, url.file);
            // this.file.del.push(url.file);
        },
        async updateOss(add, del) {
            console.warn(this.form.sms_phonestr);
            let data = {
                sms_phonestr: this.form.sms_phonestr,
                add: add ? [add] : [],
                del: del ? [del] : [],
            };
            const res = await this.$request.sms.updateOss(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                this.importUserTotal = res.data.data.total;
            } else {
                // this.file.add = []
                // this.file.del = []
                console.warn(this.file.add, this.file.del);
            }
        },
        importUpload(url) {
            this.updateOss(url.file, undefined);
            // this.file.add.push(url);
        },
        userTypeChange(val) {
            this.userTotal = 0;
            this.form.sms_phonestr = "";
            if (val === 3) {
                this.getRedisKey();
            }
        },
        closeDialog() {
            this.$emit("closeDialog");
        },

        onSubmit() {
            if (this.searchForm.send_type === 1) {
                // 检查是否至少设置了一个过滤条件
                const hasFilter =
                    this.searchForm.period ||
                    (this.searchForm.payTime &&
                        this.searchForm.payTime.length === 2) ||
                    this.searchForm.is_ts !== "" ||
                    (this.searchForm.province_id &&
                        this.searchForm.province_id.length > 0) ||
                    (this.searchForm.product_type &&
                        this.searchForm.product_type.length > 0) ||
                    (this.searchForm.createTime &&
                        this.searchForm.createTime.length === 2);

                if (!hasFilter) {
                    this.$message.error("至少设置一个过滤条件");
                    return;
                }

                console.log("查询的", this.searchForm);
                const data = {
                    ...this.form,
                };
                data.send_type = this.searchForm.send_type;
                data.search_criteria = {
                    period: this.searchForm.period,
                    stime: this.searchForm.payTime[0],
                    etime: this.searchForm.payTime[1],
                    is_ts: this.searchForm.is_ts,
                    province_contain: this.searchForm.product_type_contain,
                    province_id: this.searchForm.province_id
                        ? this.searchForm.province_id.join(",")
                        : "",
                    product_type_contain: this.searchForm.product_type_contain,
                    product_type: this.searchForm.product_type
                        ? this.searchForm.product_type.join(",")
                        : "",
                    create_stime: this.searchForm.createTime[0],
                    create_etime: this.searchForm.createTime[1],
                };
                console.log("参数", data);
                this.$request.sms.sendSms(data).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.$emit("submitForm");
                    }
                });
            } else if (this.searchForm.send_type === 2) {
                // const data = {
                //     send_type: this.searchForm.send_type
                // };
                // this.$request.sms.getSendSmsForUser(data).then(res => {
                //     if (res.data.error_code === 0) {
                //         this.form.sms_phonestr = res.data.data.sms_phonestr;

                //     }
                // });
                const sendSmsData = {
                    ...this.form,
                };
                sendSmsData.send_type = this.searchForm.send_type;
                this.$request.sms.sendSms(sendSmsData).then((res) => {
                    if (res.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.$emit("submitForm");
                    }
                });
            } else {
                if (
                    this.form.sms_schedule &&
                    this.form.sms_phonestr &&
                    this.form.sms_content
                ) {
                    if (this.searchForm.send_type === 3) {
                        let data = {
                            ...this.form,
                            ...this.file,
                        };
                        data.send_type = this.searchForm.send_type;
                        data.sms_count = this.userTotal + this.importUserTotal;
                        this.$request.sms.sendSms(data).then((res) => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.$emit("submitForm");
                            }
                        });
                    }
                } else {
                    this.$message.error("表单填写不完整");
                }
            }
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .el-upload {
    border: 0px;
}
</style>
