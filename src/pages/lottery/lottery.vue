<template>
    <div>
        <el-table border size="mini" :data="tableData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="100" align="center">
            </el-table-column>
            <el-table-column
                prop="name"
                label="奖品名称"
                min-width="220"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="type"
                label="奖品类型"
                min-width="140"
                align="center"
            >
                <template slot-scope="row">
                    {{ row.row.type | typeFormat }}
                </template>
            </el-table-column>
            <el-table-column
                prop="number"
                align="center"
                label="兔头数量/优惠券ID"
                width="200"
            >
            </el-table-column>
            <el-table-column
                prop="probability"
                label="中奖几率"
                width="200"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="sort"
                label="排序"
                width="100"
                align="center"
            >
            </el-table-column>

            <el-table-column
                prop="operator_name"
                label="操作人"
                width="100"
                align="center"
            >
            </el-table-column>

            <el-table-column
                prop="updated_time"
                label="更新时间"
                min-width="160"
                align="center"
            >
                <template slot-scope="row">
                    {{ row.row.updated_time | timeFormat }}
                </template>
            </el-table-column>
            <el-table-column
                prop="created_time"
                label="创建时间"
                min-width="160"
                align="center"
            >
                <template slot-scope="row">
                    {{ row.row.created_time | timeFormat }}
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="100"
            >
                <template slot-scope="row">
                    <el-button
                        size="mini"
                        type="text"
                        @click="editorConfig(row.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑抽奖配置"
                :visible.sync="lotteryConfigStatus"
                width="70%"
                :before-close="closeViewDialogStatus"
            >
                <el-form :model="editorData" ref="ruleForm" :rules="formRules">
                    <el-form-item
                        label="奖品名称"
                        :label-width="formLabelWidth"
                        prop="name"
                    >
                        <el-input
                            v-model="editorData.name"
                            autocomplete="off"
                            placeholder="请输入奖品名称"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="奖品类型"
                        :label-width="formLabelWidth"
                        prop="type"
                    >
                        <el-radio-group
                            v-model="editorData.type"
                            @change="typeChange"
                        >
                            <el-radio :label="0">未中奖</el-radio>
                            <el-radio :label="1">兔头</el-radio>
                            <el-radio :label="2">优惠券</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        v-if="editorData.type != 0"
                        :label="editorData.type === 1 ? '兔头数量' : '优惠券ID'"
                        :label-width="formLabelWidth"
                        prop="number"
                    >
                        <el-input
                            class="w-large"
                            v-model="editorData.number"
                            size="mini"
                            :placeholder="
                                editorData.type === 1
                                    ? '请输入兔头数量'
                                    : '请输入优惠券ID，多个用英文逗号隔开'
                            "
                        >
                        </el-input>
                        <span class="m-l-10" v-if="editorData.type != 1"
                            >多个优惠券ID用英文逗号隔开</span
                        >
                    </el-form-item>
                    <el-form-item
                        label="中奖几率"
                        :label-width="formLabelWidth"
                        :precision="0"
                        prop="probability"
                    >
                        <el-input-number
                            size="mini"
                            v-model="editorData.probability"
                            controls-position="right"
                            :min="0"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item
                        label="图标"
                        :label-width="formLabelWidth"
                        prop="image"
                    >
                        <vos-oss
                            list-type="picture-card"
                            v-if="lotteryConfigStatus"
                            :showFileList="true"
                            :limit="1"
                            :dir="dir"
                            :file-list="icon_map"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                    <el-form-item
                        label="排序"
                        :label-width="formLabelWidth"
                        :precision="0"
                        prop="sort"
                    >
                        <el-input-number
                            size="mini"
                            v-model="editorData.sort"
                            controls-position="right"
                            :min="0"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item style="margin-top:50px;margin-left:40%">
                        <el-button @click="closeViewDialogStatus"
                            >取 消</el-button
                        >
                        <el-button
                            type="primary"
                            @click="submitForm('ruleForm')"
                            >确 定</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        const checkTitleMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
        };
        return {
            dir: "vinehoo/vos/marketing/",
            formLabelWidth: "150px",
            icon_map: [],
            tableData: [],
            editorData: {},
            formRules: {
                name: [
                    {
                        required: true,
                        message: "请输入名称",
                        trigger: "blur"
                    }
                ],
                type: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "blur"
                    }
                ],
                number: [
                    {
                        required: true,
                        message: "请输入兔头数量或优惠券ID",
                        trigger: "blur"
                    }
                ],
                probability: [
                    {
                        required: true,
                        message: "请输入中奖几率",
                        trigger: "blur"
                    }
                ],
                image: [
                    {
                        required: true,
                        validator: checkTitleMap
                    }
                ],
                sort: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "blur"
                    }
                ]
            },
            lotteryConfigStatus: false
        };
    },

    mounted() {
        this.getLotteryConfigList();
    },
    filters: {
        typeFormat(val) {
            switch (val) {
                case 0:
                    return "未中奖";
                case 1:
                    return "兔头";
                case 2:
                    return "优惠券";
                default:
                    return "未知";
            }
        },
        timeFormat(val) {
            const times = new Date(val * 1000);
            const y = times.getFullYear();
            const m = times.getMonth() + 1;
            const d = times.getDate();
            const h = times.getHours();
            const min = times.getMinutes();
            const s = times.getSeconds();
            return y + "-" + m + "-" + d + " " + h + ":" + min + ":" + s;
        }
    },
    methods: {
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    const isvalid = `${this.editorData.number}`.includes("，");
                    if (isvalid) {
                        this.$message.warning("优惠券id不能含有中文逗号");
                        return;
                    }
                    this.editorData.image = this.icon_map.join(",");
                    const data = {
                        ...this.editorData
                    };
                    this.$request.lottery
                        .updateLotteryConfig(data)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.closeViewDialogStatus();
                            }
                        });
                } else {
                    return false;
                }
            });
        },
        closeViewDialogStatus() {
            this.lotteryConfigStatus = false;
            this.getLotteryConfigList();
        },
        editorConfig(row) {
            this.lotteryConfigStatus = true;
            console.log(row);
            this.editorData = row;
            this.icon_map = this.editorData.image.split(",");
        },
        typeChange(val) {
            this.editorData.number = 0;
        },
        async getLotteryConfigList() {
            const res = await this.$request.lottery.getLotteryConfig();
            try {
                if (res.data.error_code == 0) {
                    this.tableData = res.data.data.list;
                }
            } catch (e) {
                console.log(e);
            }
        }
    }
};
</script>
