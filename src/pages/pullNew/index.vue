<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    v-model="pageAttr.active_name"
                    placeholder="请输入活动名称"
                    size="mini"
                    clearable
                    @keyup.enter.native="search"
                    class="m-r-10 w-large"
                ></el-input>

                <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                <el-button type="primary" size="mini" @click="search()"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >新增</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="ID"
                        prop="id"
                        width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="是否要求新人"
                        prop="target_user"
                        width="70"
                    >
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.target_user | targetUserFilter }}
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        align="center"
                        label="活动名称"
                        prop="active_name"
                        min-width="150"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="goods_num"
                        align="center"
                        label="关联商品数"
                        width="70"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="时间"
                        width="200"
                        prop=""
                    >
                        <template #default="row">
                            <div>生效时间 : {{ row.row.effected_time }}</div>
                            <div>失效时间 : {{ row.row.invalidate_time }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="coupon_package_value"
                        align="center"
                        label="优惠券全额"
                        min-width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="活动背景色"
                        min-width="70"
                        prop="theme_color"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="规则"
                        min-width="100"
                        prop="rule"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="规则按钮背景色"
                        min-width="70"
                        prop="rule_button_color"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="按钮距离"
                        min-width="70"
                        prop="button_distance_above"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="图片"
                        prop="picture"
                        width="130"
                    >
                        <template #default="imageUrl">
                            <el-image
                                style="width: 90px; height: 90px"
                                :src="imageUrl.row.picture"
                                fit="cover"
                            ></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="创建人"
                        min-width="70"
                        prop="vh_vos_name"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="创建时间"
                        width="150"
                        prop="created_time"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="260"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                            <el-button
                                @click="preview(row.row)"
                                type="text"
                                size="mini"
                                >预览</el-button
                            >
                            <el-button
                                @click="QrCode(row.row)"
                                type="text"
                                size="mini"
                                >生成二维码</el-button
                            >
                            <el-button
                                @click="setGoods(row.row)"
                                type="text"
                                size="mini"
                                >设置商品</el-button
                            >
                            <!-- <el-button
                                @click="exportFile(row.row)"
                                type="text"
                                size="mini"
                                >用户导出</el-button
                            > -->
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="活动信息"
                :visible.sync="dialogStatus"
                width="40%"
            >
                <div style="height: 570px;overflow-y: auto;">
                    <Add v-if="dialogStatus" @close="close"></Add>
                </div>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="活动信息"
                :visible.sync="viewDialogStatus"
                width="40%"
                :before-close="closeViewDialogStatus"
            >
                <div style="height: 570px;overflow-y: auto;">
                    <Views
                        v-if="viewDialogStatus"
                        :rowData="rowData"
                        @closeViewDialogStatus="closeViewDialogStatus"
                    ></Views>
                </div>
            </el-dialog>
        </div>
        <!-- 二维码 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="基本信息"
                :visible.sync="QrCodeDialogStatus"
                width="32%"
                :before-close="closeQrCodeDialogStatus"
            >
                <div>
                    <QrCode
                        v-if="QrCodeDialogStatus"
                        :rowData="rowData"
                        @closeQrCodeDialogStatus="closeQrCodeDialogStatus"
                    ></QrCode>
                </div>
            </el-dialog>
        </div>
        <!-- 设置商品 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="商品列表"
                :visible.sync="goodsListDialogStatus"
                width="40%"
                :before-close="closeGoodsListDialogStatus"
            >
                <div style="height: 570px;overflow-y: auto;">
                    <SetGoods
                        v-if="goodsListDialogStatus"
                        :rowData="rowData"
                        :type="2"
                        @closeQrCodeDialogStatus="closeGoodsListDialogStatus"
                    ></SetGoods>
                </div>
            </el-dialog>
        </div>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="预览"
                :visible.sync="previewDialogStatus"
                width="40%"
                :before-close="previewHandle"
            >
                <div>
                    <Preview
                        v-if="previewDialogStatus"
                        :rowData="rowData"
                        @previewHandle="previewHandle"
                    ></Preview>
                </div>
            </el-dialog>
        </div>

        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import Views from "./view.vue";
import QrCode from "../../components/QrCode/QrCode.vue";
import SetGoods from "../../components/setGoods/index.vue";
// import fileDownload from "js-file-download";
import Preview from "./preview.vue";
export default {
    components: { Add, Views, QrCode, SetGoods, Preview },

    data() {
        return {
            rowData: {},
            tableData: [],
            dialogStatus: false,
            viewDialogStatus: false,
            QrCodeDialogStatus: false,
            goodsListDialogStatus: false,
            previewDialogStatus: false,
            pageAttr: {
                active_name: "",
                page: 1,
                limit: 10
            },
            total: 0,
            isEdit: false
        };
    },
    mounted() {
        this.getPullNewList();
    },
    methods: {
        //拉新列表
        async getPullNewList() {
            let res = await this.$request.PullNew.getPullNewList(this.pageAttr);
            console.log("拉新列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.pageAttr.page = 1;
            this.getPullNewList();
        },
        preview(row) {
            this.rowData = row;
            this.previewDialogStatus = true;
        },
        previewHandle() {
            this.previewDialogStatus = false;
        },
        QrCode(row) {
            this.rowData = row;
            this.QrCodeDialogStatus = true;
        },
        closeQrCodeDialogStatus() {
            this.QrCodeDialogStatus = false;
            this.getPullNewList();
        },
        setGoods(row) {
            this.rowData = row;
            this.goodsListDialogStatus = true;
        },
        closeGoodsListDialogStatus() {
            this.goodsListDialogStatus = false;
            this.getPullNewList();
        },
        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getPullNewList();
        },
        view(row) {
            this.viewDialogStatus = true;
            this.rowData = row;
        },
        close() {
            this.dialogStatus = false;
            this.getPullNewList();
        },
        //导出
        exportFile(row) {
            const blob = new Blob([row]);
            console.log("文件", blob);
            // this.$message.success("导出成功");
            // fileDownload(blob, "注册拉新.xls");
            // if (blob.size < 1024) {
            //     this.$message.error("导出失败");
            // } else {

            // }
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getPullNewList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getPullNewList();
        }
    },

    filters: {
        targetUserFilter(value) {
            switch (value) {
                case 0:
                    return "否";
                case 1:
                    return "是";
                default:
                    return "";
            }
        }
    }
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
