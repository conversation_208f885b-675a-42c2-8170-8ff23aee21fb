<template>
    <div>
        <el-form
            :model="form"
            :rules="rules"
            ref="ruleForm"
            label-width="130px"
            size="mini"
            class="demo-ruleForm"
        >
            <el-form-item label="活动名称" prop="active_name">
                <el-input
                    v-model="form.active_name"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item label="是否要求新人">
                <el-radio-group v-model="form.target_user">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="活动时间" prop="name">
                <el-date-picker
                    style="width:400px"
                    v-model="form.time"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="跳转地址" prop="jump_link">
                <el-input
                    v-model="form.jump_link"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="规则和分享按钮背景"
                label-width="150px"
                prop="rule_button_color"
            >
                <el-color-picker
                    v-model="form.rule_button_color"
                ></el-color-picker>
            </el-form-item>
            <el-form-item
                label="规则按钮字体颜色"
                label-width="150px"
                prop="rule_button_font_color"
            >
                <el-color-picker
                    v-model="form.rule_button_font_color"
                ></el-color-picker>
            </el-form-item>
            <el-form-item
                label="分享按钮是否显示"
                label-width="150px"
                prop="share_button_show"
            >
                <el-radio-group v-model="form.share_button_show">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="活动主题色" prop="theme_color">
                <el-color-picker
                    v-model="form.theme_color"
                    color-format="hex"
                ></el-color-picker>
            </el-form-item>
            <el-form-item label="按钮内容" prop="button_desc">
                <el-input
                    v-model="form.button_desc"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item label="按钮颜色" prop="button_color">
                <el-color-picker
                    v-model="form.button_color"
                    color-format="hex"
                ></el-color-picker>
            </el-form-item>
            <el-form-item label="按钮字体颜色" prop="button_font_color">
                <el-color-picker
                    v-model="form.button_font_color"
                    color-format="hex"
                ></el-color-picker>
            </el-form-item>

            <el-form-item label="按钮距离上方" prop="button_distance_above">
                <el-input
                    v-model="form.button_distance_above"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item label="规则" prop="rule">
                <el-input
                    style="width:400px"
                    type="textarea"
                    :rows="3"
                    v-model="form.rule"
                ></el-input>
            </el-form-item>
            <el-form-item label="图片" prop="img">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item label="优惠券" prop="coupon_package_id">
                <el-select
                    v-model="form.coupon_package_id"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入关键词"
                    :remote-method="remoteMethod"
                    :loading="loading"
                >
                    <el-option
                        v-for="item in couponOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>分享配置</span>
                </div>
                <div>
                    <el-form-item label="分享图" prop="img">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :limit="1"
                            :dir="dir"
                            :file-list="icon_map1"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="主标题" prop="main_title">
                        <el-input
                            style="width:400px"
                            v-model="form.main_title"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="副标题" prop="sub_title">
                        <el-input
                            style="width:400px"
                            v-model="form.sub_title"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="分享链接" prop="share_url">
                        <el-input
                            style="width:400px"
                            v-model="form.share_url"
                        ></el-input>
                    </el-form-item>
                </div>
            </el-card>

            <el-form-item
                class="m-t-20"
                style="display: flex;justify-content: center;margin-left:-130px"
            >
                <el-button @click="close()">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        const checkImg = (rule, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请选择图片"));
            } else {
                callback();
            }
        };
        const checkShare_image = (rule, value, callback) => {
            if (this.icon_map1.length == 0) {
                callback(new Error("请选择图片"));
            } else {
                callback();
            }
        };

        return {
            dir: "vinehoo/vos/marketing/",
            icon_map: [],
            icon_map1: [],
            couponOptions: [],
            loading: false,
            form: {
                active_name: "",
                target_user: 1,
                time: "",
                jump_link: "",
                rule_button_color: "",
                rule_button_font_color: "",
                share_button_show: 1,
                button_font_color: "",
                theme_color: "",
                button_desc: "",
                button_color: "",
                button_distance_above: "",
                rule: "",
                img: "",
                coupon_package_id: "",
                share_image: "",
                main_title: "",
                sub_title: "",
                share_url: ""
            },
            rules: {
                active_name: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur"
                    }
                ],
                main_title: [
                    {
                        required: true,
                        message: "请输入主标题",
                        trigger: "blur"
                    }
                ],
                sub_title: [
                    {
                        required: true,
                        message: "请输入副标题",
                        trigger: "blur"
                    }
                ],
                share_url: [
                    {
                        required: true,
                        message: "请输入分享链接",
                        trigger: "blur"
                    }
                ],
                button_font_color: [
                    {
                        required: true,
                        message: "请选择按钮字体颜色",
                        trigger: "blur"
                    }
                ],
                share_button_show: [
                    {
                        required: true,
                        message: "请选择是否显示分享按钮",
                        trigger: "blur"
                    }
                ],
                rule_button_font_color: [
                    {
                        required: true,
                        message: "请选择规则按钮字体颜色",
                        trigger: "blur"
                    }
                ],
                time: [
                    {
                        required: true,
                        message: "请选择时间",
                        trigger: "blur"
                    }
                ],
                jump_link: [
                    {
                        required: true,
                        message: "请输入跳转路径",
                        trigger: "blur"
                    }
                ],
                rule_button_color: [
                    {
                        required: true,
                        message: "请输入规则按钮背景色",
                        trigger: "blur"
                    }
                ],
                theme_color: [
                    {
                        required: true,
                        message: "请输入活动背景色",
                        trigger: "blur"
                    }
                ],

                button_desc: [
                    {
                        required: true,
                        message: "请输入按钮内容",
                        trigger: "blur"
                    }
                ],
                button_color: [
                    {
                        required: true,
                        message: "请输入按钮颜色",
                        trigger: "blur"
                    }
                ],
                button_distance_above: [
                    {
                        required: true,
                        message: "请输入按钮距离上方",
                        trigger: "blur"
                    }
                ],
                rule: [
                    {
                        required: true,
                        message: "请输入规则",
                        trigger: "blur"
                    }
                ],
                img: [
                    {
                        validator: checkImg,
                        required: true,
                        trigger: "blur"
                    }
                ],
                share_image: [
                    {
                        validator: checkShare_image,
                        required: true,
                        trigger: "blur"
                    }
                ],
                coupon_package_id: [
                    {
                        required: true,
                        message: "请选择优惠券包",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    methods: {
        close() {
            this.$emit("close");
        },
        remoteMethod(query) {
            console.log(query);
            if (query !== "") {
                this.loading = true;
                setTimeout(() => {
                    this.loading = false;
                    let data = {
                        id: query,
                        page: 1,
                        limit: 10
                    };
                    this.$request.newZoneCoupons
                        .getCouponPackageList(data)
                        .then(res => {
                            console.log("优惠券包", res);
                            if (res.data.error_code == 0) {
                                this.couponOptions = res.data.data.list.map(
                                    item => {
                                        return {
                                            id: item.id,
                                            name:
                                                item.id +
                                                "-" +
                                                item.coupon_package_name
                                        };
                                    }
                                );
                                console.log(this.couponOptions);
                            }
                        });
                }, 200);
            } else {
                this.couponOptions = [];
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = {
                        active_name: this.form.active_name,
                        target_user: this.form.target_user,
                        effected_time: this.form.time ? this.form.time[0] : "",
                        invalidate_time: this.form.time
                            ? this.form.time[1]
                            : "",
                        jump_link: this.form.jump_link,
                        rule_button_color: this.form.rule_button_color,
                        theme_color: this.form.theme_color,
                        button_desc: this.form.button_desc,
                        button_color: this.form.button_color,
                        button_distance_above: this.form.button_distance_above,
                        rule: this.form.rule,
                        picture: this.icon_map.join(","),
                        coupon_package_id: this.form.coupon_package_id,
                        share_image: this.icon_map1.join(","),
                        main_title: this.form.main_title,
                        sub_title: this.form.sub_title,
                        share_url: this.form.share_url,
                        rule_button_font_color: this.form
                            .rule_button_font_color,
                        share_button_show: this.form.share_button_show,
                        button_font_color: this.form.button_font_color
                    };
                    console.log("添加", data);
                    this.$request.PullNew.addPullNew(data).then(res => {
                        console.log("添加结果", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("添加成功");
                            this.close();
                        }
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
