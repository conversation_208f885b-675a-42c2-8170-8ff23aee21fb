<template>
  <div>
    <el-form
      ref="groupingRef"
      :inline="false"
      :rules="rules"
      :model="addGroupData"
      label-width="120px"
    >
      <el-form-item label="商品期数" prop="period">
        <el-select
          v-model="addGroupData.period"
          placeholder="请输入商品期数"
          clearable
          remote
          filterable
          :remote-method="searchGoodsByPeriod"
          @change="seachGoodPackage"
        >
          <el-option
            v-for="item in GoodsOptions"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-card
        shadow="always"
        v-show="addGroupData.period"
        style="margin-bottom: 20px"
      >
        <el-descriptions :title="selectGood.title"> </el-descriptions>
        <!-- <el-descriptions
                    v-for="i in packageProductInfo"
                    :key="i.id"
                    :colon="false"
                >
                    <el-descriptions-item :label="i.short_code"
                        >库存:{{ i.inventory }}</el-descriptions-item
                    >
                </el-descriptions> -->
        <div class="product_package">
          <div>
            <div
              v-for="i in packageProductInfo"
              :key="i.id + parseInt(Math.random() * (99999 - 10000) + 1, 10)"
              class="product_package_content"
            >
              <span class="package_first">
                {{ i.short_code }}
              </span>
            </div>
          </div>
          <div>
            <div
              v-for="i in packageProductInfo"
              :key="i.id + parseInt(Math.random() * (99999 - 10000) + 1, 10)"
              class="product_package_content"
            >
              <span>库存:{{ i.inventory }}</span>
            </div>
          </div>
        </div>
        <div class="product_package">
          <div>
            <div
              v-for="i in packageInfo"
              :key="i.id"
              class="product_package_content"
            >
              <span class="package_first">
                {{ i.package_name }}
              </span>
            </div>
          </div>
          <div>
            <div
              v-for="i in packageInfo"
              :key="i.id"
              class="product_package_content"
            >
              <span>{{ i.packageNum }}</span>
              <span
                v-for="item in i.product"
                :key="item.id"
                class="package_second"
              >
              </span>
            </div>
          </div>
          <div>
            <div
              v-for="i in packageInfo"
              :key="i.id"
              class="product_package_content"
            >
              <span class="package_third">¥{{ i.price }}</span>
            </div>
          </div>
        </div>
      </el-card>
      <!-- <el-form-item label="可成团数" prop="group_sum">
        <el-input-number
          :precision="0"
          :min="0"
          v-model="addGroupData.group_sum"
          style="max-width: 350px"
          placeholder="请输入可成团数"
          clearable
        ></el-input-number>
      </el-form-item> -->
      <el-form-item label="成团人数" prop="group_people">
        <el-input-number
          :precision="0"
          :min="0"
          v-model="addGroupData.group_people"
          style="max-width: 350px"
          placeholder="请输入成团人数"
          clearable
        ></el-input-number>
      </el-form-item>
      <el-form-item label="拼团价格" prop="grouping_price_checkbox">
        <el-checkbox-group v-model="group_priceList" @change="changeGroupPrice">
          <el-checkbox
            border
            v-for="item in packageInfo"
            :label="item.id"
            :key="item.id"
            >{{ item.package_name }}</el-checkbox
          >
        </el-checkbox-group>
        <el-form-item
          style="margin-top: 20px"
          label-width="100px"
          :label="i.package_name"
          v-for="i in addGroupData.group_price"
          :key="i.package_id"
          prop="group_price"
        >
          <el-input-number
            :precision="2"
            :min="0"
            v-model="i.price"
            style="max-width: 350px"
            placeholder="请输入拼团价格"
            clearable
          ></el-input-number>
        </el-form-item>
      </el-form-item>
      <el-form-item label="邀请新人" prop="is_invite">
        <el-switch
          v-model="addGroupData.is_invite"
          :active-value="1"
          :inactive-value="0"
        >
        </el-switch>
      </el-form-item>
      <el-card shadow="hover" style="margin-top: 20px; max-width: 7500px">
            <div slot="header">
                <span> 分享配置 </span>
            </div>
            <!-- card body -->
            <div>
                <el-form-item label="分享图" prop="share_image">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="share_image"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="主标题" prop="main_title">
                    <el-input
                        style="width: 380px"
                        v-model="addGroupData.main_title"
                        size="normal"
                        clearable
                    ></el-input>
                </el-form-item>
            </div>
            <el-form-item label="启用" prop="share_status">
            <el-switch
              v-model="addGroupData.share_status"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </el-form-item>
            <!-- <div>
                <el-form-item label="副标题" prop="sub_title">
                    <el-input
                        style="width: 380px"
                        v-model="addGroupData.sub_title"
                        size="normal"
                        clearable
                    ></el-input>
                </el-form-item>
            </div> -->
            <!-- <div>
                <el-form-item label="分享链接" prop="share_url">
                    <el-input
                        style="width: 380px"
                        v-model="addGroupData.share_url"
                        size="normal"
                        clearable
                    ></el-input>
                </el-form-item>
            </div> -->
        </el-card>
    </el-form>
  </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
  components: {
        VosOss,
    },
  data() {
    // const groupPricevali = ()
    const groupPricevali = (rules, value, callback) => {
      if (this.addGroupData.group_price.length > 0) {
        callback();
      } else {
        callback(new Error("请输入价格"));
      }
    };
    const groupPriceCheckboxvali = (rules, value, callback) => {
      // console.warn(this.addNewcomersData.newcomer_price);
      console.warn(this.addGroupData.group_price);
      callback();
    };
    const checkShareImg = (rules, value, callback) => {
      if (this.share_image.length == 0) {
        callback(new Error("请上传分享图"));
      } else {
        callback();
      }
    };
    return {
      addGroupDataRules: {},
      packageInfo: [],
      packageProductInfo: [],
      dir: "vinehoo/vos/marketing/",
      addGroupData: {
        period: "",
        periods_type: 0,
        // group_sum: 0,
        group_people: 0,
        group_price: [],
        is_invite: 0,
        share_image: "",
        main_title: "",
        sub_title: "",
        share_url: "",
        share_status:1
      },
      group_priceList: [],
      GoodsOptions: [],
      selectGood: {
        title: "",
      },
      share_image: [],
      isEdit: false,
      rules: {
        period: [
          {
            required: true,
            trigger: "change",
            message: "请输入期数",
          },
        ],
        // group_sum: [
        //   {
        //     required: true,
        //     trigger: "change",
        //     message: "请输入可成团数",
        //   },
        // ],
        group_people: [
          {
            required: true,
            trigger: "change",
            message: "请输入成团人数",
          },
        ],
        grouping_price_checkbox: [
          {
            required: true,
            trigger: "change",
            validator: groupPriceCheckboxvali,
            // message: "请输入拼团价格",
          },
        ],
        group_price: [
          {
            required: true,
            trigger: "change",
            validator: groupPricevali,
            // message: "请输入拼团价格",
          },
        ],
        is_invite: [
          {
            required: true,
            trigger: "change",
            message: "请输入是否邀请新人",
          },
          
        ],
        share_image: [
                    {
                        required: true,
                        validator: checkShareImg
                    }
                ],
                main_title: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请填写主标题"
                    }
                ],
                // sub_title: [
                //     {
                //         required: true,
                //         trigger: "blur",
                //         message: "请填写副标题"
                //     }
                // ],
                // share_url: [
                //     {
                //         required: true,
                //         message: "请填写分享链接",
                //         trigger: "blur"
                //     }
                // ],
      },
    };
  },

  mounted() {},

  methods: {
    changeGroupPrice(val, detaEcho) {
      let tempArr = [];
      val.map((item) => {
        this.packageInfo.map((item2) => {
          if (!detaEcho) {
            if (item2.id == item) {
              tempArr.push({
                package_id: item,
                package_name: item2.package_name,
                price: 0,
              });
            }
          } else {
            if (item2.id == item.id) {
              tempArr.push({
                package_id: Number(item.id),
                package_name: item2.package_name,
                price: item.price,
              });
            }
          }
        });
      });
      this.addGroupData.group_price = tempArr;
    },
    searchGoodsByPeriod(periods) {
      return new Promise((resolve, reject) => {
        this.$request.grouping
          .getGoodsByPeriod({
            periods: periods,
            periods_type: 0,
          })
          .then((res) => {
            this.GoodsOptions = res.data.data.list;
            resolve();
          });
      });
    },
    seachGoodPackage(id) {
      return new Promise((resolve, reject) => {
        if (id) {
          this.GoodsOptions.map((item) => {
            if (item.id === id) {
              this.selectGood.title = item.title;
            }
          });
          this.$request.grouping
            .getGoodsPackage({
              period: id,
              periods_type: 0,
            })
            .then((res) => {
              this.packageInfo = res.data.data;
              let packageProductInfo = res.data.data.map((item, key) => {
                this.packageInfo[key].packageNum = item.product
                  .map((item2) => {
                    return item2.short_code + "*" + item2.nums;
                  })
                  .join("+");
                return item.product;
              });
              this.packageProductInfo = packageProductInfo.flat();
              resolve();
            });
        } else {
          this.selectGood = {};
          this.packageInfo = [];
          this.packageProductInfo = [];
          this.GoodsOptions = [];
          this.addGroupData.group_price = [];
          reject();
        }
      });
    },
    dataEcho(row) {
      if (row) {
        this.isEdit = true;
        this.addGroupData.id = row.id;
        this.addGroupData.period = row.period;
        this.addGroupData.share_image = row.share_image;
        this.addGroupData.main_title = row.main_title;
        this.addGroupData.sub_title = row.sub_title;
        this.addGroupData.share_url = row.share_url;
      if (this.addGroupData.share_image != "") {
          this.share_image = this.addGroupData.share_image .split(
              ","
          );
      }
        // this.addGroupData.group_sum = row.group_sum;
        this.addGroupData.group_people = row.group_people;
        this.group_priceList = JSON.parse(row.group_price).map((item) => {
          return item.package_id;
        });
        console.warn(this.group_priceList);
        let group_priceArr = JSON.parse(row.group_price).map((item) => {
          return {
            id: item.package_id,
            price: item.price,
          };
        });
        this.addGroupData.is_invite = row.is_invite;
        this.addGroupData.share_status = row.share_status;
        this.searchGoodsByPeriod(row.period).then(() => {
          this.seachGoodPackage(row.period).then(() => {
            this.changeGroupPrice(group_priceArr, true);
          });
        });
      }
    },
    submitGrouping() {
      this.$refs["groupingRef"].validate((valid) => {
        if (valid) {
          if (this.addGroupData.group_price.length == 0) {
            this.$message.warning("请选择套餐");
            return;
          }
          this.addGroupData.share_image = this.share_image.join(",");
          let addGroupData = JSON.parse(JSON.stringify(this.addGroupData));

          addGroupData.group_price.map((item) => {
            delete item.package_name;
          });
          addGroupData.group_price = addGroupData.group_price.map((item) => {
            return {
              package_id: item.package_id,
              price: item.price.toFixed(2),
            };
          });
          addGroupData.group_price = JSON.stringify(addGroupData.group_price);
          let methods = this.isEdit ? "updateGroup" : "addGroup";
          console.warn(addGroupData);
          this.$request.grouping[methods](addGroupData)
            .then((res) => {
              if (res.data.error_code == 0) {
                this.$emit("successSubmit");
              } else {
                this.addGroupData = JSON.parse(
                  JSON.stringify(this.addGroupData)
                );
              }
            })
            .catch((e) => {
              this.addGroupData = JSON.parse(JSON.stringify(addGroupData));
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.product_package {
  display: flex;
  // flex-direction: column;
  .product_package_content {
    margin-bottom: 10px;
    .package_first {
      margin-right: 20px;
    }
    .package_second {
      // width: 300px;
      margin-right: 20px;
    }
    .package_third {
      margin-right: 20px;
    }
  }
}
</style>