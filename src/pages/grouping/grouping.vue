<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form
                ref="form"
                label-width="80px"
                :inline="true"
                @submit.native.prevent
                size="mini"
            >
                <el-form-item>
                    <el-input
                        @keyup.enter.native="queryGrouping"
                        v-model="queryGroupingData.period"
                        placeholder="请输入期数"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryGroupingData.title"
                        @keyup.enter.native="queryGrouping"
                        placeholder="请输入标题"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryGroupingData.onsale_status"
                        placeholder="请选择商品状态"
                        clearable
                    >
                        <el-option
                            v-for="item in onsaleStatusList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-select
                        v-model="queryGroupingData.is_invite"
                        placeholder="请选择是否必须邀请新人"
                        clearable
                    >
                        <el-option label="否" value="0"> </el-option>
                        <el-option label="是" value="1"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryGrouping"
                        >查询</el-button
                    >
                    <el-button type="success" @click="updateGrouping"
                        >新增</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="GroupingList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="期数" prop="period" width="120">
                </el-table-column>
                <el-table-column label="商品名称" prop="title">
                </el-table-column>
                <el-table-column label="商品状态" prop="title" width="100">
                    <template slot-scope="scope">
                        {{ onsaleStatus[scope.row.onsale_status] }}
                    </template>
                </el-table-column>
                <el-table-column label="原售价" prop="price" width="120">
                </el-table-column>
                <el-table-column label="拼团价" width="120">
                    <template slot-scope="scope">
                        <span>{{
                            JSON.parse(scope.row.group_price)[0].price
                        }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="可成团数" prop="group_sum" width="120">
        </el-table-column> -->
                <el-table-column
                    label="成团人数"
                    prop="group_people"
                    width="120"
                >
                </el-table-column>
                <el-table-column label="是否必须邀请新人" width="120">
                    <template slot-scope="scope">
                        {{ scope.row.is_invite == 0 ? "否" : "是" }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="150">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="editGrouping(scope)"
                            size="mini"
                            >编辑</el-button
                        >
                        <el-button
                            type="text"
                            size="mini"
                            @click="deleteGrouping(scope)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryGroupingData.limit"
                :current-page="queryGroupingData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            :close-on-click-modal="false"
            title="拼团设置"
            :visible.sync="addGroupingVisible"
            width="60%"
            @close="cloesGrouping"
        >
            <UpdateGroup
                v-if="addGroupingVisible"
                ref="addGroupingDia"
                @successSubmit="addGroupingVisible = false"
            ></UpdateGroup>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="addGroupingVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdate"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import UpdateGroup from "./UpdateGroup.vue";
export default {
    components: { UpdateGroup },
    data() {
        return {
            GroupingList: [],
            queryGroupingData: {
                period: "",
                title: "",
                onsale_status: "",
                is_invite: "",
                page: 1,
                limit: 10
            },
            addGroupingVisible: false,
            onsaleStatusList: [
                { value: 0, label: "待上架" },
                { value: 1, label: "待售中" },
                { value: 2, label: "在售中" },
                { value: 3, label: "已下架" },
                { value: 4, label: "已售罄" }
            ],
            onsaleStatus: {
                0: "待上架",
                1: "待售中",
                2: "在售中",
                3: "已下架",
                4: "已售罄"
            },
            total: 0
        };
    },
    computed: {},
    mounted() {
        this.queryGroupingList();
    },

    methods: {
        queryGrouping() {
            this.queryGroupingData.page = 1;
            this.queryGroupingList();
        },
        groupPrice(group_price) {
            let arr = JSON.parse(group_price)
                .map(item => {
                    return item.price;
                })
                .join(" , ");
            return arr;
        },
        queryGroupingList() {
            this.$request.grouping
                .getGroupingList(this.queryGroupingData)
                .then(result => {
                    this.GroupingList = result.data.data.list;
                    this.total = result.data.data.total;
                });
        },
        updateGrouping() {
            this.addGroupingVisible = true;
        },
        editGrouping(scope) {
            this.addGroupingVisible = true;
            this.$nextTick(() => {
                this.$refs.addGroupingDia.dataEcho(scope.row);
            });
        },
        cloesGrouping() {
            this.queryGroupingList();
        },
        comfirmUpdate() {
            this.$nextTick(() => {
                this.$refs.addGroupingDia.submitGrouping();
            });
        },
        handleSizeChange(limit) {
            this.queryGroupingData.limit = limit;
            this.queryGroupingData.page = 1;
            this.queryGroupingList();
        },
        handleCurrentChange(page) {
            this.queryGroupingData.page = page;
            this.queryGroupingList();
        },
        deleteGrouping(params) {
            this.$confirm("确定删除该拼团？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.$request.grouping
                    .deleteGrouping({ id: params.row.id })
                    .then(result => {
                        if (result.data.error_code == 0) {
                            this.$message({
                                type: "success",
                                message: "删除成功！"
                            });
                            this.queryGroupingList();
                        }
                    });
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
