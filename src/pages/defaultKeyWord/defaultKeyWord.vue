<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-table
                :data="defaultKeyData"
                border
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                size="mini"
            >
                <el-table-column label="频道" width="200">
                    <template slot-scope="scope">
                        {{ typeList[scope.row.type] }}
                    </template>
                </el-table-column>
                <el-table-column label="默认关键词" prop="keyword" width="200">
                </el-table-column>
                <el-table-column label="修改时间" prop="updated_at" width="250">
                </el-table-column>
                <el-table-column
                    label="操作人"
                    prop="operator_name"
                    width="200"
                >
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="150">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="editKey(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <el-dialog
            title="默认关键词设置"
            :visible.sync="defaultKeyWordVisible"
            width="40%"
            @close="closeEditkeyword"
            :close-on-click-modal="false"
        >
            <div>
                <el-form
                    :model="defaultKeywordRow"
                    ref="defaultKeywordRow"
                    :rules="defaultKeyRules"
                    label-width="130px"
                    :inline="false"
                >
                    <el-form-item label="频道：">
                        <el-input
                            v-model="typeList[defaultKeywordRow.type]"
                            disabled
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="默认关键词：" prop="keyword">
                        <el-input
                            v-model="defaultKeywordRow.keyword"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="defaultKeyWordVisible = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="comfirmEditkeyword"
                    >确认</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: "Vue2MarketingDefaultkeyword",

    data() {
        return {
            defaultKeyData: [],
            typeList: { 0: "首页", 1: "闪购", 2: "秒发", 3: "社区" },
            defaultKeyWordVisible: false,
            defaultKeywordRow: {},
            defaultKeyRules: {
                keyword: [
                    {
                        required: true,
                        message: "请输入关键词",
                        trigger: "blur"
                    }
                ]
            }
        };
    },

    mounted() {
        this.getDefaultKeywordList();
    },

    methods: {
        editKey(row) {
            this.defaultKeyWordVisible = true;
            this.defaultKeywordRow = JSON.parse(JSON.stringify(row));
        },
        getDefaultKeywordList() {
            this.$request.defaultkeyword
                .getDefaultKeywordList()
                .then(result => {
                    this.defaultKeyData = result.data.data.list;
                });
        },
        comfirmEditkeyword() {
            this.$request.defaultkeyword
                .updateKeyword({
                    id: this.defaultKeywordRow.id,
                    keyword: this.defaultKeywordRow.keyword
                })
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.$message({
                            type: "success",
                            message: "操作成功"
                        });
                        this.defaultKeyWordVisible = false;
                        this.getDefaultKeywordList();
                    }
                });
        },
        closeEditkeyword() {}
    }
};
</script>
