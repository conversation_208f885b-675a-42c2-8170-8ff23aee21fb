<template>
    <div>
        <div>
            <div>
                <el-radio-group v-model="radio" @change="changeRadio">
                    <el-radio label="collect">
                        收藏用户
                    </el-radio>
                    <el-radio label="want">
                        想要用户
                    </el-radio>
                </el-radio-group>
            </div>
            <div style="margin:10px 0;font-size: 16px;">
                总人数:
                <span style="font-weight:600;"
                    >{{ total }}人</span
                >
            </div>
        </div>
        <!-- <el-form :inline="true" size="mini">
            <el-form-item>
                <el-input
                    clearable
                    v-model="query.period"
                    placeholder="请输入期数"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    clearable
                    v-model="query.uid"
                    placeholder="请输入用户ID"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    clearable
                    v-model="query.user_phone"
                    placeholder="请输入用户手机号"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
        </el-form> -->
        <el-table
            v-if="radio == 'collect'"
            :data="collet_user_list"
            border
            size="mini"
            :header-cell-style="{ 'text-align': 'center' }"
            :cell-style="{ 'text-align': 'center' }"
            key="collect"
        >
            <el-table-column label="用户ID" prop="uid"> </el-table-column>
            <el-table-column label="用户昵称" prop="nickname">
            </el-table-column>
            <el-table-column label="用户手机号" prop="plaintext_telephone">
            </el-table-column>
        </el-table>
        <el-table
            v-if="radio == 'want'"
            :data="want_user_list"
            border
            size="mini"
            :header-cell-style="{ 'text-align': 'center' }"
            :cell-style="{ 'text-align': 'center' }"
            key="want"
        >
            <el-table-column label="用户ID" prop="uid"> </el-table-column>
            <el-table-column label="用户昵称" prop="nickname">
            </el-table-column>
            <el-table-column label="用户手机号" prop="user_phone">
            </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    name: "Vue2MarketingRequestUser",

    data() {
        return {
            collet_user_list: [],
            want_user_list: [],
            query: {
                period_id: "",
                period: "",
                is_from: 2
            },
            total: 0,
            radio: "collect"
        };
    },

    mounted() {},
    methods: {
        getCollectionUserlist(params) {
            if (params) {
                this.query.period_id = params.period;
                this.query.period = params.period;
            }
            this.$request.repeatPurchaseRate
                .getCollectionUserlist(this.query)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.collet_user_list = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        getWantUserlist() {
            this.$request.repeatPurchaseRate
                .getRepurchasestatisticsUserlist(this.query)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.want_user_list = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        search() {
            this.query.page = 1;
            this.changeRadio();
        },
        handleSizeChange(limit) {
            this.query.page = 1;
            this.query.limit = limit;
            this.changeRadio();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.changeRadio();
        },
        changeRadio() {
            if (this.radio === "collect") {
                this.getCollectionUserlist();
            } else {
                this.getWantUserlist();
            }
        }
    }
};
</script>

<style lang="scss" scoped></style>
