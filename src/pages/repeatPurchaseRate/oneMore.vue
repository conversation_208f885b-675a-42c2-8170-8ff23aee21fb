<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-date-picker
                        v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :default-time="['00:00:00', '23:59:59']"
                        @change="changeTime"
                    />
                </el-form-item>
                <el-form-item>
                    <el-input
                        clearable
                        v-model="query.period"
                        placeholder="请输入期数"
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        clearable
                        v-model="query.short_code"
                        placeholder="请输入简码"
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        clearable
                        v-model="query.product_name"
                        placeholder="请输入产品名称"
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.numssort"
                        placeholder="请选择点击人数排序"
                        @change="search"
                        clearable
                    >
                        <el-option label="升序" value="0"></el-option>
                        <el-option label="倒序" value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.propsort"
                        placeholder="请选择点击人数比例排序"
                        @change="search"
                        clearable
                    >
                        <el-option label="升序" value="0"></el-option>
                        <el-option label="倒序" value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.buypeoplesort"
                        placeholder="请选择商品购买人数排序"
                        @change="search"
                        clearable
                    >
                        <el-option label="升序" value="0"></el-option>
                        <el-option label="倒序" value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top:10px">
            <el-table
                :data="repeatPurchaseList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                show-summary
                :summary-method="handleSummary"
            >
                <el-table-column width="80px" align="center"> </el-table-column>
                <el-table-column label="上架时间" width="300px" align="center">
                    <template slot-scope="{ row }">
                        {{ row.period_start_time }} - {{ row.period_end_time }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="period"
                    label="期数"
                    width="100px"
                    align="center"
                >
                </el-table-column>
                <el-table-column prop="short_code" label="简码" align="center">
                </el-table-column>
                <el-table-column
                    prop="product_name"
                    label="产品名称"
                    min-width="240px"
                    :show-overflow-tooltip="true"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="sale_nums"
                    label="销售数量"
                    width="100px"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ row.sale_nums }}瓶
                    </template>
                </el-table-column>
                <el-table-column
                    prop="order_nums"
                    label="订单数量"
                    width="100px"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="buy_people_nums"
                    label="商品购买人数"
                    width="100px"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="nums"
                    label="再来一单点击人数"
                    width="100px"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="proportion"
                    label="点击人数比例"
                    width="100px"
                    align="center"
                >
                    <template slot-scope="{ row }">
                        {{ calcDecimal(row.proportion) }}%
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            @click="lookUser(row)"
                            >查看用户</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            title="查看需求用户"
            :visible.sync="reqeat_purchase_visible"
            width="70%"
            :close-on-click-modal="false"
        >
            <oneMoreUser
                ref="oneMoreUserRef"
                v-if="reqeat_purchase_visible"
            ></oneMoreUser>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="reqeat_purchase_visible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="reqeat_purchase_visible = false"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import oneMoreUser from "./oneMoreUser.vue";
import Decimal from "decimal.js";
export default {
    components: { oneMoreUser },
    data() {
        return {
            time: [],
            query: {
                limit: 10,
                page: 1,
                short_code: "",
                period: "",
                product_name: "",
                start_time: "",
                end_time: "",
                propsort: "",
                numssort: "",
                buypeoplesort: "",
            },
            repeatPurchaseList: [],
            total: 0,
            reqeat_purchase_visible: false
        };
    },

    mounted() {
        this.getRepurchasestatisticsList();
    },

    methods: {
        calcDecimal(value) {
            const num1 = new Decimal(value);
            return num1.times(100);
        },
        getRepurchasestatisticsList() {
            this.$request.repeatPurchaseRate
                .getRepurchasestatisticsList(this.query)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.repeatPurchaseList = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        search() {
            this.query.page = 1;
            this.getRepurchasestatisticsList();
        },
        lookUser(row) {
            this.reqeat_purchase_visible = true;
            this.$nextTick(() => {
                this.$refs.oneMoreUserRef.getRepurchasestatisticsUserlist(
                    row.period
                );
            });
        },
        handleSizeChange(limit) {
            this.query.page = 1;
            this.query.limit = limit;
            this.getRepurchasestatisticsList();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.getRepurchasestatisticsList();
        },
        handleSummary(param) {
            const { columns, data, rows } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = "合计";
                    return;
                }
                const nocount_index = [1, 2, 3];
                if (nocount_index.includes(index)) {
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                if (!values.every(value => isNaN(value))) {
                    sums[index] = values.reduce((prev, curr) => {
                        const value = Number(curr);
                        if (!isNaN(value)) {
                            return prev + curr;
                        } else {
                            return prev;
                        }
                    }, 0);
                    if (column.property === "sale_nums") {
                        sums[index] += "瓶";
                    }
                    if (column.property === "proportion") {
                        const dividend = new Decimal(sums[index]);
                        const divisor = new Decimal(data.length);
                        sums[index] =
                            dividend
                                .dividedBy(divisor)
                                .times(100)
                                .toFixed(2)
                                .toString() + "%";
                    }
                } else {
                    sums[index] = "";
                }
            });
            return sums;
        },
        changeTime() {
            if (this.time) {
                this.query.start_time = this.time[0];
                this.query.end_time = this.time[1];
            } else {
                this.query.start_time = "";
                this.query.end_time = "";
            }
            this.search();
        }
    }
};
</script>

<style lang="scss" scoped></style>
