<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <!-- <el-form-item>
                    <el-input
                        clearable
                        v-model="query.period"
                        placeholder="请输入期数"
                        @keyup.enter.native="search"
                    ></el-input>
                </el-form-item> -->
                <!-- <el-form-item>
                    <el-input
                        clearable
                        v-model="query.short_code"
                        placeholder="请输入简码"
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        clearable
                        v-model="query.product_name"
                        placeholder="请输入产品名称"
                    ></el-input>
                </el-form-item> -->
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top:10px">
            <el-table
                :data="repeatPurchaseList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column prop="period" label="期数"> </el-table-column>
                <el-table-column prop="short_code" label="简码">
                </el-table-column>
                <el-table-column
                    prop="product_names"
                    label="产品名称"
                    min-width="240px"
                >
                </el-table-column>
                <el-table-column prop="collection_nums" label="收藏人数">
                </el-table-column>
                <el-table-column prop="want_nums" label="点击想要人数">
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="{ row }">
                        <el-button
                            type="text"
                            size="mini"
                            @click="lookUser(row)"
                            >查看用户</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div> -->
        <el-dialog
            title="查看需求用户"
            :visible.sync="reqeat_purchase_visible"
            width="70%"
            :close-on-click-modal="false"
        >
            <requestUser
                ref="requestUserRef"
                v-if="reqeat_purchase_visible"
            ></requestUser>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="reqeat_purchase_visible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="reqeat_purchase_visible = false"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import requestUser from "./requestUser.vue";
export default {
    components: { requestUser },
    data() {
        return {
            query: {
                limit: 50,
                page: 1,
                short_code: "",
                period: "",
                product_name: ""
            },
            repeatPurchaseList: [],
            total: 0,
            reqeat_purchase_visible: false
        };
    },

    mounted() {
        this.getCollectionlist();
    },

    methods: {
        getCollectionlist() {
            this.$request.repeatPurchaseRate
                .getCollectionlist(this.query)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.repeatPurchaseList = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        search() {
            this.query.page = 1;
            this.getCollectionlist();
        },
        lookUser(row) {
            this.reqeat_purchase_visible = true;
            this.$nextTick(() => {
                this.$refs.requestUserRef.getCollectionUserlist(row);
            });
        },
        handleSizeChange(limit) {
            this.query.page = 1;
            this.query.limit = limit;
            this.getCollectionlist();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.getCollectionlist();
        }
    }
};
</script>

<style lang="scss" scoped></style>
