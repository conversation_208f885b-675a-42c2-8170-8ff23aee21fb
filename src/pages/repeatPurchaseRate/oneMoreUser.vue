<template>
    <div>
        <el-form :inline="true" size="mini">
            <!-- <el-form-item>
                <el-input
                    clearable
                    v-model="query.period"
                    placeholder="请输入期数"
                ></el-input>
            </el-form-item> -->
            <el-form-item>
                <el-input
                    clearable
                    v-model="query.uid"
                    placeholder="请输入用户ID"
                    @keyup.enter.native="search"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    clearable
                    v-model="query.user_phone"
                    placeholder="请输入用户手机号"
                    @keyup.enter.native="search"
                ></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">查询</el-button>
            </el-form-item>
        </el-form>
        <el-table
            :data="user_list"
            border
            size="mini"
            :header-cell-style="{ 'text-align': 'center' }"
            :cell-style="{ 'text-align': 'center' }"
        >
            <el-table-column label="期数" prop="period"> </el-table-column>
            <el-table-column label="用户ID" prop="uid"> </el-table-column>
            <el-table-column label="用户昵称" prop="nickname">
            </el-table-column>
            <el-table-column label="用户手机号" prop="user_phone">
            </el-table-column>
            <el-table-column label="提交时间" prop="create_time">
            </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: center">
            <el-pagination
                background
                style="margin-top: 10px; text-align: center"
                :page-sizes="[10, 30, 50, 100, 200]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="query.limit"
                :current-page="query.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
    </div>
</template>

<script>
export default {
    name: "Vue2MarketingRequestUser",

    data() {
        return {
            user_list: [],
            query: {
                short_codes: "",
                uid: "",
                period: "",
                user_phone: "",
                is_from: 1,
                page: 1,
                limit: 10
            },
            total: 0,
            periodcount: 0,
            uidcount: 0
        };
    },

    mounted() {},
    methods: {
        getRepurchasestatisticsUserlist(period) {
            if (period) {
                this.query.period = period;
            }
            this.$request.repeatPurchaseRate
                .getRepurchasestatisticsUserlist(this.query)
                .then(res => {
                    if (res.data.error_code === 0) {
                        this.user_list = res.data.data.list;
                        this.total = res.data.data.total;
                    }
                });
        },
        search() {
            this.query.page = 1;
            this.getRepurchasestatisticsUserlist();
        },
        handleSizeChange(limit) {
            this.query.page = 1;
            this.query.limit = limit;
            this.getRepurchasestatisticsUserlist();
        },
        handleCurrentChange(page) {
            this.query.page = page;
            this.getRepurchasestatisticsUserlist();
        }
    }
};
</script>

<style lang="scss" scoped></style>
