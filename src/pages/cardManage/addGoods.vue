<template>
    <div>
        <el-form :model="form" :rules="formRules" ref="ruleForm">
            <el-form-item
                label="商品期数"
                :label-width="formLabelWidth"
                prop="relation_id"
            >
                <el-input
                    v-model="form.relation_id"
                    placeholder="请输入商品期数"
                    onkeyup="this.value=this.value.replace(/\s+/g,'')"
                    style="width: 50%; margin-right: 20px"
                ></el-input>
                <el-button type="primary" @click="isExistRelationid"
                    >确定</el-button
                >
            </el-form-item>
            <el-form-item
                label="商品名称"
                :label-width="formLabelWidth"
                prop=""
                v-if="isGoodsName"
            >
                <el-input
                    v-model="goodsName"
                    placeholder="请输入商品名称"
                    style="width: 50%; margin-right: 20px"
                    disabled
                ></el-input>
            </el-form-item>
            <!-- 横向移动 -->
            <div v-show="goodsManageData.pattern == 0">
                <el-form-item
                    v-show="
                        goodsManageData.style == 2 &&
                        goodsManageData.channel != 2
                    "
                    label="主标题"
                    :label-width="formLabelWidth"
                    prop="title"
                >
                    <el-input
                        v-model="form.title"
                        autocomplete="off"
                        placeholder="请输入主标题"
                        maxlength="7"
                        show-word-limit
                        style="width: 50%; margin-right: 20px"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    v-show="goodsManageData.page_mode == 1"
                    label="筛选项"
                    :label-width="formLabelWidth"
                    prop="filter_id"
                >
                    <el-select
                        v-model="form.filter_id"
                        filterable
                        placeholder="请输入筛选项"
                    >
                        <el-option
                            v-for="item in filterOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    v-show="goodsManageData.style == 3"
                    label="主标题"
                    :label-width="formLabelWidth"
                    prop="title"
                >
                    <el-input
                        v-model="form.title"
                        autocomplete="off"
                        placeholder="请输入主标题"
                        maxlength="24"
                        show-word-limit
                        style="width: 50%; margin-right: 20px"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    v-show="
                        goodsManageData.style == 0 || goodsManageData.style == 1
                    "
                    label="主标题"
                    :label-width="formLabelWidth"
                    prop="title"
                >
                    <el-input
                        v-model="form.title"
                        autocomplete="off"
                        placeholder="请输入主标题"
                        maxlength="10"
                        show-word-limit
                        style="width: 50%; margin-right: 20px"
                    ></el-input>
                </el-form-item>
            </div>
            <!-- 平铺展示 -->
            <div v-show="goodsManageData.pattern == 1">
                <div
                    v-show="
                        goodsManageData.style == 4 || goodsManageData.style == 5
                    "
                >
                    <el-form-item
                        label="主标题"
                        :label-width="formLabelWidth"
                        prop="title"
                    >
                        <el-input
                            v-model="form.title"
                            autocomplete="off"
                            placeholder="请输入主标题"
                            maxlength="8"
                            show-word-limit
                            style="width: 50%; margin-right: 20px"
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        label="标签文字"
                        :label-width="formLabelWidth"
                        prop="sub_title"
                    >
                        <el-input
                            v-model="form.sub_title"
                            autocomplete="off"
                            placeholder="请输入标签文字"
                            maxlength="4"
                            show-word-limit
                            style="width: 50%; margin-right: 20px"
                        ></el-input>
                    </el-form-item>
                </div>
                <!-- <div v-show="goodsManageData.style == 7">
                    <el-form-item
                        label="主标题"
                        :label-width="formLabelWidth"
                        prop="card_name"
                    >
                        <el-input
                            v-model="form.card_name"
                            autocomplete="off"
                            placeholder="请输入主标题"
                            maxlength="20"
                            show-word-limit
                            style="width:50%;margin-right:20px"
                        ></el-input>
                    </el-form-item>
                </div> -->
                <div v-show="goodsManageData.style == 6">
                    <el-form-item
                        label="主标题"
                        :label-width="formLabelWidth"
                        prop="title"
                    >
                        <el-input
                            v-model="form.title"
                            autocomplete="off"
                            placeholder="请输入主标题"
                            maxlength="8"
                            show-word-limit
                            style="width: 50%; margin-right: 20px"
                        ></el-input>
                    </el-form-item>
                </div>
                <el-form-item
                    label="商品图片"
                    :label-width="formLabelWidth"
                    prop="image"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                    <span
                        v-show="
                            goodsManageData.style == 4 ||
                            goodsManageData.style == 5
                        "
                    >
                        <!-- 注意图片比例为:72*176 -->
                        注意图片需为正方形
                    </span>
                </el-form-item>
            </div>

            <el-form-item
                label="排序"
                prop="sort"
                :label-width="formLabelWidth"
            >
                <el-input-number
                    v-model="form.sort"
                    controls-position="right"
                    @change="handleChange"
                    :min="1"
                ></el-input-number>
            </el-form-item>

            <el-form-item style="margin-top: 50px; margin-left: 40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss,
    },
    props: ["goodsManageData"],
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的数字"));
            } else {
                callback();
            }
        };
        const checkTitileMap2 = (rules, value, callback) => {
            if (
                this.icon_map.length == 0 &&
                this.goodsManageData.pattern != 0
            ) {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        const checkSub_title = (rules, value, callback) => {
            if (
                this.goodsManageData.pattern != 0 &&
                this.goodsManageData.style != 6 &&
                this.form.sub_title == ""
            ) {
                callback(new Error("请输入标签文字"));
            } else {
                callback();
            }
        };
        const checekTitle = (rules, value, callback) => {
            if (
                this.goodsManageData.style == 2 &&
                this.goodsManageData.channel != 2 &&
                this.form.title == ""
            ) {
                callback(new Error("请输入主标题"));
            } else {
                callback();
            }
        };
        const checkFilter = (rules, value, callback) => {
            if (this.goodsManageData.page_mode == 1 && !this.form.filter_id) {
                callback(new Error("请选择筛选项"));
            } else {
                callback();
            }
        };
        return {
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            form: {
                channel: "1",
                relation_id: "",
                title: "",
                sub_title: "",
                image: "",
                sort: 1,
                filter_id: "",
            },
            goodsName: "",
            isGoodsName: false,
            filterOptions: [],
            // options: [],
            // value: [],
            formRules: {
                image: [
                    {
                        required: true,
                        validator: checkTitileMap2,
                    },
                ],
                title: [
                    {
                        required: true,
                        validator: checekTitle,
                        trigger: "blur",
                    },
                ],
                sub_title: [
                    {
                        required: true,
                        validator: checkSub_title,
                        trigger: "blur",
                    },
                ],
                filter_id: [
                    {
                        required: true,
                        validator: checkFilter,
                        trigger: "blur",
                    },
                ],
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur",
                    },
                ],
                relation_id: [
                    {
                        required: true,
                        message: "请输入商品期数",
                        trigger: "blur",
                    },
                ],
                sort: [
                    {
                        required: true,
                        validator: weightValidator,
                        trigger: "blur",
                    },
                ],
            },
            formLabelWidth: "120px",
        };
    },
    // watch: {
    //     "form.channel": {
    //         handler(newVal, oldVal) {
    //             console.log("改变的频道", newVal);
    //             this.form.goodsNper = "";
    //         }
    //     }
    // },
    mounted() {
        if (this.goodsManageData.page_mode == 1) {
            this.getFilterListReq();
        }
    },
    methods: {
        closeDiog() {
            this.$emit("close");
            this.$emit("getCardGoodsList");
        },
        getFilterListReq() {
            this.$request.cardManage
                .getFilterList({
                    id: this.goodsManageData.id,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.filterOptions = res.data.data;
                    }
                });
        },
        //商品期数是否存在
        async isExistRelationid() {
            let data = {
                periods: this.form.relation_id,
            };
            console.log("商品期数", data);
            let res = await this.$request.cardManage.isExistRelationid(data);
            console.log("商品期数是否存在", res);
            if (res.data.error_code == 0) {
                if (res.data.data.list.length == 0) {
                    this.$message.error("商品期数不存在");
                    this.isGoodsName = false;
                } else {
                    this.$message.success("查找成功");
                    this.goodsName = res.data.data.list[0].brief;
                    this.icon_map = [res.data.data.list[0].product_img_arr[0]];
                    this.isGoodsName = true;
                    if (
                        this.goodsManageData.style == 2 &&
                        this.goodsManageData.channel == 2
                    ) {
                        this.form.title = res.data.data.list[0].title;
                    }
                }
            }
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        },
        submitForm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    let img = "";
                    if (this.goodsManageData.pattern == 0) {
                        img = "";
                    } else {
                        img = this.icon_map.join(",");
                    }
                    let data = {
                        channel: 1,
                        relation_id: parseInt(this.form.relation_id),
                        title: this.form.title,
                        sub_title: this.form.sub_title,
                        image: img,
                        type: 1,
                        sort: this.form.sort,
                        cid: this.goodsManageData.id,
                        filter_id:
                            this.goodsManageData.page_mode == 1
                                ? [this.form.filter_id]
                                : [],
                    };
                    console.log("form表单", data);
                    this.$request.cardManage
                        .addCardGoodsLive(data)
                        .then((res) => {
                            console.log("添加商品管理", res);
                            if (res.data.error_code == 0) {
                                this.$message.success("添加成功");
                                // this.$emit("close");
                                this.closeDiog();
                            }
                        });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
    },
};
</script>

<style></style>
