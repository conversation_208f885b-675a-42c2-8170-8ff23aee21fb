<template>
    <div>
        <div class="order-form">
            <el-card>
                <!-- 筛选项 -->
                <el-select
                    v-show="goodsManageData.page_mode == 1"
                    class="m-r-10"
                    v-model="pageAttr.filter_id"
                    filterable
                    multiple
                    clearable=""
                    size="mini"
                    placeholder="筛选项"
                >
                    <el-option
                        v-for="item in filterOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>

                <!-- 商品状态筛选 -->
                <el-select
                    class="m-r-10"
                    v-model="pageAttr.onsale_status"
                    multiple
                    clearable
                    size="mini"
                    placeholder="商品状态"
                >
                    <el-option label="待上架" :value="0" />
                    <el-option label="待售中" :value="1" />
                    <el-option label="在售中" :value="2" />
                    <el-option label="已下架" :value="3" />
                    <el-option label="已售罄" :value="4" />
                </el-select>

                <!-- 添加类型筛选 -->
                <el-select
                    class="m-r-10"
                    v-model="pageAttr.add_type"
                    multiple
                    clearable
                    size="mini"
                    placeholder="添加类型"
                >
                    <el-option label="手动" :value="0" />
                    <el-option label="自动" :value="1" />
                </el-select>

                <el-button
                    class="m-r-10"
                    type="primary"
                    size="mini"
                    @click="search"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="addGoodsDialog = true"
                    >添加商品</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="期数"
                        prop="relation_id"
                        min-width="80"
                    >
                    </el-table-column>
                    <!-- <el-table-column
                        align="center"
                        label="频道"
                        prop="channel"
                        min-width="80"
                    >
                        <template #default="row">
                            <span>{{
                                `${row.row.channel == 1 ? "闪购" : ""}
                               ${row.row.channel == 2 ? "秒发" : ""} 
                               ${row.row.channel == 3 ? "跨境" : ""}
                               ${row.row.channel == 4 ? "尾货" : ""}`
                            }}</span>
                        </template>
                    </el-table-column> -->
                    <el-table-column
                        align="center"
                        label="商品名称"
                        prop="title"
                        min-width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="图片"
                        prop="image"
                        min-width="150"
                    >
                        <template #default="image">
                            <el-image
                                style="width: 50px; height: 50px"
                                :src="image.row.image"
                                fit="cover"
                            ></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="筛选项"
                        prop="filter"
                        min-width="120"
                        v-if="goodsManageData.page_mode == 1"
                    >
                        <template #default="scope">
                            <span>{{
                                scope.row.filter.length
                                    ? scope.row.filter[0].name
                                    : ""
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="排序"
                        prop="sort"
                        min-width="80"
                    >
                        <template slot-scope="{ row }">
                            <input
                                class="sort_input"
                                type="text"
                                v-model="row.sort"
                                oninput="value=value.replace(/[^0-9]/g,'')"
                                @change="updateFlashSaleSort(row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="商品状态"
                        min-width="100"
                        prop="status"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.status | onsaleStatsText }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="add_type"
                        align="center"
                        label="添加类型"
                        min-width="100"
                    >
                        <template #default="status">
                            <span>{{
                                status.row.add_type == 0 ? "手动" : "自动"
                            }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="operation"
                        label="操作"
                        fixed="right"
                        min-width="60"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                type="text"
                                size="mini"
                                slot="reference"
                                @click="edit(row)"
                                style="margin: 0 10px"
                                >编辑</el-button
                            >
                            <el-popconfirm
                                title="确定删除吗？"
                                @confirm="deleteConfirm(row)"
                            >
                                <el-button
                                    type="text"
                                    size="mini"
                                    slot="reference"
                                    >删除</el-button
                                >
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>

        <!-- 新增 -->
        <div>
            <el-dialog
                width="40%"
                title="添加商品信息"
                :visible.sync="addGoodsDialog"
                :before-close="close"
                append-to-body
            >
                <AddGoods
                    ref="addGoods"
                    v-if="addGoodsDialog"
                    @close="close"
                    :goodsManageData="goodsManageData"
                ></AddGoods>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                width="40%"
                title="编辑商品信息"
                :visible.sync="editGoodsDialog"
                :before-close="editGoodsClose"
                append-to-body
            >
                <EditGoods
                    ref="editGoods"
                    v-if="editGoodsDialog"
                    @editGoodsClose="editGoodsClose"
                    :goodsManageData="goodsManageData"
                    :rowData="rowData"
                ></EditGoods>
            </el-dialog>
        </div>

        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import AddGoods from "./addGoods.vue";
import EditGoods from "./editGoods.vue";
export default {
    components: { AddGoods, EditGoods },
    props: ["goodsManageData"],
    data() {
        return {
            rowData: {},
            addGoodsDialog: false,
            editGoodsDialog: false,
            tableData: [],
            pageAttr: {
                page: 1,
                limit: 10,
                filter_id: [],
                onsale_status: [],
                add_type: [],
            },
            total: 0,
            filterOptions: [],
        };
    },
    filters: {
        onsaleStatsText(val) {
            switch (val) {
                case 0:
                    return "待上架";
                case 1:
                    return "待售中";
                case 2:
                    return "在售中";
                case 3:
                    return "已下架";
                case 4:
                    return "已售罄";
                default:
                    return "-";
            }
        },
    },
    mounted() {
        if (this.goodsManageData.page_mode == 1) {
            this.getFilterListReq();
        }
        this.getCardGoodsList();
    },
    methods: {
        //获取商品管理列表
        async getCardGoodsList() {
            let data = {
                page: this.pageAttr.page,
                limit: this.pageAttr.limit,
                cid: this.goodsManageData.id,
                type: 1,
                // GET请求使用英文半角逗号分隔数组参数
                filter_id: this.pageAttr.filter_id,
                onsale_status: this.pageAttr.onsale_status,
                add_type: this.pageAttr.add_type,
            };
            let res = await this.$request.cardManage.getCardGoodsList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
            console.log("商品管理列表", res);
        },
        //查询
        search() {
            this.pageAttr.page = 1;
            this.getCardGoodsList();
        },
        //获取筛选项列表
        getFilterListReq() {
            this.$request.cardManage
                .getFilterList({
                    id: this.goodsManageData.id,
                })
                .then((res) => {
                    if (res.data.error_code == 0) {
                        this.filterOptions = res.data.data;
                    }
                });
        },
        deleteConfirm(row) {
            console.log("11111", row);
            let data = {
                id: row.row.id,
            };
            this.$request.cardManage.deleteCardGoodsLive(data).then((res) => {
                console.log("删除商品", res);
                if (res.data.error_code == 0) {
                    this.getCardGoodsList();
                }
            });
        },
        //更新排序
        async updateFlashSaleSort(row) {
            let data = {
                id: row.id,
                sort: row.sort,
            };
            let res = await this.$request.cardManage.updateSort(data);
            console.log("更新排序", res);
            if (res.data.error_code == 0) {
                this.$Message.success("更新成功");
                this.getCardGoodsList();
            }
        },
        edit(row) {
            this.rowData = row.row;
            this.editGoodsDialog = true;
        },
        editGoodsClose() {
            this.editGoodsDialog = false;
            this.getCardGoodsList();
        },
        close() {
            this.addGoodsDialog = false;
            this.getCardGoodsList();
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getCardGoodsList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getCardGoodsList();
        },
    },
};
</script>

<style lang="scss" scoped>
.m-r-10 {
    margin-right: 10px;
}
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
.sort_input {
    -webkit-appearance: none;
    appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 30px;
    line-height: 30px;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    text-align: center;
}
</style>
