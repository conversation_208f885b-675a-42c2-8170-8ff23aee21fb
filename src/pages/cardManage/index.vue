<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="channel"
                    filterable
                    size="mini"
                    placeholder="请选择频道"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in objectOptions"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10 w-mini"
                    style="width: 150px"
                    v-model="status"
                    filterable
                    size="mini"
                    placeholder="请选择卡片状态"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in cardStatus"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10 w-mini"
                    style="width: 150px"
                    v-model="pattern"
                    filterable
                    size="mini"
                    placeholder="请选择模式"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in patternOptions"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-model="card_name"
                    autocomplete="off"
                    @keyup.enter.native="search"
                    placeholder="请输入卡片标题"
                    style="width: 150px; margin-right: 10px"
                    size="mini"
                ></el-input>

                <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                <el-button type="primary" size="mini" @click="search()"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >添加卡片</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="频道"
                        prop="channel"
                        min-width="60"
                    >
                        <template #default="row">
                            <span>{{
                                `${row.row.channel == 0 ? "首页" : ""}
                               ${row.row.channel == 2 ? "秒发" : ""}`
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="模式"
                        min-width="80"
                        prop="pattern"
                    >
                        <template #default="row">
                            <span>{{
                                row.row.pattern == 0 ? "横向滑动" : "平铺展示"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="样式"
                        width="100"
                        prop="style"
                    >
                        <template #default="row">
                            <span>{{
                                `${row.row.style == 0 ? "样式1" : ""}  
                                ${row.row.style == 1 ? "样式2" : ""}
                                ${row.row.style == 2 ? "样式3" : ""} 
                                ${row.row.style == 3 ? "样式4" : ""} 
                                ${row.row.style == 4 ? "特卖" : ""} 
                                ${row.row.style == 5 ? "秒杀" : ""} 
                                ${row.row.style == 6 ? "拼团" : ""}
                                ${row.row.style == 7 ? "直播" : ""}  `
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="卡片标题"
                        prop="card_name"
                        min-width="100"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="sub_title"
                        align="center"
                        label="副标题"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="展示模式"
                        width="70"
                        prop="page_mode"
                    >
                        <template #default="row">
                            <span>{{
                                `${row.row.page_mode == 1 ? "筛选" : "浏览"}  
                                `
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="排序"
                        min-width="80"
                        prop="sort"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="创建时间"
                        min-width="100"
                        prop="created_at"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="操作人"
                        min-width="80"
                        prop="operator_name"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="卡片状态"
                        width="70"
                        prop="status"
                    >
                        <template #default="row">
                            <span>{{
                                `${row.row.status == 0 ? "禁用" : "启用"}  
                                `
                            }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="operation"
                        label="操作"
                        fixed="right"
                        width="260"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                v-if="row.row.style == 7"
                                type="text"
                                size="mini"
                                @click="liveManage(row.row)"
                                >直播管理</el-button
                            >
                            <el-button
                                v-else
                                type="text"
                                size="mini"
                                @click="goodsManage(row.row)"
                                >商品管理</el-button
                            >
                            <!-- <el-button
                                v-if="row.row.style == 2&&row.row.channel ==2"
                                type="text"
                                size="mini"
                                @click="filterSet(row.row)"
                                >筛选项</el-button
                            > -->
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                            <el-popconfirm
                                confirm-button-text="确定"
                                cancel-button-text="取消"
                                title="确定改变状态吗？"
                                @confirm="
                                    updateEnable(
                                        row.row,
                                        row.row.status == 0 ? '1' : '0'
                                    )
                                "
                            >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    v-if="row.row.status == 0"
                                    type="text"
                                    style="margin-left: 10px"
                                    >启用</el-button
                                >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    type="text"
                                    v-if="row.row.status == 1"
                                    style="margin-left: 10px"
                                    >禁用</el-button
                                >
                            </el-popconfirm>
                            <el-button
                                @click="deleteCard(row.row)"
                                type="text"
                                size="mini"
                                style="margin-left: 10px"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 商品列表 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="商品列表"
                :visible.sync="GoodsListDialog"
                width="60%"
            >
                <GoodsList
                    v-if="GoodsListDialog"
                    :goodsManageData="goodsManageData"
                ></GoodsList>
            </el-dialog>
        </div>
        <!-- 直播列表 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="直播列表"
                :visible.sync="liveListDialog"
                width="60%"
            >
                <LiveList
                    v-if="liveListDialog"
                    :liveManageData="liveManageData"
                ></LiveList>
            </el-dialog>
        </div>
        <!-- 新增卡片 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="新增卡片"
                :visible.sync="dialogStatus"
                width="40%"
                style="margin-top: 1%"
            >
                <div style="overflow-y: auto">
                    <Add
                        ref="addCard"
                        v-if="dialogStatus"
                        @close="close"
                        @getCardManageList="getCardManageList"
                    ></Add>
                </div>

                <!-- <div slot="footer" class="dialog-footer">
                    <el-button @click="dialogStatus = false">取 消</el-button>
                    <el-button type="primary" @click="submitForm()"
                        >确 定</el-button
                    >
                </div> -->
            </el-dialog>
        </div>
        <!-- 编辑卡片 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑卡片"
                :visible.sync="viewDialogStatus"
                width="40%"
                :before-close="closeViewDialogStatus"
            >
                <div style="overflow-y: auto">
                    <Views
                        :rowData="rowData"
                        v-if="viewDialogStatus"
                        ref="viewCard"
                        @closeViewDialogStatus="closeViewDialogStatus"
                        @getCardManageList="getCardManageList"
                    ></Views>
                </div>

                <!-- <div slot="footer" class="dialog-footer">
                    <el-button @click="viewDialogStatus = false"
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="viewSubmitForm()"
                        >确 定</el-button
                    >
                </div> -->
            </el-dialog>
        </div>

        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="子项管理"
                :visible.sync="filterViewDialogStatus"
                width="40%"
            >
                <!-- :before-close="closeViewDialogStatus" -->
                <el-button type="primary" size="defualt" @click="addType"
                    >添加</el-button
                >
                <draggable v-model="filterList">
                    <div
                        v-for="(item, index) in filterList"
                        :key="item.key"
                        style="margin-top: 10px"
                    >
                        <el-input
                            v-model="item.name"
                            placeholder="请输入"
                            class="w-normal m-r-10"
                            size="mini"
                            show-word-limit
                        ></el-input>
                        <el-button
                            type="danger"
                            size="mini"
                            class="m-r-10"
                            @click="deleteFilter(item, index)"
                            >删除</el-button
                        >
                    </div>
                </draggable>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="filterViewDialogStatus = false"
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="filterSubmitForm()"
                        >确 定</el-button
                    >
                </div>
            </el-dialog>
        </div>
        <!-- 分页 -->
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import GoodsList from "./goodsList.vue";
import LiveList from "./liveList.vue";
import Views from "./view.vue";
import draggable from "vuedraggable";
export default {
    components: { Add, GoodsList, LiveList, Views, draggable },

    data() {
        return {
            rowData: {},
            tableData: [],
            objectOptions: [
                {
                    id: 0,
                    name: "首页",
                },
                {
                    id: 2,
                    name: "秒发",
                },
            ],
            cardStatus: [
                {
                    id: 0,
                    name: "禁用",
                },
                {
                    id: 1,
                    name: "启用",
                },
            ],
            patternOptions: [
                {
                    id: 0,
                    name: "横向滑动",
                },
                {
                    id: 1,
                    name: "平铺展示",
                },
            ],
            channel: "",
            status: "",
            card_name: "",
            pattern: "",
            dialogStatus: false,
            GoodsListDialog: false,
            liveListDialog: false,
            viewDialogStatus: false,
            pageAttr: {
                page: 1,
                limit: 10,
            },
            filterList: [],
            filterViewDialogStatus: false,
            total: 0,
            goodsManageData: {},
            liveManageData: {},
        };
    },
    mounted() {
        // this.getChannel();
        this.getCardManageList();
    },
    methods: {
        goodsManage(row) {
            console.log("商品管理一条", row);
            this.goodsManageData = row;
            this.GoodsListDialog = true;
        },
        liveManage(row) {
            console.log("直播管理一条", row);
            this.liveManageData = row;
            this.liveListDialog = true;
        },
        //获取频道
        // async getChannel() {
        //     let res = await this.$request.KingArea.getChannel({ type: 2 });
        //     console.log("频道", res);
        //     if (res.data.error_code == 0) {
        //         this.objectOptions = res.data.data;
        //     }
        // },
        //获取卡片管理列表
        async getCardManageList() {
            let res = await this.$request.cardManage.getCardManageList({
                page: this.pageAttr.page,
                limit: this.pageAttr.limit,
                channel: this.channel,
                status: this.status,
                card_name: this.card_name,
                pattern: this.pattern,
            });
            console.log("卡片管理列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        //查询
        search() {
            this.pageAttr.page = 1;
            this.getCardManageList();
        },
        //删除卡片
        deleteCard(row) {
            this.$confirm(
                "此操作将会删除卡片内所有信息，确认要删除吗？",
                "提示",
                {
                    confirmButtonText: "确定",
                    type: "warning",
                }
            )
                .then(() => {
                    this.$request.cardManage
                        .deleteCard({
                            id: row.id,
                        })
                        .then((res) => {
                            console.log("删除", res);
                            if (res.data.error_code == 0) {
                                this.$message.success("删除成功");
                                this.getCardManageList();
                            }
                        });
                })
                .catch(() => {
                    console.log("取消");
                });
        },
        addType() {
            this.filterList.push({
                id: 0,
                name: "",
                key: new Date().getTime(),
            });
        },
        //筛选项
        filterSet(row) {
            this.filterViewDialogStatus = true;
            this.filterList = row.filter.map((item) => ({
                ...item,
                key: new Date().getTime(),
            }));
            this.rowData = row;
        },
        deleteFilter(item, index) {
            console.log(item);

            this.filterList.splice(index, 1);
        },
        filterSubmitForm() {
            var data = {
                id: this.rowData.id,
                channel: this.rowData.channel,
                card_name: this.rowData.card_name,
                card_class: this.rowData.card_class,
                sub_title: this.rowData.sub_title,
                sub_title_back: this.rowData.sub_title_back,
                pattern: this.rowData.pattern,
                style: this.rowData.style,
                sort: this.rowData.sort,
                goods_sort_type: this.rowData.goods_sort_type,
                add_method: this.rowData.add_method,
                auto_add_type: this.rowData.auto_add_type,
                auto_add_content: this.rowData.auto_add_content,
                filter: this.filterList.filter(
                    (item) => item.name && item.name.trim() !== ""
                ),
            };

            console.log("--表单", data);
            this.$request.cardManage.editCardManage(data).then((res) => {
                console.log("111111111", res);
                if (res.data.error_code == 0) {
                    console.log(9999);
                    this.filterViewDialogStatus = false;
                    this.getCardManageList();
                }
            });
        },
        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getCardManageList();
        },
        //查看编辑弹框
        view(row) {
            console.log("卡片管理编辑一条", row);
            this.viewDialogStatus = true;
            this.rowData = row;
        },
        //关闭新增弹框
        close() {
            this.dialogStatus = false;
        },
        //禁用或者启用
        async updateEnable(row, status) {
            let data = {
                id: row.id,
                status: status,
            };
            const res = await this.$request.cardManage.updateStatus(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                // this.$message.success("操作成功");
                this.getCardManageList();
            }
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getCardManageList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getCardManageList();
        },
    },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
