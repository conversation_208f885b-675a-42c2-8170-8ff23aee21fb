<template>
    <div>
        <el-form :model="form" :rules="formRules" ref="ruleForm">
            <el-form-item
                label="直播ID"
                :label-width="formLabelWidth"
                prop="liveId"
            >
                <el-input
                    v-model="form.relation_id"
                    autocomplete="off"
                    placeholder="请输入直播ID"
                    onkeyup="this.value=this.value.replace(/\s+/g,'')"
                    style="width:45%;margin-right:20px"
                    disabled
                ></el-input>
                <!-- <el-button type="primary" @click="isExistRelationid"
                    >确定</el-button
                > -->
            </el-form-item>
            <el-form-item
                label="直播名称"
                :label-width="formLabelWidth"
                prop=""
                v-if="isLiveName"
            >
                <el-input
                    v-model="title"
                    autocomplete="off"
                    placeholder="请输入直播名称"
                    style="width:45%;margin-right:20px
                    
                "
                    disabled
                ></el-input>
            </el-form-item>
            <el-form-item
                label="主标题"
                :label-width="formLabelWidth"
                prop="title"
            >
                <el-input
                    v-model="form.title"
                    autocomplete="off"
                    placeholder="请输入主标题"
                    maxlength="20"
                    show-word-limit
                    style="width:45%;margin-right:20px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="排序"
                prop="sort"
                :label-width="formLabelWidth"
            >
                <el-input-number
                    v-model="form.sort"
                    controls-position="right"
                    @change="handleChange"
                    :min="1"
                ></el-input-number>
            </el-form-item>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["liveManageData", "rowData"],
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的数字"));
            } else {
                callback();
            }
        };
        return {
            title: "",
            isLiveName: false,
            form: {
                relation_id: "",
                title: "",
                sort: 1
            },
            formRules: {
                title: [
                    {
                        required: true,
                        message: "请输入主标题",
                        trigger: "blur"
                    }
                ],
                relation_id: [
                    {
                        required: true,
                        message: "请输入直播ID",
                        trigger: "blur"
                    }
                ],
                sort: [
                    {
                        required: true,
                        validator: weightValidator,
                        trigger: "blur"
                    }
                ]
            },
            formLabelWidth: "120px"
        };
    },
    mounted() {
        console.log(this.rowData, this.liveManageData);
        this.form = this.rowData;
        if (this.form.relation_id) {
            this.isExistRelationid();
        }
    },
    methods: {
        closeDiog() {
            this.$emit("editClose");
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        },
        async isExistRelationid() {
            let data = {
                id: this.form.relation_id
            };
            console.log("直播id", data);
            let res = await this.$request.cardManage.isExistLiveId(data);
            console.log("直播id是否存在", res);
            if (res.data.error_code == 0) {
                if (res.data.data.list.length == 0) {
                    this.$Message.error("直播ID不存在");
                } else {
                    this.title = res.data.data.list[0].title;
                    this.isLiveName = true;
                    // this.$Message.success("直播ID查找成功");
                }
            } else {
                this.$Message.error(res.data.error_msg);
            }
        },
        submitForm() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    let data = {
                        id: this.form.id,
                        title: this.form.title,
                        sub_title: "",
                        image: "",
                        sort: this.form.sort
                    };
                    console.log("form表单", data);
                    this.$request.cardManage.updateSort(data).then(res => {
                        console.log("编辑直播管理", res);
                        if (res.data.error_code == 0) {
                            this.$message.success("编辑成功");
                            this.closeDiog();
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
