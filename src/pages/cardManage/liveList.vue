<template>
    <div>
        <div class="order-form">
            <el-card>
                <el-button
                    type="success"
                    size="mini"
                    @click="addLiveDialog = true"
                    >添加直播</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="直播ID"
                        prop="relation_id"
                        min-width="80"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="直播名称"
                        prop="title"
                        min-width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="直播开始时间"
                        prop="start_time"
                        min-width="150"
                    >
                    </el-table-column>

                    <el-table-column
                        align="center"
                        label="排序"
                        prop="sort"
                        min-width="80"
                    >
                        <template slot-scope="{ row }">
                            <input
                                class="sort_input"
                                type="text"
                                v-model="row.sort"
                                oninput="value=value.replace(/[^0-9]/g,'')"
                                @change="updateFlashSaleSort(row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="状态"
                        prop="status"
                        min-width="80"
                    >
                        <template #default="row">
                            <span>
                                {{
                                    `${row.row.status == 1 ? "已创建直播" : ""}
                                ${row.row.status == 2 ? "直播中" : ""}
                                ${row.row.status == 3 ? "已结束" : ""}
                                ${row.row.status == 4 ? "已删除" : ""}`
                                }}
                            </span>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="operation"
                        label="操作"
                        fixed="right"
                        min-width="60"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                type="text"
                                size="mini"
                                slot="reference"
                                @click="edit(row)"
                                style="margin:0 10px"
                                >编辑</el-button
                            >
                            <el-popconfirm
                                title="确定删除吗？"
                                @confirm="deleteConfirm(row)"
                            >
                                <el-button
                                    type="text"
                                    size="mini"
                                    slot="reference"
                                    >删除</el-button
                                >
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                width="40%"
                title="新增直播信息"
                :visible.sync="addLiveDialog"
                :before-close="close"
                append-to-body
            >
                <AddLive
                    ref="AddLive"
                    v-if="addLiveDialog"
                    @close="close"
                    :liveManageData="liveManageData"
                ></AddLive>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                width="40%"
                title="编辑直播信息"
                :visible.sync="editLiveDialog"
                :before-close="editClose"
                append-to-body
            >
                <EditLive
                    ref="EditLive"
                    v-if="editLiveDialog"
                    @editClose="editClose"
                    :liveManageData="liveManageData"
                    :rowData="rowData"
                ></EditLive>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
import AddLive from "./addLive.vue";
import EditLive from "./editLive.vue";
export default {
    components: { AddLive, EditLive },
    props: ["liveManageData"],
    data() {
        return {
            rowData: {},
            addLiveDialog: false,
            editLiveDialog: false,
            tableData: [],
            pageAttr: {
                page: 1,
                limit: 10
            },
            total: 0
        };
    },
    mounted() {
        this.getCardGoodsList();
    },
    methods: {
        //获取直播管理列表
        async getCardGoodsList() {
            let data = {
                page: this.pageAttr.page,
                limit: this.pageAttr.limit,
                cid: this.liveManageData.id,
                type: 2
            };
            let res = await this.$request.cardManage.getCardGoodsList(data);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
            console.log("直播管理列表", res);
        },
        //更新排序
        async updateFlashSaleSort(row) {
            let data = {
                id: row.id,
                sort: row.sort
            };
            let res = await this.$request.cardManage.updateSort(data);
            console.log("更新排序", res);
            if (res.data.error_code == 0) {
                this.$Message.success("更新成功");
                this.getCardGoodsList();
            }
        },
        submitForm() {
            this.$refs.AddLive.submitForm();
        },
        edit(row) {
            this.rowData = row.row;
            this.editLiveDialog = true;
        },
        editClose() {
            this.editLiveDialog = false;
            this.getCardGoodsList();
        },
        close() {
            this.addLiveDialog = false;
            this.getCardGoodsList();
        },
        deleteConfirm(row) {
            console.log("222222", row);
            let data = {
                id: row.row.id
            };
            this.$request.cardManage.deleteCardGoodsLive(data).then(res => {
                if (res.data.error_code == 0) {
                    this.getCardGoodsList();
                }
            });
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getCardGoodsList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getCardGoodsList();
        }
    }
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
}
.sort_input {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 30px;
    line-height: 30px;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    text-align: center;
}
</style>
