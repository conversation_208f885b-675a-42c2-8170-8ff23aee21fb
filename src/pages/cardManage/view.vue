<template>
    <div>
        <el-form :model="form" :rules="formRules" ref="ruleForm">
            <el-form-item
                label="频道"
                :label-width="formLabelWidth"
                prop="channel"
            >
                <el-radio disabled v-model="form.channel" label="0"
                    >首页</el-radio
                >
                <el-radio disabled v-model="form.channel" label="2"
                    >秒发</el-radio
                >
            </el-form-item>
            <el-form-item
                label="模式"
                :label-width="formLabelWidth"
                prop="pattern"
            >
                <el-radio v-model="form.pattern" label="0" disabled
                    >横向移动</el-radio
                >
                <el-radio v-model="form.pattern" label="1" disabled
                    >平铺展示</el-radio
                >
            </el-form-item>
            <el-form-item
                label="样式"
                :label-width="formLabelWidth"
                prop="style"
            >
                <!-- 横向滑动 -->
                <div v-show="form.pattern == 0">
                    <el-radio v-model="form.style" label="0" disabled
                        >样式1</el-radio
                    >
                    <el-radio v-model="form.style" label="1" disabled
                        >样式2</el-radio
                    >
                    <el-radio v-model="form.style" label="2" disabled
                        >样式3</el-radio
                    >
                    <el-radio v-model="form.style" label="3" disabled
                        >样式4</el-radio
                    >
                </div>
                <!-- 平铺展示 -->
                <div v-show="form.pattern == 1">
                    <el-radio v-model="form.style" label="4" disabled
                        >特卖</el-radio
                    >
                    <el-radio v-model="form.style" label="5" disabled
                        >秒杀</el-radio
                    >
                    <el-radio v-model="form.style" label="6" disabled
                        >拼团</el-radio
                    >
                    <el-radio v-model="form.style" label="7" disabled
                        >直播</el-radio
                    >
                </div>
            </el-form-item>
            <el-form-item
                label="排序"
                :label-width="formLabelWidth"
                prop="goods_sort_type"
            >
                <!-- <el-input v-model="form.name" autocomplete="off"></el-input> -->
                <el-radio v-model="form.goods_sort_type" label="0"
                    >添加时间倒序</el-radio
                >
                <el-radio v-model="form.goods_sort_type" label="1"
                    >上架时间倒序</el-radio
                >
                <el-radio v-model="form.goods_sort_type" label="2"
                    >随机</el-radio
                >
            </el-form-item>
            <el-form-item
                label="展示模式"
                prop="page_mode"
                :label-width="formLabelWidth"
            >
                <el-radio disabled v-model="form.page_mode" :label="0"
                    >浏览</el-radio
                >
                <el-radio disabled v-model="form.page_mode" :label="1"
                    >筛选</el-radio
                >
            </el-form-item>
            <!-- 横向滑动标题、副标题 -->
            <div v-show="form.pattern == 0">
                <el-form-item
                    label="卡片标题"
                    :label-width="formLabelWidth"
                    prop="card_name"
                >
                    <el-input
                        v-model="form.card_name"
                        autocomplete="off"
                        placeholder="请输入卡片标题"
                        :maxlength="
                            form.channel == 2 && form.style == 2 ? 58 : 9
                        "
                        show-word-limit
                    ></el-input>
                </el-form-item>
                <!-- <el-form-item
                    label="筛选项"
                    :label-width="formLabelWidth"
                    prop="filter"
                     v-show="form.channel==2&&form.style==2"
                >
                <el-select
                   
                    v-model="filterList"
                    multiple
                    filterable
                    allow-create
                    default-first-option
                    placeholder="请输入筛选项">
                    <el-option
                    v-for="item in form.filter"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name">
                    </el-option>
                </el-select>
                </el-form-item> -->

                <el-form-item
                    label="标签文字"
                    :label-width="formLabelWidth"
                    prop="sub_title"
                    v-show="!(form.channel == 2 && form.style == 2)"
                >
                    <el-input
                        v-model="form.sub_title"
                        autocomplete="off"
                        placeholder="请输入标签文字"
                        maxlength="8"
                        show-word-limit
                    ></el-input>
                </el-form-item>
            </div>
            <!-- 平铺展示标题、副标题、副标题背景-->
            <div v-show="form.pattern == 1">
                <el-form-item
                    label="标题类型"
                    :label-width="formLabelWidth"
                    prop="card_class"
                >
                    <el-radio v-model="form.card_class" label="1"
                        >文字标题</el-radio
                    >
                    <el-radio v-model="form.card_class" label="2"
                        >图片标题</el-radio
                    >
                </el-form-item>
                <el-form-item
                    label="卡片标题"
                    :label-width="formLabelWidth"
                    prop="card_name"
                    v-show="form.card_class == 1"
                >
                    <el-input
                        v-model="form.card_name"
                        autocomplete="off"
                        placeholder="请输入卡片标题"
                        maxlength="4"
                        show-word-limit
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="卡片标题"
                    :label-width="formLabelWidth"
                    prop="title_img"
                    v-show="form.card_class == 2"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>

                <el-form-item
                    label="标签文字"
                    :label-width="formLabelWidth"
                    prop="sub_title"
                >
                    <el-input
                        v-model="form.sub_title"
                        autocomplete="off"
                        placeholder="请输入标签文字"
                        maxlength="4"
                        show-word-limit
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="标签文字背景"
                    :label-width="formLabelWidth"
                    prop="sub_title_img"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="sub_title_back"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
            </div>

            <el-form-item
                label="样式效果预览"
                :label-width="formLabelWidth"
                prop="StylePreview"
            >
                <div v-show="form.channel == 2">
                    <!-- 秒发样式 -->
                    <img src="./images/miaofa.png" width="200px" alt="" />
                </div>
                <!-- 横向滑动效果预览 -->
                <div v-show="form.channel == 0">
                    <div v-show="form.pattern == 0">
                        <img
                            src="./images/wufeng.png"
                            alt=""
                            v-show="form.pattern == 0 && form.style == 0"
                        />
                        <img
                            src="./images/youfeng.png"
                            alt=""
                            v-show="form.pattern == 0 && form.style == 1"
                        />
                        <img
                            src="./images/shuban.png"
                            alt=""
                            v-show="form.pattern == 0 && form.style == 2"
                        />
                        <img
                            src="./images/baitu.png"
                            alt=""
                            v-show="form.pattern == 0 && form.style == 3"
                        />
                    </div>
                    <!-- 平铺展示效果预览 -->
                    <div v-show="form.pattern == 1">
                        <img
                            src="./images/temai.png"
                            alt=""
                            v-show="form.pattern == 1 && form.style == 4"
                        />
                        <img
                            src="./images/miaosha.png"
                            alt=""
                            v-show="form.pattern == 1 && form.style == 5"
                        />
                        <img
                            src="./images/pintuan.png"
                            alt=""
                            v-show="form.pattern == 1 && form.style == 6"
                        />
                        <img
                            src="./images/zhibo.png"
                            alt=""
                            v-show="form.pattern == 1 && form.style == 7"
                        />
                    </div>
                </div>
            </el-form-item>
            <el-form-item
                label="排序"
                :label-width="formLabelWidth"
                prop="sort"
            >
                <el-input-number
                    v-model="form.sort"
                    controls-position="right"
                    @change="handleChange"
                    :min="1"
                    :max="10"
                ></el-input-number>
            </el-form-item>
            <el-form-item
                label="子项"
                :label-width="formLabelWidth"
                prop="filter"
                v-if="form.page_mode == 1"
            >
                <div
                    style="min-height: 40px; display: flex; align-items: center"
                >
                    <div
                        style="
                            min-width: 180px;
                            min-height: 28px;
                            padding: 0 15px;
                            border: 1px solid #dcdfe6;
                            display: inline-block;
                        "
                    >
                        {{ filterStr }}
                    </div>
                    <el-button
                        type="primary"
                        size="mini"
                        @click="filterSet()"
                        style="margin-left: 20px"
                    >
                        管理子项
                    </el-button>
                </div>
            </el-form-item>
            <AddGoodsConfig
                v-if="form.page_mode == 0"
                :isEdit="true"
                :add_method="form.add_method"
                :auto_add_type="form.auto_add_type"
                :auto_add_content="form.auto_add_content"
                ref="AddGoodsConfig"
            ></AddGoodsConfig>
            <el-card
                v-if="form.page_mode == 0"
                shadow="hover"
                style="margin-top: 20px; max-width: 7500px"
            >
                <div slot="header">
                    <span> 分享配置 </span>
                </div>
                <!-- card body -->
                <div>
                    <el-form-item label="分享图" prop="share_image">
                        <VosOss
                            list-type="picture-card"
                            :showFileList="true"
                            :limit="1"
                            :dir="dir"
                            :file-list="share_image"
                            :limitWhList="[800, 800]"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </VosOss>
                    </el-form-item>
                </div>
                <div>
                    <el-form-item label="主标题" prop="main_title">
                        <el-input
                            style="width: 380px"
                            v-model="form.main_title"
                            size="normal"
                            clearable
                        ></el-input>
                    </el-form-item>
                </div>
            </el-card>
            <el-form-item style="margin-top: 50px; margin-left: 40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>

        <el-dialog
            :close-on-click-modal="false"
            title="子项管理"
            :visible.sync="filterViewDialogStatus"
            width="60%"
            append-to-body
        >
            <el-button
                type="primary"
                size="defualt"
                @click="addType"
                style="margin-bottom: 10px"
                >添加</el-button
            >

            <div class="table" v-if="filterList.length">
                <el-card class="card" shadow="hover">
                    <!-- 添加标题头 -->
                    <div
                        class="table-header"
                        style="
                            display: flex;
                            align-items: center;
                            padding: 12px 8px;
                            background: #f5f7fa;
                            border-bottom: 1px solid #ebeef5;
                        "
                    >
                        <span
                            style="
                                flex: 1;
                                padding-left: 20px;
                                font-weight: bold;
                            "
                            >子项名称（拖拽可进行排序）</span
                        >
                        <div
                            style="
                                width: 170px;
                                text-align: center;
                                font-weight: bold;
                            "
                        >
                            操作
                        </div>
                    </div>

                    <draggable
                        v-model="filterList"
                        :animation="200"
                        handle=".el-table__row"
                    >
                        <transition-group>
                            <div
                                v-for="item in filterList"
                                :key="item.sort"
                                class="el-table__row"
                                style="
                                    display: flex;
                                    align-items: center;
                                    padding: 8px;
                                    border-bottom: 1px solid #ebeef5;
                                "
                            >
                                <span style="flex: 1; padding-left: 20px">{{
                                    item.name
                                }}</span>
                                <span style="font-size: 10px">
                                    {{
                                        item.add_method == 1 ? "自动" : "手动"
                                    }}</span
                                >
                                <div style="width: 170px; text-align: center">
                                    <el-button
                                        @click="view(item)"
                                        type="text"
                                        size="mini"
                                        >编辑</el-button
                                    >
                                    <el-button
                                        @click="deleteData(item)"
                                        type="text"
                                        size="mini"
                                        style="margin-left: 10px"
                                        >删除</el-button
                                    >
                                </div>
                            </div>
                        </transition-group>
                    </draggable>
                </el-card>
            </div>
            <el-empty v-else></el-empty>

            <div slot="footer" class="dialog-footer">
                <el-button @click="filterViewDialogStatus = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="filterSure">确 定</el-button>
            </div>
        </el-dialog>
        <el-dialog
            :close-on-click-modal="false"
            title="添加子项"
            :visible.sync="addChildrenVisiable"
            width="60%"
            append-to-body
        >
            <addChild
                v-if="addChildrenVisiable"
                ref="addChild"
                @submit="handleChildSubmit"
                @cancel="addChildrenVisiable = false"
            ></addChild>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import AddGoodsConfig from "../../components/addGoodsCon/addGoodsCon.vue";
import draggable from "vuedraggable";
import addChild from "./addChild.vue";
export default {
    components: {
        VosOss,
        AddGoodsConfig,
        draggable,
        addChild,
    },
    props: ["rowData"],
    data() {
        //验证排序
        let sortingValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入数字"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的数字"));
            } else {
                callback();
            }
        };
        //图标验证
        const checkTitileMap1 = (rules, value, callback) => {
            if (this.sub_title_back.length == 0 && this.form.pattern != "0") {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        const checkTitileMap2 = (rules, value, callback) => {
            if (this.icon_map.length == 0 && this.form.card_class == "2") {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        const checkCard_name = (rules, value, callback) => {
            if (this.form.card_class == 1 && this.form.card_name == "") {
                callback(new Error("请输入卡片标题"));
            } else {
                callback();
            }
        };
        const checkFliter = (rules, value, callback) => {
            if (this.form.page_mode === 1 && this.filterList.length === 0) {
                callback(new Error("筛选模式下子项必填，请添加至少一个子项"));
            } else {
                callback();
            }
        };
        const checkSubTitle = (rules, value, callback) => {
            if (
                this.form.sub_title == "" &&
                !(this.form.channel == 2 && this.form.style == 2)
            ) {
                callback(new Error("请输入副标题"));
            } else {
                callback();
            }
        };
        const checkShareImg = (rules, value, callback) => {
            if (this.share_image.length == 0) {
                callback(new Error("请上传分享图"));
            } else {
                callback();
            }
        };
        return {
            icon_map: [],
            sub_title_back: [],
            dir: "vinehoo/vos/marketing/",
            dialogFormVisible: false,
            form: {
                channel: "0",
                pattern: "0",
                style: "0",
                StylePreview: "0",
                card_name: "",
                sub_title: "",
                card_class: "1",
                title_img: "",
                sub_title_img: "",
                sort: 1,
                goods_sort_type: "0",
                add_metshod: 0,
                filter: [],
                main_title: "",
                // secondary_title:"",
                share_image: "",
                // share_url:"",
                page_mode: 0,
            },
            filterViewDialogStatus: false,
            filterList: [],
            filterStr: "",
            addChildrenVisiable: false,
            share_image: [],
            formRules: {
                title_img: [
                    {
                        required: true,
                        validator: checkTitileMap2,
                    },
                ],
                sub_title_img: [
                    {
                        required: true,
                        validator: checkTitileMap1,
                    },
                ],
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur",
                    },
                ],
                pattern: [
                    {
                        required: true,
                        message: "请选择模式",
                        trigger: "blur",
                    },
                ],
                style: [
                    {
                        required: true,
                        message: "请选择样式",
                        trigger: "blur",
                    },
                ],
                card_name: [
                    {
                        required: true,
                        validator: checkCard_name,
                        trigger: "blur",
                    },
                ],
                filter: [
                    {
                        required: true,
                        validator: checkFliter,
                    },
                ],
                sub_title: [
                    {
                        required: true,
                        validator: checkSubTitle,
                    },
                ],
                sort: [
                    {
                        required: true,
                        validator: sortingValidator,
                        trigger: "blur",
                    },
                ],
            },
            formLabelWidth: "120px",
        };
    },
    mounted() {
        console.log("编辑", this.rowData);
        this.form = this.rowData;
        this.form.pattern = String(this.form.pattern);
        this.form.style = String(this.form.style);
        this.form.channel = String(this.form.channel);
        this.form.goods_sort_type = String(this.form.goods_sort_type);
        this.form.card_class = String(this.form.card_class);
        this.form.page_mode = this.form.page_mode || 0;
        if (this.form.pattern != 0) {
            this.sub_title_back = this.rowData.sub_title_back.split(",");
        }
        if (this.form.share_image) {
            this.share_image = this.form.share_image.split(",");
        }
        // 初始化筛选项数据
        if (this.rowData.filter && this.rowData.filter.length > 0) {
            this.filterList = this.rowData.filter;
            this.filterStr = this.filterList.map((obj) => obj.name).join("、");
        }
        if (this.form.card_class == 2) {
            this.icon_map = this.rowData.card_name.split(",");
        }
    },
    methods: {
        closeDiog() {
            this.$emit("closeViewDialogStatus");
            this.$emit("getCardManageList");
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        },
        //筛选项
        filterSet(row) {
            this.filterViewDialogStatus = true;
        },
        addType() {
            this.addChildrenVisiable = true;
            this.$nextTick(() => {
                this.$refs.addChild.setFormData(null);
            });
        },
        filterSure() {
            this.filterViewDialogStatus = false;
            this.filterList = this.filterList.filter(
                (item) => item.name && item.name.trim() !== ""
            );
            this.filterStr = this.filterList.map((obj) => obj.name).join("、");
        },
        deleteData(row) {
            this.$confirm("确认删除该子项?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    const index = this.filterList.findIndex(
                        (item) => item.sort === row.sort
                    );
                    if (index > -1) {
                        this.filterList.splice(index, 1);
                        this.$message({
                            type: "success",
                            message: "删除成功!",
                        });
                        // 更新显示的字符串
                        this.filterStr = this.filterList
                            .map((obj) => obj.name)
                            .join("、");
                    }
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        view(row) {
            this.addChildrenVisiable = true;
            this.$nextTick(() => {
                this.$refs.addChild.setFormData(row);
            });
        },
        handleChildSubmit(formData) {
            if (formData.sort >= 0) {
                // 编辑现有数据
                const index = this.filterList.findIndex(
                    (item) => item.sort === formData.sort
                );
                if (index > -1) {
                    this.filterList[index] = { ...formData };
                    this.$message({
                        type: "success",
                        message: "编辑成功!",
                    });
                }
            } else {
                // 添加新数据
                let sortNum = this.filterList.length;
                this.filterList.push({
                    ...formData,
                    id: 0, // 生成临时 id
                    sort: sortNum,
                });
                this.$message({
                    type: "success",
                    message: "添加成功!",
                });
            }
            this.addChildrenVisiable = false;
        },
        //表单提交，在父组件调用
        submitForm() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    let card_name = "";
                    if (this.form.card_class == 1) {
                        card_name = this.form.card_name;
                    } else {
                        card_name = this.icon_map.join(",");
                    }

                    if (this.form.page_mode == 0) {
                        const { add_methodnew, auto_add_typeNew, resultArr } =
                            this.$refs.AddGoodsConfig.getEditData(); //接受路径配置传过来的参数
                        this.form.add_method = add_methodnew;
                    }

                    var data = {
                        id: this.form.id,
                        channel: this.form.channel,
                        card_name: card_name,
                        card_class: this.form.card_class,
                        sub_title: this.form.sub_title,
                        sub_title_back: this.sub_title_back.join(","),
                        pattern: this.form.pattern,
                        style: this.form.style,
                        sort: this.form.sort,
                        goods_sort_type: this.form.goods_sort_type,
                        add_method:
                            this.form.page_mode == 0 ? this.form.add_method : 0,
                        filter:
                            this.form.page_mode == 1
                                ? this.filterList.filter(
                                      (item) =>
                                          item.name && item.name.trim() !== ""
                                  )
                                : [],
                        main_title: this.form.main_title,
                        // secondary_title:this.form.secondary_title,
                        // share_url:this.form.share_url,
                        share_image: this.share_image.join(","),
                        page_mode: this.form.page_mode,
                    };
                    if (this.form.add_method == 1) {
                        const { auto_add_typeNew, resultArr } =
                            this.$refs.AddGoodsConfig.getEditData(); //接受路径配置传过来的参数
                        data.auto_add_type = auto_add_typeNew;
                        data.auto_add_content = resultArr;
                    }
                    console.log("表单", data);
                    this.$request.cardManage
                        .editCardManage(data)
                        .then((res) => {
                            console.log("111111111", res);
                            if (res.data.error_code == 0) {
                                console.log(9999);
                                this.$emit("closeViewDialogStatus");
                                this.$emit("getCardManageList");
                            }
                        });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}

.handle {
    color: #909399;
}
.handle:hover {
    color: #409eff;
}

.el-table__row {
    background: #fff;
    cursor: move;
    &:hover {
        background: #f5f7fa;
    }
}

.sortable-ghost {
    opacity: 0.5;
    background: #c8ebfb !important;
}

.sortable-drag {
    opacity: 0.9;
    background: #fff;
}

.table-header {
    font-size: 14px;
    color: #909399;
}
</style>
