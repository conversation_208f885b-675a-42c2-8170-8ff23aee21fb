<template>
    <el-dialog
        :title="'商品配置'"
        :visible.sync="visible"
        :close-on-click-modal="false"
        :before-close="closeDialog"
        width="80%"
    >
        <div style="">
            <span>所属标签：</span>
            <el-button type="primary" plain size="mini">{{
                row.title
            }}</el-button>
        </div>
        <el-card shadow="never" style="margin-top: 20px;margin-bottom: 20px;">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="query.periods"
                        placeholder="期数"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="query.status"
                        filterable
                        placeholder="状态"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { value: 0, label: '隐藏' },
                                { value: 1, label: '显示' }
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button type="success" size="mini" @click="handleAdd"
                        >添加商品</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="never">
            <VhTable
                :data="tableData"
                :pageInfo="pageInfo"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            >
                <el-table-column label="期数" prop="periods" width="100" />
                <el-table-column label="商品名称" prop="goods_name" />
                <el-table-column label="图片" width="120">
                    <template slot-scope="{ row: { images } }">
                        <el-popover
                            placement="top-start"
                            trigger="hover"
                            size="mini"
                        >
                            <a
                                :href="images"
                                target="_blank"
                                title="查看最大化图片"
                            >
                                <img
                                    :src="images"
                                    style="width: 200px; height: 200px"
                                />
                            </a>
                            <img
                                slot="reference"
                                :src="images"
                                style="
                                    width: 50px;
                                    height: 50px;
                                    cursor: pointer;
                                "
                            />
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="status" width="100">
                    <template slot-scope="{ row: { status } }">
                        {{ status === 0 ? "隐藏" : "显示" }}
                    </template>
                </el-table-column>
                <el-table-column label="权重" prop="weight_value" width="100" />
                <el-table-column label="更新人/更新时间" width="200">
                    <template
                        slot-scope="{ row: { update_name, update_time } }"
                    >
                        <span>{{ update_name }}</span>
                        <span style="margin-left: 5px;">{{ update_time }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="操作">
                    <template slot-scope="{ row }">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleEdit(row)"
                            >编辑</el-button
                        >
                        <el-button
                            :type="row.status === 0 ? 'success' : 'info'"
                            size="mini"
                            @click="handleStatusUpdate(row)"
                            >{{ row.status === 0 ? "显示" : "隐藏" }}</el-button
                        >

                        <el-button
                            type="danger"
                            size="mini"
                            @click="handleDel(row)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </VhTable>
        </el-card>

        <!-- 商品列表 -->
        <goodsOperateDialog
            v-if="goodsOperateVisible"
            :visible.sync="goodsOperateVisible"
            :row="rowData"
            :filtersId="row.id"
            :edit="edit"
            @load="load"
        />
    </el-dialog>
</template>

<script>
import dialogMixin from "@/mixins/dialogMixin";
import toastMixin from "@/mixins/toastMixin";
import tableListMixin from "@/mixins/tableListMixin";
import GoodsOperateDialog from "./goodsOperateDialog";
export default {
    mixins: [tableListMixin, dialogMixin, toastMixin],
    props: {
        row: {
            type: Object,
            default: () => ({})
        }
    },
    components: {
        GoodsOperateDialog
    },
    data: () => ({
        query: {
            periods: "",
            status: ""
        },
        goodsOperateVisible: false,
        rowData: {},
        edit: false
    }),
    methods: {
        load() {
            return this.$request.secondFilter
                .secondFilterGoodsList({
                    filters_id: this.row.id,
                    ...this.query
                })
                .then(res => {
                    const { data = {} } = res?.data || {};
                    this.tableData = data?.list || [];
                    return data;
                });
        },
        handleAdd() {
            this.rowData = this.$options.data().rowData;
            this.goodsOperateVisible = true;
            this.edit = false;
        },
        handleEdit(row) {
            this.rowData = { ...row };
            this.goodsOperateVisible = true;
            this.edit = true;
        },
        handleStatusUpdate(row) {
            const { id, status } = row;
            this.$request.secondFilter
                .secondFilterGoodsUpdate({
                    id,
                    operation_type: 0,
                    value: status ? 0 : 1
                })
                .then(res => {
                    if (res?.data?.error_code === 0) {
                        this.toastSuccess();
                        this.search();
                    }
                });
        },

        handleDel({ id }) {
            this.$confirm("确认删除该商品吗？", "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    this.$request.secondFilter
                        .secondFilterGoodsDel({
                            id
                        })
                        .then(res => {
                            if (res?.data?.error_code === 0) {
                                this.toastSuccess();
                                this.search();
                            }
                        });
                })
                .catch(() => {
                    this.toast("info", "您取消了删除");
                });
        }
    }
};
</script>
