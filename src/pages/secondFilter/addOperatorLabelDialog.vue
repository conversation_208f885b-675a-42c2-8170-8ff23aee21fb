<template>
    <el-dialog
        :title="'新增运营标签'"
        :visible.sync="visible"
        :close-on-click-modal="false"
        :before-close="closeDialog"
        width="40%"
    >
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            size="mini"
            label-width="120px"
        >
            <el-form-item label="筛选标签标题" prop="title">
                <el-input
                    :minlength="2"
                    :maxlength="5"
                    placeholder="请输入筛选标签标题(最多5个字符)"
                    v-model="ruleForm.title"
                ></el-input>
            </el-form-item>
            <el-form-item label="权重值" prop="weight_value">
                <el-input
                    placeholder="请输入权重值"
                    v-model.number="ruleForm.weight_value"
                ></el-input>
            </el-form-item>
            <!-- <el-form-item label="是否生效" prop="status">
                <el-switch
                    v-model="ruleForm.status"
                    :active-value="1"
                    :inactive-value="0"
                    active-color="#13ce66"
                ></el-switch>
            </el-form-item> -->
            <el-form-item>
                <el-button plane @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确认</el-button
                >
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
import dialogMixin from "@/mixins/dialogMixin";
import toastMixin from "@/mixins/toastMixin";
export default {
    mixins: [dialogMixin, toastMixin],
    data: () => ({
        ruleForm: {
            title: "",
            weight_value: 0
            // status: 1
        },
        rules: {
            title: [
                {
                    required: true,
                    message: "请输入筛选标签标题(最多5个字符)",
                    trigger: "blur"
                },
                { min: 2, max: 5, message: "最多5个字符", trigger: "blur" }
            ],
            weight_value: [
                {
                    required: true,
                    message: "请输入权重值",
                    trigger: "blur"
                },
                { type: "number", message: "请输入数字", trigger: "blur" }
            ]
        }
    }),
    methods: {
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    this.$request.secondFilter
                        .secondFilterAdd(this.ruleForm)
                        .then(res => {
                            if (res?.data?.error_code === 0) {
                                this.toastSuccess();
                                this.closeDialog();
                                this.$emit("load");
                            }
                        });
                }
            });
        }
    }
};
</script>
