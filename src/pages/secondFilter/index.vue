<template>
    <div>
        <el-card>
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-select
                        v-model="query.type"
                        clearable
                        placeholder="标签类型"
                    >
                        <el-option
                            v-for="item in MLabelTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="mini" @click="search"
                        >查询</el-button
                    >
                    <el-button
                        type="success"
                        size="mini"
                        @click="addOperatorLabelVisible = true"
                        >添加运营标签</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px;">
            <el-table
                :data="tableData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column
                    label="筛选标签标题"
                    prop="title"
                ></el-table-column>
                <el-table-column
                    label="筛选标签标识"
                    prop="identifier"
                ></el-table-column>
                <el-table-column label="标签类型">
                    <template slot-scope="{ row: { filters_type } }">
                        {{ MLabelTypeText[filters_type] }}
                    </template>
                </el-table-column>
                <el-table-column label="操作人" prop="operator" />
                <el-table-column label="状态" prop="status">
                    <template slot-scope="{ row }">
                        <el-tag
                            :type="handleStatus(row.status, 'success', 'info')"
                            size="mini"
                            effect="dark"
                        >
                            {{
                                handleStatus(row.status, "生效", "失效")
                            }}</el-tag
                        >
                    </template>
                </el-table-column>
                <el-table-column
                    label="权重值"
                    prop="weight_value"
                ></el-table-column>
                <el-table-column
                    label="操作时间"
                    prop="update_time"
                ></el-table-column>
                <el-table-column label="操作" width="300">
                    <template slot-scope="{ row }">
                        <el-button
                            type="primary"
                            size="mini"
                            @click="handleEdit(row)"
                        >
                            修改
                        </el-button>
                        <el-button
                            :type="handleStatus(!row.status, 'success', 'info')"
                            size="mini"
                            @click="handleUpdateStatus(row)"
                        >
                            {{ handleStatus(!row.status, "生效", "失效") }}
                        </el-button>
                        <el-button
                            v-if="row.filters_type === 1"
                            type="danger"
                            size="mini"
                            @click="handleGoodsList(row)"
                        >
                            商品配置
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 修改 -->
        <el-dialog
            title="修改配置"
            :visible.sync="editVisible"
            :close-on-click-modal="false"
            width="40%"
        >
            <el-form
                :model="ruleForm"
                :rules="rules"
                ref="ruleForm"
                size="mini"
                label-width="120px"
            >
                <el-form-item label="筛选标签标题" prop="title">
                    <el-input
                        :minlength="2"
                        :maxlength="5"
                        placeholder="请输入筛选标签标题(最多5个字符)"
                        v-model="ruleForm.title"
                    ></el-input>
                </el-form-item>
                <el-form-item label="筛选标签标识" prop="identifier">
                    <el-input
                        disabled
                        placeholder="请输入筛选标签标识"
                        v-model="ruleForm.identifier"
                    ></el-input>
                </el-form-item>
                <el-form-item label="权重值" prop="weight_value">
                    <el-input
                        placeholder="请输入权重值"
                        v-model.number="ruleForm.weight_value"
                    ></el-input>
                </el-form-item>
                <el-form-item label="是否生效" prop="status">
                    <el-switch
                        v-model="ruleForm.status"
                        :active-value="1"
                        :inactive-value="0"
                        active-color="#13ce66"
                    ></el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button plane @click="editVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >确认</el-button
                    >
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 新增运营标签 -->
        <AddOperatorLabelDialog
            v-if="addOperatorLabelVisible"
            :visible.sync="addOperatorLabelVisible"
            @load="load"
        />

        <!-- 商品列表 -->
        <GoodsListDialog
            v-if="goodsListVisible"
            :visible.sync="goodsListVisible"
            :row="rowData"
        />
    </div>
</template>
<script>
import listMixin from "@/mixins/listMixin";
import {
    MLabelTypeOptions,
    MLabelTypeText
} from "@/mapper/secondFilter/mapper";
import AddOperatorLabelDialog from "./addOperatorLabelDialog";
import GoodsListDialog from "./goodsListDialog";
export default {
    mixins: [listMixin],
    components: {
        AddOperatorLabelDialog,
        GoodsListDialog
    },
    data: () => ({
        MLabelTypeOptions,
        MLabelTypeText,
        query: {
            type: "",
            limit: 1000
        },
        editVisible: false,
        addOperatorLabelVisible: false,
        goodsListVisible: false,
        ruleForm: {
            title: "",
            identifier: "",
            weight_value: 0,
            status: 0
        },
        rules: {
            title: [
                {
                    required: true,
                    message: "请输入筛选标签标题(最多5个字符)",
                    trigger: "blur"
                },
                { min: 2, max: 5, message: "最多5个字符", trigger: "blur" }
            ],
            identifier: [
                {
                    required: true,
                    message: "请输入筛选标签标识",
                    trigger: "blur"
                }
            ],
            weight_value: [
                {
                    required: true,
                    message: "请输入权重值",
                    trigger: "blur"
                },
                { type: "number", message: "请输入数字", trigger: "blur" }
            ]
        },
        rowData: {}
    }),
    methods: {
        async load() {
            const res = await this.$request.secondFilter.secondFilterList(
                this.query
            );
            if (res?.data?.error_code === 0) {
                this.tableData = res?.data?.data?.list || [];
            }
        },

        handleEdit(row) {
            this.ruleForm = { ...row };
            this.editVisible = true;
        },

        handleUpdateStatus(row) {
            const { id, status } = { ...row };
            const data = {
                id,
                status: status ? 0 : 1
            };
            this.$request.secondFilter.secondFilterUpdate(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$message.success("操作成功！");
                    this.load();
                }
            });
        },

        handleGoodsList(row) {
            this.rowData = { ...row };
            this.goodsListVisible = true;
        },

        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    const { weight_value } = this.ruleForm;
                    const data = {
                        ...this.ruleForm,
                        weight_value: +weight_value
                    };
                    this.$request.secondFilter
                        .secondFilterEdit(data)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功！");
                                this.load();
                                this.editVisible = false;
                            }
                        });
                }
            });
        },

        handleStatus(status, params1, params2) {
            return status ? params1 : params2;
        }
    }
};
</script>
