<template>
    <el-dialog
        :title="`${edit ? '编辑' : '新增'}商品`"
        :visible.sync="visible"
        :close-on-click-modal="false"
        :before-close="closeDialog"
        append-to-body
        width="60%"
    >
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            size="mini"
            label-width="120px"
        >
            <el-form-item label="期数" prop="periods">
                <el-input
                    placeholder="期数"
                    v-model.number="ruleForm.periods"
                    style="width: 120px; margin-right: 10px;"
                    clearable
                ></el-input>
                <el-button
                    type="primary"
                    :disabled="!ruleForm.periods"
                    @click="importGoodsData"
                    >确定</el-button
                >
            </el-form-item>
            <el-form-item label="产品简称" prop="goods_short_name">
                <el-input
                    placeholder="请输入产品简称"
                    v-model.number="ruleForm.goods_short_name"
                    maxlength="15"
                    show-word-limit
                    style="width: 300px"
                ></el-input>
            </el-form-item>
            <el-form-item label="商品名称" prop="goods_name">
                <el-input
                    placeholder="商品名称"
                    v-model.number="ruleForm.goods_name"
                    disabled
                    style="width: 300px"
                ></el-input>
            </el-form-item>
            <el-form-item label="图片" prop="img">
                <span style="font-size: 12px;">(图片不可更改)</span>
                <vos-oss
                    ref="goodsImageRef"
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :fileList.sync="goods_image"
                    disabled
                >
                </vos-oss>
            </el-form-item>
            <el-form-item label="单价" prop="unit_price">
                <el-input-number
                    v-model="ruleForm.unit_price"
                    :min="0"
                    :step="1"
                    disabled
                />
                <span class="unit">元</span>
            </el-form-item>
            <el-form-item label="市场价" prop="market_price">
                <el-input-number
                    v-model="ruleForm.market_price"
                    :min="0"
                    :step="1"
                    disabled
                />
                <span class="unit">元</span>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-radio v-model="ruleForm.status" :label="1">显示</el-radio>
                <el-radio v-model="ruleForm.status" :label="0">隐藏</el-radio>
            </el-form-item>
            <el-form-item label="排序" prop="weight_value">
                <el-input-number
                    v-model="ruleForm.weight_value"
                    :min="0"
                    :step="1"
                />
            </el-form-item>
            <el-form-item>
                <el-button plane @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确认</el-button
                >
            </el-form-item>
        </el-form>
    </el-dialog>
</template>

<script>
import VosOss from "vos-oss";
import dialogMixin from "@/mixins/dialogMixin";
import toastMixin from "@/mixins/toastMixin";
export default {
    components: {
        VosOss
    },
    mixins: [dialogMixin, toastMixin],
    props: {
        edit: {
            type: Boolean,
            default: false
        },
        filtersId: {
            type: Number,
            default: 0
        },
        row: {
            type: Object,
            default: () => ({})
        }
    },
    data: () => ({
        dir: "vinehoo/vos/marketing/",
        ruleForm: {
            periods: "",
            goods_short_name: "",
            goods_name: "",
            unit_price: 0,
            market_price: 0,
            status: 1,
            weight_value: 0,
            periods_type: 1
        },
        rules: {
            periods: [
                {
                    required: true,
                    message: "请输入期数",
                    trigger: "blur"
                },
                { type: "number", message: "请输入数字", trigger: "blur" }
            ]
        },
        goods_image: []
    }),
    mounted() {
        console.log("--------------------------------", this.filtersId);
        this.init();
    },
    methods: {
        init() {
            if (this.edit) {
                const {
                    periods,
                    periods_type,
                    goods_short_name,
                    weight_value,
                    goods_name,
                    images,
                    unit_price,
                    market_price,
                    status
                } = this.row;
                this.ruleForm.periods = periods;
                this.ruleForm.periods_type = periods_type;
                this.ruleForm.goods_short_name = goods_short_name;
                this.ruleForm.goods_name = goods_name;
                this.goods_image = [images];
                this.ruleForm.unit_price = unit_price;
                this.ruleForm.market_price = market_price;
                this.ruleForm.status = status;
                this.ruleForm.weight_value = weight_value;
            }
        },

        importGoodsData() {
            this.$request.activities
                .getGoodsById({
                    period: this.ruleForm.periods
                })
                .then(res => {
                    if (res?.data?.error_code === 0 && res?.data?.data) {
                        this.ruleForm.periods_type =
                            res?.data?.data?.periods_type;
                        this.ruleForm.goods_name = res.data.data.title;
                        this.ruleForm.unit_price = res.data.data.price;
                        this.ruleForm.market_price = res.data.data.market_price;
                        this.goods_image = [
                            res.data.data.product_img.split(",")[0]
                        ];
                        this.$refs.goodsImageRef.handleviewFileList(
                            this.goods_image
                        );
                    } else {
                        this.toast("error", "返回数据有误");
                    }
                });
        },

        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    const {
                        periods,
                        periods_type,
                        goods_short_name,
                        status,
                        weight_value
                    } = this.ruleForm;
                    const data = {
                        periods,
                        goods_short_name,
                        periods_type,
                        filters_id: this.filtersId,
                        status,
                        weight_value
                    };
                    if (this.edit) {
                        data.id = this.row.id;
                    }
                    console.log(data);

                    const requestName = this.edit
                        ? "secondFilterGoodsEdit"
                        : "secondFilterGoodsAdd";
                    this.$request.secondFilter[requestName](data).then(res => {
                        if (res?.data?.error_code === 0) {
                            this.toastSuccess();
                            this.closeDialog();
                            this.$emit("load");
                        }
                    });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.unit {
    margin-left: 10px;
}
.pic-tips {
    font-size: 12px;
    color: #6b6b6b;
    line-height: 12px;
}
</style>
