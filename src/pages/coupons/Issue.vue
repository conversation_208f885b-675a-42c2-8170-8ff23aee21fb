<template>
    <div class="article-layout">
        <el-card>
            <div class="article-form">
                <!-- <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="优惠券名称"
                        size="mini"
                        @keyup.enter.native="search"
                        v-model="form.coupon_name"
                        clearable
                    ></el-input>
                </div> -->
                <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="用户ID"
                        clearable
                        @keyup.enter.native="search"
                        v-model="form.uid"
                        size="mini"
                    ></el-input>
                </div>
                <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="优惠券ID"
                        oninput="value=value.replace(/[^0-9]/g,'')"
                        clearable
                        @keyup.enter.native="search"
                        v-model="form.coupon_id"
                        size="mini"
                    ></el-input>
                </div>
                <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="优惠券包ID"
                        clearable
                        @keyup.enter.native="search"
                        v-model="form.coupon_package_details_id"
                        size="mini"
                    ></el-input>
                </div>
                <div>
                    <el-select
                        class="m-r-10 w-mini"
                        v-model="form.review_status"
                        filterable
                        size="mini"
                        placeholder="状态"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { value: 1, label: '审核中' },
                                { value: 2, label: '已发放' },
                                { value: 3, label: '已使用' },
                                { value: 4, label: '已过期' },
                                { value: 5, label: '已驳回' },
                                { value: 6, label: '申请失败' },
                                { value: 7, label: '已作废' }
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
                <!-- <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="用户昵称"
                        clearable
                        v-model="form.nickname"
                        size="mini"
                        @keyup.enter.native="search"
                    ></el-input>
                </div> -->
                <div>
                    <el-date-picker
                        v-model="time_date"
                        @change="time_dateChange"
                        size="mini"
                        type="datetimerange"
                        range-separator="-"
                        class="m-r-10"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        start-placeholder="发放-开始日期"
                        end-placeholder="发放-结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </div>

                <div class="form-actions">
                    <el-button size="mini" @click="search" type="warning"
                        >查询</el-button
                    >
                    <el-button size="mini" @click="onExport" type="primary"
                        >导出</el-button
                    >
                </div>
            </div>
        </el-card>
        <div class="article-main">
            <el-card>
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="coupon_id"
                        label="优惠券ID"
                        width="90"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="coupon_package_details_id"
                        label="优惠券包ID"
                        width="90"
                        align="center"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.coupon_package_details_id || "-" }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="issue_cause"
                        align="center"
                        :show-overflow-tooltip="true"
                        label="发券事由"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="coupon_name"
                        label="优惠券名称"
                        :show-overflow-tooltip="true"
                        min-width="170"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="uid"
                        label="用户ID"
                        min-width="100"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="nickname"
                        label="用户昵称"
                        min-width="150"
                        :show-overflow-tooltip="true"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="reg_time"
                        label="用户注册时间"
                        min-width="150"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="telephone_encrypt"
                        label="手机号"
                        min-width="100"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="reg_from"
                        label="注册来源"
                        min-width="100"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="created_time"
                        align="center"
                        label="申请时间"
                        width="160"
                    >
                        <template slot-scope="row">
                            {{ row.row.created_time }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="coupon_face_value"
                        align="center"
                        label="价值"
                        width="90"
                    >
                    </el-table-column>
                    <!-- <el-table-column
                        prop="apply_uid"
                        align="center"
                        label="申请人ID"
                        min-width="90"
                    >
                    </el-table-column> -->
                    <el-table-column
                        prop="apply_name"
                        align="center"
                        label="申请人"
                        width="80"
                    >
                    </el-table-column>

                    <!-- <el-table-column
                        prop="address"
                        align="center"
                        label="审核人"
                        min-width="90"
                    >
                    </el-table-column> -->
                    <el-table-column
                        prop="review_status"
                        align="center"
                        label="状态"
                        width="80"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="audit_time"
                        align="center"
                        label="发放时间"
                        width="160"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="order_sn"
                        align="center"
                        label="订单编号"
                        min-width="140"
                        :show-overflow-tooltip="true"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="remark"
                        align="center"
                        label="备注"
                        :show-overflow-tooltip="true"
                        min-width="120"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="operator"
                        label="操作"
                        fixed="right"
                        width="100"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                v-if="row.row.review_status == '已发放'"
                                @click="couponInvalid(row.row)"
                                type="text"
                                size="mini"
                                >优惠券作废</el-button
                            >
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="address" label="备注" > -->
                    <!-- </el-table-column> -->
                </el-table>
            </el-card>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 优惠券作废 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="作废原因"
                :visible.sync="couponInvalidDialogStatus"
                width="30%"
            >
                <CouponInvalid
                    v-if="couponInvalidDialogStatus"
                    :couponInvalidRowData="couponInvalidRowData"
                    @close="couponInvalidClose"
                />
            </el-dialog>
        </div>
    </div>
</template>
<script>
import CouponInvalid from "./couponInvalid.vue";
export default {
    components: {
        CouponInvalid
    },
    data() {
        return {
            time_date: [],
            tableData: [],
            couponInvalidRowData: {},
            couponInvalidDialogStatus: false,
            form: {
                page: 1,
                limit: 10,
                coupon_name: "",
                coupon_id: "",
                apply_uid: "",
                nickname: "",
                start_time: "",
                end_time: "",
                uid: "",
                coupon_package_details_id: "",
                review_status: ""
            },
            total: 0
        };
    },
    mounted() {
        this.couponissueList();
    },
    methods: {
        //优化圈作废弹框关闭
        couponInvalidClose() {
            this.couponInvalidDialogStatus = false;
            this.couponissueList();
        },
        search() {
            this.form.page = 1;
            this.couponissueList();
        },
        time_dateChange(val) {
            if (val) {
                this.form.start_time = val[0];
                this.form.end_time = val[1];
            } else {
                this.form.start_time = "";
                this.form.end_time = "";
            }
        },
        async couponissueList() {
            let data = {
                ...this.form
            };
            let result = await this.$request.coupons.couponissueList(data);
            console.log(result);
            if (result.data.error_code == 0) {
                this.tableData = result.data.data.list;
                this.total = result.data.data.total;
            }
        },
        //优惠券作废
        async couponInvalid(row) {
            this.couponInvalidRowData = row;
            console.log("111", this.couponInvalidRowData);

            this.couponInvalidDialogStatus = true;
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.couponissueList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.couponissueList();
        },
        onExport() {
            this.$request.coupons.exportCouponIssueList(this.form).then(res => {
                if (res.data.error_code == 0) {
                    this.$message.success("导出审批提交成功");
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
