<template>
    <div class="push-coupons-layout">
        <el-card>
            <div class="push-coupons-form">
                <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="用户昵称"
                        size="mini"
                        v-model="form.nickname"
                        clearable
                    ></el-input>
                </div>
                <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="用户ID"
                        clearable
                        v-model="form.uid"
                        oninput="value=value.replace(/[^0-9]/g,'')"
                        size="mini"
                    ></el-input>
                </div>
                <div class="form-actions">
                    <el-button size="mini" @click="search" type="warning"
                        >查询</el-button
                    >
                </div>
                <div>
                    <el-button
                        style="margin-left:10px"
                        type="success"
                        size="mini"
                        @click="downloadTemp"
                        >下载模版</el-button
                    >
                </div>
                <vos-oss
                    style="margin-left:10px"
                    list-type="text"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="filelist"
                    filesType="/"
                    @on-success="handleSuccess(filelist)"
                >
                    <el-button type="primary" size="mini"
                        >批量导入用户</el-button
                    >
                </vos-oss>
            </div>
            <el-table
                v-if="!filelist.length"
                ref="multipleTable"
                :data="tableData"
                :row-key="getRowKeys"
                border
                size="mini"
                tooltip-effect="dark"
                style="width: 100%"
                @selection-change="handleSelectionChange"
            >
                <el-table-column
                    :reserve-selection="true"
                    type="selection"
                    width="55"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    label="用户ID"
                    min-width="120"
                    prop="uid"
                    align="center"
                >
                </el-table-column>
                <el-table-column
                    prop="nickname"
                    label="昵称"
                    min-width="120"
                    align="center"
                >
                </el-table-column>
            </el-table>
            <div class="pagination-block" v-if="!filelist.length">
                <el-pagination
                    background
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="form.page"
                    :page-size="form.limit"
                    :page-sizes="[10, 30, 50, 100, 200]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                >
                </el-pagination>
            </div>
            <!-- 已选择的用户 -->
            <div class="m-b-10" v-if="multipleSelection.length != 0">
                <b>已选择的用户</b>
                <el-table
                    :data="multipleSelection"
                    style="width: 100%"
                    border
                    max-height="250"
                    size="mini"
                    :header-cell-style="{ 'text-align': 'center' }"
                    :cell-style="{ 'text-align': 'center' }"
                >
                    <el-table-column
                        fixed
                        prop="uid"
                        label="用户ID"
                        width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="nickname"
                        label="用户昵称"
                        min-width="120"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="120">
                        <template slot-scope="scope">
                            <el-button
                                type="danger"
                                size="mini"
                                @click="del(scope.$index, scope.row)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="actions">
                <div>
                    <el-select
                        v-model="issue_cause"
                        filterable
                        placeholder="请选择发放事由"
                        size="mini"
                        clearable
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 4, maxRows: 2 }"
                        placeholder="请输入备注"
                        v-model="remark"
                        show-word-limit
                        maxlength="256"
                    >
                    </el-input>
                </div>
                <div class="buttons">
                    <el-button type="primary" size="mini" @click="push"
                        >发放</el-button
                    >
                    <el-button size="mini" @click="closePushCouponsStatus"
                        >取消</el-button
                    >
                </div>
            </div>
        </el-card>
    </div>
</template>
<script>
import VosOss from "vos-oss";
export default {
    props: ["rowData"],
    components: {
        VosOss
    },
    data() {
        return {
            filelist: [],
            dir: "vinehoo/vos/marketing/coupon",
            issue_cause: "", // 事由
            remark: "", //备注
            media_id: "", //附件ID
            options: [
                {
                    value: 1,
                    label: "顶赛漏液"
                },
                {
                    value: 2,
                    label: "延迟发货"
                },
                {
                    value: 3,
                    label: "外观破损"
                },
                {
                    value: 4,
                    label: "纠错鼓励"
                },
                {
                    value: 5,
                    label: "质量问题"
                },
                {
                    value: 6,
                    label: "其他"
                },
                {
                    value: 8,
                    label: "安抚补偿"
                },
                {
                    value: 9,
                    label: "发错货补差价"
                },
                {
                    value: 10,
                    label: "退回优惠券"
                },
                {
                    value: 11,
                    label: "缺货"
                },
                {
                    value: 12,
                    label: "补发优惠券"
                },
                {
                    value: 13,
                    label: "快递问题"
                }
            ],
            tableData: [],
            multipleSelection: [],
            multipleSelection_log: [],
            form: {
                coupon_name: "",
                id: "",
                page: 1,
                limit: 10
            },
            total: 0
        };
    },
    mounted() {
        this.getUserList();
    },
    methods: {
        getRowKeys(row) {
            return row.uid; //行唯一标识
        },
        handleSuccess(icon_map) {
            console.log("文件", icon_map);
            let data = {
                file: icon_map.join(",")
            };
            this.$request.newZoneCoupons
                .uploadWeiXinTemporary(data)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.media_id = res.data.data;
                    }
                });
        },
        async push() {
            const data = {
                issue_cause: this.issue_cause,
                coupon_id: this.rowData.id,
                remark: this.remark
            };

            if (this.filelist.length) {
                data["file"] = this.filelist.join(",");
                data.media_id = this.media_id;
                const res = await this.$request.sms.importCouponUser(data);
                if (res.data.error_code == 0) {
                    this.$message.success("发放成功");
                    this.closePushCouponsStatus();
                }
            } else {
                data["uids"] = JSON.parse(
                    JSON.stringify(this.multipleSelection)
                ).map(item => {
                    return item.uid;
                });
                const res = await this.$request.coupons.pushCoupon(data);
                if (res.data.error_code == 0) {
                    this.$message.success("发放成功");
                    this.closePushCouponsStatus();
                }
            }

            //发放
        },
        closePushCouponsStatus() {
            this.$emit("closePushCouponsStatus");
        },
        async getUserList() {
            let data = {
                ...this.form
            };
            let res = await this.$request.user.getUserList(data);
            console.log(res);
            this.tableData = res.data.data.list;
            this.total = res.data.data.total;
        },
        search() {
            this.form.page = 1;
            this.getUserList();
        },
        handleSelectionChange(val) {
            this.multipleSelection_log = val;
            this.multipleSelection = [];
            val.map(i => {
                this.multipleSelection.push({
                    uid: i.uid,
                    nickname: i.nickname
                });
            });
        },
        del(index, row) {
            this.multipleSelection.splice(index, 1);
            let obj = this.multipleSelection_log.find(item => {
                return item.uid == row.uid;
            });
            this.$refs.multipleTable.toggleRowSelection(obj);
        },
        downloadTemp() {
            window.location.href =
                "https://images.vinehoo.com/vinehoo/vos/marketing/template/%E4%BC%98%E6%83%A0%E5%88%B8%E5%AF%BC%E5%85%A5%E7%94%A8%E6%88%B7.xls";
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getUserList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getUserList();
        }
    }
};
</script>
<style lang="scss" scoped>
.push-coupons-layout {
    .pagination-block {
        text-align: center;
        margin: 10px 0;
        display: flex;
        justify-content: center;
    }
    .push-coupons-form {
        display: flex;
    }
    .actions {
        & > div {
            margin-bottom: 10px;
        }
        .buttons {
            float: right;
        }
    }
}
</style>
