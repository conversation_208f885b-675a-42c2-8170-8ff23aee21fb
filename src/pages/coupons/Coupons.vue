<template>
    <div class="article-layout">
        <el-card>
            <div class="article-form">
                <!-- <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="优惠券名称"
                        size="mini"
                        @keyup.enter.native="search"
                        v-model="form.coupon_name"
                        clearable
                    ></el-input>
                </div> -->
                <div>
                    <el-select
                        class="m-r-10 w-mini"
                        v-model="form.classify_id"
                        filterable
                        size="mini"
                        placeholder="优惠券父类型"
                        clearable
                        filtered
                    >
                        <el-option
                            v-for="item in classifyList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
                <div>
                    <el-select
                        class="m-r-10 w-normal"
                        v-model="form.coupon_type"
                        filterable
                        size="mini"
                        placeholder="优惠券子类型"
                        clearable
                        filtered
                    >
                        <el-option
                            v-for="item in couponTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </div>
                <div>
                    <el-input
                        oninput="value=value.replace(/[^0-9]/g,'')"
                        class="w-normal m-r-10"
                        placeholder="优惠券ID"
                        @keyup.enter.native="search"
                        clearable
                        v-model="form.id"
                        size="mini"
                    ></el-input>
                </div>
                <div>
                    <el-select
                        class="m-r-10 w-mini"
                        v-model="form.status"
                        filterable
                        size="mini"
                        placeholder="状态"
                        clearable
                    >
                        <el-option
                            v-for="(item, index) in statusOptions"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-input
                        class="w-normal m-r-10"
                        placeholder="请输入金额"
                        @keyup.enter.native="search"
                        clearable
                        v-model="form.coupon_face_value"
                        size="mini"
                    ></el-input>
                </div>
                <div class="form-actions">
                    <el-button size="mini" @click="search" type="warning"
                        >查询</el-button
                    >
                    <el-button
                        size="mini"
                        type="success"
                        @click="dialogVisible = true"
                        >新增</el-button
                    >
                </div>
            </div>
        </el-card>
        <div class="article-main">
            <el-card>
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        prop="id"
                        label="ID"
                        width="90"
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="coupon_scope"
                        label="使用范围"
                        align="center"
                        width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="classify_id"
                        align="center"
                        label="类型"
                        min-width="90"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="coupon_name"
                        label="优惠券名称"
                        min-width="200"
                        align="center"
                    >
                        <template slot-scope="row">
                            <div>{{ row.row.coupon_name }}</div>
                            <div
                                v-if="row.row.relation_id"
                                style="color:#F56C6C"
                            >
                                {{ setLabel(row.row.coupon_name)
                                }}{{ row.row.relation_id }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="remark"
                        label="备注"
                        min-width="120"
                        show-overflow-tooltip
                        align="center"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="coupon_face_value"
                        align="center"
                        label="价值"
                        width="90"
                    >
                        <template slot-scope="row">
                            {{ row.row | coupon_face_valueFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="threshold_price"
                        align="center"
                        label="启用金额"
                        width="90"
                    >
                        <template slot-scope="row">
                            {{
                                row.row.threshold_price | threshold_priceFormat
                            }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="validity_days"
                        label="有效天数"
                        width="90"
                        align="center"
                    >
                        <template slot-scope="row">
                            {{ row.row.validity_days | validity_daysFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="effected_time"
                        align="center"
                        label="时间"
                        width="230"
                    >
                        <template slot-scope="row">
                            <div>生效时间：{{ row.row.effected_time }}</div>
                            <div>失效时间：{{ row.row.invalidate_time }}</div>
                            <div>创建时间：{{ row.row.created_time }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="admin_name"
                        align="center"
                        label="创建人"
                        width="80"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        align="center"
                        label="操作"
                        fixed="right"
                        width="100"
                    >
                        <template slot-scope="row">
                            <el-button-group>
                                <el-button
                                    size="mini"
                                    type="text"
                                    v-show="row.row.status == '正常'"
                                    @click="updataStatus(row.row, 2)"
                                    >禁用</el-button
                                >
                                <el-button
                                    style="margin-left: 10px"
                                    v-show="row.row.status == '禁用'"
                                    size="mini"
                                    type="text"
                                    @click="updataStatus(row.row, 1)"
                                    >开启</el-button
                                >
                                <el-button
                                    style="margin-left: 10px"
                                    size="mini"
                                    type="text"
                                    v-show="row.row.status == '正常'"
                                    @click="openPushCoupons(row.row)"
                                    >发放</el-button
                                >
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <div>
            <el-dialog
                title="用户列表"
                :close-on-click-modal="false"
                :visible.sync="PushCouponsStatus"
                width="70%"
            >
                <PushCoupons
                    @closePushCouponsStatus="PushCouponsStatus = false"
                    :rowData="rowData"
                    v-if="PushCouponsStatus"
                ></PushCoupons>
            </el-dialog>
        </div>
        <div>
            <el-dialog
                title="新增优惠券"
                :close-on-click-modal="false"
                :visible.sync="dialogVisible"
                width="70%"
            >
                <createCoupons
                    @updateList="getCouponList"
                    @cancelDialog="dialogVisible = false"
                    :config="config"
                    v-if="dialogVisible"
                ></createCoupons>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import PushCoupons from "./pushCoupons.vue";
import createCoupons from "./createCoupons.vue";
export default {
    components: {
        PushCoupons,
        createCoupons
    },
    data() {
        return {
            rowData: {},
            dialogVisible: false,
            tableData: [],
            PushCouponsStatus: false,
            config: [],
            form: {
                page: 1,
                limit: 10,
                status: "",
                id: "",
                coupon_name: "",
                classify_id: "",
                coupon_type: "",
                coupon_face_value: ""
            },
            statusOptions: [
                {
                    label: "正常",
                    value: 1
                },
                {
                    label: "禁用",
                    value: 2
                }
            ],
            total: 0,
            classifyList: [],
            couponTypeList: []
        };
    },
    filters: {
        coupon_face_valueFormat(val) {
            if (val.coupon_face_value == 0) {
                return "全额";
            } else {
                // return val.coupon_face_value;
                if (val.classify_id === "直折券") {
                    return val.coupon_face_value + "折";
                } else {
                    return val.coupon_face_value + "元";
                }
            }
        },
        validity_daysFormat(val) {
            if (val == 0 || !val) {
                return "-";
            } else {
                return val + "天";
            }
        },
        threshold_priceFormat(val) {
            if (val == 0) {
                return "无门槛";
            } else {
                return val + "元";
            }
        }
    },
    mounted() {
        this.getConfig();
        this.getCouponList();
    },
    methods: {
        search() {
            this.form.page = 1;
            this.getCouponList();
        },
        openPushCoupons(row) {
            this.rowData = row;
            this.PushCouponsStatus = true;
        },
        async getConfig() {
            let res = await this.$request.coupons.getConfig();
            if (res.data.error_code == 0) {
                this.config = res.data.data.list;
                const list = res.data.data.list.reduce(
                    (prev, curr) => prev.concat(curr.coupon_list),
                    []
                );
                console.log("list", list);
                this.classifyList = list
                    .map(item => ({
                        label: item.classify_name,
                        value: item.classify_id
                    }))
                    .reduce(
                        (prev, curr) =>
                            prev.some(item => item.value === curr.value)
                                ? prev
                                : prev.concat(curr),
                        []
                    )
                    .sort((itemA, itemB) => itemA.value - itemB.value);
                this.couponTypeList = list
                    .map(item => ({
                        label: item.title,
                        value: item.id
                    }))
                    .reduce(
                        (prev, curr) =>
                            prev.some(item => item.value === curr.value)
                                ? prev
                                : prev.concat(curr),
                        []
                    )
                    .sort((itemA, itemB) => itemA.value - itemB.value);
            }
        },
        setLabel(title) {
            if (title.includes("指定活动")) {
                return "活动ID：";
            } else if (title.includes("指定商品")) {
                return "商品期数：";
            } else if (title.includes("指定酒会")) {
                return "酒会ID：";
            }
        },
        async updataStatus(row, status) {
            console.log(row, status);
            let data = {
                id: row.id,
                status
            };
            let res = await this.$request.coupons.updateStatus(data);
            if (res.data.error_code == 0) {
                this.getCouponList();
                this.$message.success("操作成功");
            }
        },
        async getCouponList() {
            let data = {
                ...this.form
            };
            let result = await this.$request.coupons.getCouponList(data);
            if (result.data.error_code == 0) {
                this.total = result.data.data.total;
                this.tableData = result.data.data.list;
            }
            console.log(result);
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getCouponList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getCouponList();
        }
    }
};
</script>
<style lang="scss" scoped>
.article-layout {
    .article-main {
        margin: 10px 0;

        /deep/ .el-table .warning-row {
            background: oldlace;
        }

        /deep/ .el-table .danger-row {
            background: oldlace;
        }

        /deep/ .el-table .success-row {
            background: #f0f9eb;
        }
    }

    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .article-form {
        & > div {
            display: inline-block;
        }
    }
}
</style>
