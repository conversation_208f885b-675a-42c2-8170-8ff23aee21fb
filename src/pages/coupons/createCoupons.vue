<template>
    <div class="coupons-layout">
        <el-steps :active="active" finish-status="success" align-center>
            <el-step title="选择优惠券使用范围"> </el-step>
            <el-step title="选择优惠券类型"></el-step>
            <el-step title="完成"></el-step>
        </el-steps>
        <div class="coupons-main">
            <div class="coupons-step1" v-if="active == 0">
                <div style="margin-top: 20px">
                    <el-radio-group v-model="form.coupon_scope" size="medium">
                        <el-radio-button
                            v-for="(item, index) in config"
                            :key="index"
                            :label="item.id"
                        >
                            {{ item.name }}</el-radio-button
                        >
                    </el-radio-group>
                </div>
                <el-button
                    type="primary"
                    style="margin-top: 60px;"
                    @click="nextGroup()"
                    >下一步</el-button
                >
                <el-button @click="cancelDialog">取消</el-button>
            </div>
            <div class="coupons-step2" v-if="active == 1">
                <div
                    v-for="(item, index) in typeList"
                    :key="index"
                    @click="nextType(index, item.id)"
                >
                    <el-card class="box-card" shadow="hover">
                        <p>
                            <el-link :underline="false" type="info">{{
                                item.title
                            }}</el-link>

                            <el-tag style="float: right;">{{
                                item.classify_name
                            }}</el-tag>
                        </p>
                        <div>
                            <el-link :underline="false" type="info">
                                <span class="f-12">
                                    {{ item.desc }}
                                </span>
                            </el-link>
                        </div>
                    </el-card>
                </div>
            </div>

            <div class="coupons-step3" v-if="active == 2">
                <el-form ref="form" :model="form" label-width="140px">
                    <el-form-item label="优惠券类型">
                        <el-card class="box-card">
                            <div>
                                <el-link :underline="false" type="info">{{
                                    displayCard.title
                                }}</el-link>

                                <el-tag style="float: right;">{{
                                    displayCard.classify_name
                                }}</el-tag>
                            </div>
                            <div>
                                <el-link :underline="false" type="info">
                                    <span class="f-12">
                                        {{ displayCard.desc }}
                                    </span>
                                </el-link>
                            </div>
                        </el-card>
                    </el-form-item>
                    <el-form-item label="优惠券名称">
                        <el-input
                            class="input-large"
                            v-model="displayCard.title"
                            disabled
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="生效时间">
                        <el-date-picker
                            v-model="time_date"
                            type="datetimerange"
                            range-separator="至"
                            @change="time_dateChange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                        >
                        </el-date-picker>
                        <el-link class="link" :underline="false" type="info">
                            固定天数
                        </el-link>
                        <el-switch v-model="isFixed"></el-switch>
                    </el-form-item>
                    <el-form-item v-if="isFixed" label="使用时间">
                        <el-link class="link" :underline="false" type="info">
                            领取后
                        </el-link>
                        <el-input-number
                            v-model="form.validity_days"
                            :precision="0"
                            :min="1"
                            :max="365"
                        ></el-input-number>
                        <el-link class="link" :underline="false" type="info">
                            天
                        </el-link>
                    </el-form-item>
                    <el-form-item label="面额">
                        <el-input-number
                            v-model="form.coupon_face_value"
                            :precision="1"
                            :min="0"
                        ></el-input-number>
                        <el-link class="link" :underline="false" type="info">
                            <span>{{
                                displayCard.classify_name.includes("折")
                                    ? "折"
                                    : "元"
                            }}</span
                            ><span
                                v-if="displayCard.title.includes('运费减免')"
                            >
                                （ 设置0元 即是全减 ）</span
                            >
                        </el-link>
                    </el-form-item>
                    <el-form-item
                        label="使用条件"
                        v-if="isShow('threshold_price')"
                    >
                        <el-link class="link" :underline="false" type="info">
                            满
                        </el-link>
                        <el-input-number
                            v-model="form.threshold_price"
                            :precision="1"
                            :min="0"
                        ></el-input-number>
                        <el-link class="link" :underline="false" type="info">
                            可用
                        </el-link>
                    </el-form-item>
                    <el-form-item
                        :label="setLabel(displayCard.title)"
                        v-if="isShow('relation_id')"
                    >
                        <div>
                            <!-- <el-input v-else v-model="form.relation_id" placeholder="请输入ID"> -->
                            <el-select
                                v-if="displayCard.title.includes('指定活动')"
                                v-model="form.relation_id"
                                @change="valueChange"
                                filterable
                                clearable
                                default-first-option
                                placeholder="请选择活动"
                            >
                                <el-option
                                    v-for="item in activitiesTableData"
                                    :key="item.id"
                                    :label="item.activity_name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-input
                                v-else
                                v-model="form.relation_id"
                                placeholder="请输入ID"
                            >
                            </el-input>
                            <!-- <el-select
                                style="width:70%"
                                v-else
                                v-model="form.relation_id"
                                @change="valueChange"
                                filterable
                                allow-create
                                default-first-option
                                placeholder="最多选择5个"
                            >
                            </el-select> -->
                        </div>
                    </el-form-item>
                    <el-form-item label="备注">
                        <el-input
                            type="textarea"
                            class="w-large"
                            v-model="form.remark"
                        ></el-input>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="onSubmit"
                            >立即创建</el-button
                        >
                        <el-button @click="cancelDialog">取消</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <!-- <el-button style="margin-top: 12px;" @click="next">下一步</el-button> -->
    </div>
</template>
<script>
export default {
    props: ["config"],
    data() {
        return {
            displayCard: {},
            active: 0,
            field: [],
            time_date: [],
            activitiesQueryform: {
                page: 1,
                limit: 10000
            },
            activitiesTableData: [],
            isFixed: false,
            form: {
                coupon_name: "",
                validity_days: "",
                effected_time: "",
                relation_id: "",
                threshold_price: "",
                coupon_type: "",
                invalidate_time: "",
                coupon_scope: "",
                coupon_face_value: "",
                remark: ""
            },
            typeList: [] // 优惠券类型
        };
    },
    mounted() {
        if (this.config.length) {
            this.form.coupon_scope = this.config[0].id;
        }
        this.getActivitiesList();
    },
    methods: {
        getActivitiesList() {
            this.$request.coupons
                .getActivitiesList(this.activitiesQueryform)
                .then(result => {
                    if (result.data.error_code == 0) {
                        console.log(result.data.data);
                        this.activitiesTableData = result.data.data.list;
                    }
                });
        },
        cancelDialog() {
            this.$emit("cancelDialog");
        },
        updateList() {
            this.$emit("updateList");
        },
        onSubmit() {
            if (!this.isFixed) {
                this.form.validity_days = 0;
            }
            console.log(this.form);
            let data = {
                ...this.form,
                coupon_name: this.displayCard.title
            };
            this.$request.coupons.addCoupon(data).then(res => {
                console.log(res);
                if (res.data.error_code == 0) {
                    this.updateList();
                    this.cancelDialog();
                    this.$message.success("操作成功");
                }
            });
        },
        isShow(key) {
            let status = false;
            this.field.forEach(i => {
                if (i == key) {
                    status = true;
                }
            });
            return status;
        },
        typeChange(val) {
            this.form.relation_id = "";
            console.log(val);
        },
        valueChange(val) {
            console.log(val);
            val.map((item, index) => {
                if (!Number(item) || !Number.isInteger(Number(item))) {
                    val.splice(index, 1);
                }
            });
            let length = val.length;
            if (length > 5) {
                this.$message.error("最多只能选择5个噢");
            }
        },
        nextType(index, id) {
            console.log(id);
            // 选择类型
            this.form.coupon_type = id;
            this.field = this.typeList[index].field;
            this.displayCard = this.typeList[index];
            console.log(this.field);
            this.active = 2;
        },
        setLabel(title) {
            if (title.includes("指定活动")) {
                return "指定活动";
            } else if (title.includes("指定商品")) {
                return "指定商品";
            } else if (title.includes("指定酒会")) {
                return "指定酒会";
            }
        },
        nextGroup() {
            // 选择范围

            // this.form.coupon_scope = val;

            this.config.find((i, index) => {
                if (i.id == this.form.coupon_scope) {
                    this.typeList = this.config[index].coupon_list;
                    this.active = 1;
                }
            });
        },
        time_dateChange(val) {
            if (val) {
                this.form.effected_time = val[0];
                this.form.invalidate_time = val[1];
            } else {
                this.form.effected_time = "";
                this.form.invalidate_time = "";
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.coupons-layout {
    .coupons-main {
        .coupons-step2 {
            & > div {
                display: inline-block;
            }
        }
        .coupons-step1,
        .coupons-step2 {
            text-align: center;
        }
        .coupons-step2 {
            padding-top: 24px;
        }
        .coupons-step3 {
            margin-top: 20px;
            .link {
                margin: 0 10px;
                font-size: 10px;
            }
            .input-large {
                width: 300px;
            }
        }
        .coupons-step2,
        .coupons-step3 {
            .box-card {
                text-align: left;
                display: inline-block;
                margin: 6px 10px;
                width: 300px;
                .f-12 {
                    font-size: 8px;
                }
            }
        }
    }
}
</style>
