<template>
    <div>
        <el-form :model="form" :rules="formRules" ref="ruleForm">
            <el-form-item
                :label-width="formLabelWidth"
                size="mini"
                prop="reason"
            >
                <el-input
                    type="textarea"
                    :rows="3"
                    v-model="form.reason"
                    size="mini"
                    placeholder="请输入作废原因"
                ></el-input>
            </el-form-item>
            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["couponInvalidRowData"],
    data() {
        return {
            form: { reason: "" },
            formLabelWidth: "0px",
            formRules: {}
        };
    },
    mounted() {
        console.log(this.couponInvalidRowData);
    },
    methods: {
        // 点击取消
        closeDiog() {
            this.$emit("close");
        },

        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    let data = {
                        id: this.couponInvalidRowData.id,
                        reason: this.form.reason
                    };
                    this.$request.coupons.couponInvalid(data).then(res => {
                        if (res.data.error_code == 0) {
                            this.$message.success("成功");
                            this.closeDiog();
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
.el-form-item {
    margin-bottom: 0;
}
.is-always-shadow {
    box-shadow: none;
}
.el-card {
    width: 90%;
    // height: 470px;
    margin: 5px auto;
}
.el-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
/deep/ .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
</style>
