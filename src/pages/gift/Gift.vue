<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    class="w-normal m-r-10"
                    size="mini"
                    clearable
                    @keyup.enter.native="search"
                    v-model="form.name"
                    placeholder="活动名称"
                ></el-input>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="form.object"
                    filterable
                    size="mini"
                    placeholder="活动对象"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in objectOptions"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>

                <el-button @click="reset" size="mini">重置</el-button>
                <div class="action-btn">
                    <el-button
                        type="success"
                        size="mini"
                        @click="dialogStatus = true"
                        >新增营销</el-button
                    >
                    <el-button type="warning" size="mini" @click="search"
                        >查询</el-button
                    >
                </div>
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="活动ID"
                        prop="id"
                        width="80"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="活动名称"
                        prop="name"
                        min-width="160"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="info"
                        align="center"
                        label="活动生效时间"
                        width="310"
                    >
                        <template slot-scope="row">
                            {{ row.row.start_time }} 至
                            {{ row.row.end_time }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="活动对象"
                        width="100"
                    >
                        <template slot-scope="row">
                            {{ row.row.object | objectFormat }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="活动启用来源"
                        width="150"
                    >
                        <template slot-scope="row">
                            {{ row.row.type | typeFormat }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="160"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >查看</el-button
                            >
                            <el-button
                                size="mini"
                                @click="updateEnable(row.row, 1)"
                                v-if="!row.row.is_enable"
                                type="text"
                                >开启</el-button
                            >
                            <el-button
                                size="mini"
                                type="text"
                                @click="updateEnable(row.row, 0)"
                                v-if="row.row.is_enable"
                                >关闭</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="满减营销活动配置信息"
                :visible.sync="dialogStatus"
                width="70%"
            >
                <add v-if="dialogStatus" @close="close"></add>
            </el-dialog>
        </div>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="满减营销活动配置信息"
                :visible.sync="viewDialogStatus"
                width="70%"
                :before-close="closeViewDialogStatus"
            >
                <views :rowData="rowData" v-if="viewDialogStatus"></views>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.page"
                :page-size="form.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import add from "./add.vue";
import views from "./view.vue";
export default {
    components: { add, views },

    data() {
        return {
            rowData: {},
            tableData: [],
            objectOptions: [
                {
                    label: "酒云网",
                    value: 1
                }
                // {
                //     label: "三方平台",
                //     value: 2
                // }
            ],
            dialogStatus: false,
            viewDialogStatus: false,
            form: {
                page: 1,
                limit: 10,
                object: "",
                name: ""
            },
            total: 0
        };
    },
    methods: {
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getGiftList();
        },
        async updateEnable(row, status) {
            let data = {
                id: row.id,
                is_enable: status
            };
            const res = await this.$request.gift.changeStatus(data);
            if (res.data.error_code == 0) {
                console.log(res.data);
                this.$message.success("操作成功");
                this.getGiftList();
            }
            console.log(data);
        },
        view(row) {
            console.log(row);
            this.viewDialogStatus = true;
            this.rowData = row;
        },
        getGiftList() {
            let data = {
                ...this.form
            };
            this.$request.gift.getGiftList(data).then(res => {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    this.total = res.data.data.total;
                    this.tableData = res.data.data.list;
                }
            });
        },
        search() {
            this.form.page = 1;
            this.getGiftList();
        },
        reset() {
            this.$confirm("您确定要重置所有筛选条件吗?", "重置操作", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.form.page = 1;
                this.form.object = "";
                this.form.name = "";
                this.getGiftList();
            });
        },
        close() {
            this.dialogStatus = false;
            this.getGiftList();
        },
        handleSizeChange(val) {
            this.form.page = 1;
            this.form.limit = val;
            this.getGiftList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.form.page = val;
            this.getGiftList();
        }
    },
    mounted() {
        this.getGiftList();
    },
    filters: {
        typeFormat(val) {
            let typeOptions = [
                {
                    label: "酒云网全场频道",
                    value: 1
                },
                {
                    label: "闪购频道",
                    value: 2
                },
                {
                    label: "秒发频道",
                    value: 3
                },
                {
                    label: "指定活动",
                    value: 4
                },
                {
                    label: "指定商品",
                    value: 5
                }
            ];
            let type = typeOptions.find(i => i.value == val);
            if (type) {
                return type.label;
            } else {
                return "";
            }
        },
        objectFormat(val) {
            if (val == 1) {
                return "酒云网";
            } else if (val == 2) {
                return "三方";
            } else {
                return "-";
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
