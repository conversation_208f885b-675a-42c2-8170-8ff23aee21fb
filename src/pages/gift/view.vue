<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
        >
            <el-form-item label="活动名称" prop="name">
                <el-input
                    size="mini"
                    disabled
                    class="w-large"
                    v-model="ruleForm.name"
                ></el-input>
                <el-checkbox
                    v-model="ruleForm.is_display_label"
                    :true-label="1"
                    :false-label="0"
                    style="margin-left: 10px;"
                    disabled
                    >是否展示标签</el-checkbox
                >
            </el-form-item>
            <el-form-item label="活动时间" prop="time">
                <el-date-picker
                    disabled
                    v-model="ruleForm.time"
                    type="datetimerange"
                    range-separator="至"
                    size="mini"
                    @change="timeChange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="活动开始日期"
                    end-placeholder="活动结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item label="活动备注" prop="remark">
                <el-input
                    class="w-large"
                    disabled
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    placeholder="请输入内容"
                    v-model="ruleForm.remark"
                >
                </el-input>
            </el-form-item>
            <el-form-item label="活动对象" prop="object">
                <el-select
                    size="mini"
                    disabled
                    v-model="ruleForm.object"
                    placeholder="请选择活动对象"
                >
                    <el-option label="酒云网" :value="1"></el-option>
                    <!-- <el-option label="三方" :value="2"></el-option> -->
                </el-select>
            </el-form-item>
            <el-form-item label="活动范围" prop="type">
                <el-radio-group v-model="ruleForm.type" @change="typeChange">
                    <el-radio
                        disabled
                        v-for="(item, index) in typeOptions"
                        :key="index"
                        :label="item.value"
                    >
                        {{ item.label }}</el-radio
                    >
                </el-radio-group>
            </el-form-item>
            <el-form-item
                :label="labelFormat()"
                prop="assign"
                v-if="ruleForm.type == 4 || ruleForm.type == 5"
            >
                <el-select
                    v-if="ruleForm.type == 4"
                    v-model="ruleForm.assign"
                    multiple
                    filterable
                    disabled
                    clearable
                    default-first-option
                    placeholder="请选择活动"
                >
                    <el-option
                        v-for="item in activitiesTableData"
                        :key="item.id"
                        :label="item.activity_name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-input
                    v-if="ruleForm.type == 5"
                    disabled
                    class="w-large"
                    size="mini"
                    placeholder="请输入指定ID用英文，隔开"
                    v-model="ruleForm.assign"
                >
                </el-input>
            </el-form-item>
            <el-form-item
                label="优惠内容"
                prop="rule"
                class="rule"
                v-if="ruleForm.type"
            >
                <!-- <el-button size="mini" type="success" @click="addRule"
                    >新增规则</el-button
                > -->
                <el-card
                    class="box-card"
                    v-for="(item, index) in ruleForm.rule"
                    :key="index"
                >
                    <div slot="header" class="clearfix">
                        <el-link :underline="false"
                            >规则{{ index + 1 }}</el-link
                        >
                        <!-- <el-button
                            size="mini"
                            style="float:right"
                            type="danger"
                            v-if="index != 0"
                            @click="removeRule(index)"
                            icon="el-icon-delete"
                            circle
                        ></el-button> -->
                    </div>
                    <div class="text item">
                        <div class="form">
                            <el-link :underline="false">单订单满</el-link>
                            <el-input-number
                                v-model="item.fill"
                                :min="0"
                                disabled
                                size="mini"
                                :precision="2"
                            ></el-input-number>
                        </div>
                        <div class="form">
                            <el-link :underline="false">减少金额</el-link>
                            <el-input-number
                                v-model="item.decrease"
                                :min="0"
                                size="mini"
                                disabled
                                :precision="2"
                            ></el-input-number>
                        </div>
                        <div class="form" v-if="ruleForm.rule.length == 1">
                            <el-link :underline="false">满减次数</el-link>
                            <el-input-number
                                v-model="item.stacking_numb"
                                :min="0"
                                size="mini"
                                disabled
                                :precision="0"
                            ></el-input-number>
                            <el-link style="margin-left:6px" :underline="false">
                                (0即是不限次数)</el-link
                            >
                        </div>
                    </div>
                </el-card>
            </el-form-item>
            <el-form-item label="上传图片">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    dir="vinehoo/vos/marketing/"
                    :file-list="fileList"
                    disabled
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item label="跳转链接">
                <el-input
                    v-model="ruleForm.top_image_skip_url"
                    size="mini"
                    disabled
                    class="w-large"
                ></el-input>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import VosOss from "vos-oss";

export default {
    components: {
        VosOss
    },
    props: ["rowData"],
    data() {
        return {
            typeOptions: [
                {
                    label: "酒云网全场频道",
                    value: 1
                },
                {
                    label: "闪购频道",
                    value: 2
                },
                {
                    label: "秒发频道",
                    value: 3
                },
                {
                    label: "指定活动",
                    value: 4
                }
            ],
            activitiesQueryform: {
                page: 1,
                limit: 10000
            },
            activitiesTableData: [],
            ruleForm: {
                name: "",
                time: [],
                assign: "",
                rule: [],
                remark: "",
                object: "",
                is_superposition: 1,
                end_time: "",
                start_time: "",
                type: ""
            },
            rules: {
                rule: [
                    {
                        required: true,
                        message: "优惠内容不能为空",
                        trigger: "change"
                    }
                ],
                name: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur"
                    },
                    {
                        min: 2,
                        max: 10,
                        message: "活动名称字符长度在 2 到 10 个字符",
                        trigger: "blur"
                    }
                ],
                time: [
                    {
                        required: true,
                        message: "请选择时间",
                        trigger: "change"
                    }
                ],
                type: [
                    {
                        required: true,
                        message: "请选择活动范围",
                        trigger: "change"
                    }
                ],
                object: [
                    {
                        required: true,
                        message: "请选择活动区域",
                        trigger: "change"
                    }
                ]
            },
            fileList: []
        };
    },
    mounted() {
        console.log(this.rowData);
        this.ruleForm = this.rowData;
        this.ruleForm.time = [this.rowData.start_time, this.rowData.end_time];
        if (this.ruleForm.type == 4 && this.ruleForm.assign) {
            this.getActivitiesList();
            let arr = [];
            this.ruleForm.assign.split(",").map(i => {
                arr.push(Number(i));
            });
            this.ruleForm.assign = arr;
        }
        this.ruleForm.rule = JSON.parse(JSON.stringify(this.rowData.rule));
        this.fileList = this.ruleForm.top_image
            ? [this.ruleForm.top_image]
            : [];
    },
    methods: {
        getActivitiesList() {
            this.$request.activities
                .getActivitiesList(this.activitiesQueryform)
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.activitiesTableData = result.data.data.list;
                    }
                });
        },
        labelFormat() {
            let type = this.ruleForm.type;
            if (type == 4 || type == 5) {
                return type == 4 ? "指定活动" : "指定商品";
            } else {
                return "";
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    // this.ruleForm.is_superposition =
                    if (this.ruleForm.rule.length == 1) {
                        this.ruleForm.is_superposition = 1;
                        if (!this.ruleForm.rule[0].stacking_numb) {
                            this.ruleForm.rule[0].stacking_numb = 0;
                        }
                    } else {
                        this.ruleForm.is_superposition = 0;
                        this.ruleForm.rule.map(i => {
                            delete i["stacking_numb"];
                        });
                    }
                    console.log(this.ruleForm);

                    let data = {
                        ...this.ruleForm,
                        rule: JSON.stringify(this.ruleForm.rule)
                    };
                    this.$request.gift.addGift(data).then(res => {
                        console.log(res);
                    });
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        },
        addRule() {
            let obj = {};
            switch (this.ruleForm.type) {
                case 1:
                    obj = {
                        fill: 0,
                        decrease: 0,
                        stacking_numb: 0
                    };
                    this.ruleForm.rule.push(obj);
                    break;
                case 2:
                    obj = {
                        fill: 0,
                        decrease: 0,
                        stacking_numb: 0
                    };
                    this.ruleForm.rule.push(obj);
                    break;
                case 3:
                    obj = {
                        fill: 0,
                        decrease: 0,
                        stacking_numb: 0
                    };
                    this.ruleForm.rule.push(obj);
                    break;
                case 4:
                    obj = {
                        fill: 0,
                        decrease: 0,
                        stacking_numb: 0
                    };
                    this.ruleForm.rule.push(obj);
                    break;
                case 5:
                    obj = {
                        fill: 0,
                        decrease: 0,
                        stacking_numb: 0
                    };
                    this.ruleForm.rule.push(obj);
                    break;
                default:
                    this.$message.error("请选择活动范围");
            }
        },
        resetForm(formName) {
            this.$refs[formName].resetFields();
        },
        typeChange(val) {
            console.log(val);
            this.ruleForm.rule = [];
        },
        removeRule(index) {
            this.ruleForm.rule.splice(index, 1);
            console.log(this.ruleForm.rule.length);
            if (this.ruleForm.rule.length == 1) {
                this.ruleForm.rule[0].stacking_numb = 0;
            }
        },
        timeChange(val) {
            if (val) {
                this.ruleForm.start_time = val[0];
                this.ruleForm.end_time = val[1];
            } else {
                this.ruleForm.start_time = "";
                this.ruleForm.end_time = "";
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.rule {
    .text {
        font-size: 14px;
    }

    .item {
        margin-bottom: 18px;
    }

    .clearfix:before,
    .clearfix:after {
        display: table;
        content: "";
    }
    .clearfix:after {
        clear: both;
    }

    .box-card {
        width: 340px;
        margin-top: 10px;
        border-radius: 8px;
        /deep/ .el-card__header {
            padding: 8px 12px;
        }
        /deep/ .el-card__body {
            padding: 8px 12px 0 8px;
        }
    }
    /deep/ .el-form-item__content {
        line-height: normal;
    }
    .form {
        display: flex;
        margin-bottom: 4px;
        align-items: center;
        .el-link {
            margin-right: 4px;
        }
    }
}
</style>
