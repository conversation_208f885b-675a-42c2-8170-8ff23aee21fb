<template>
    <div>
        <el-form :model="outForm" :rules="outFormRules" ref="ruleForm">
            <el-form-item
                label="券包名称"
                :label-width="formLabelWidth"
                size="mini"
                prop="coupon_package_name"
            >
                <el-input
                    v-model="outForm.coupon_package_name"
                    size="mini"
                    placeholder="请输入路径名称"
                ></el-input>
            </el-form-item>

            <!-- 添加优惠券 -->
            <el-button
                type="primary"
                size="mini"
                style="margin-left:16%;margin-top:10px"
                @click="handleAddparams"
                >添加优惠券</el-button
            >
            <el-dialog
                width="50%"
                title="添加"
                :visible.sync="innerVisible"
                append-to-body
            >
                <el-form
                    :model="form"
                    :rules="formRules"
                    ref="addRuleForm"
                    label-width="100px"
                    class="demo-ruleForm"
                >
                    <el-card>
                        <el-form-item
                            label="优惠券"
                            prop="coupon_id"
                            :label-width="formLabelWidth"
                        >
                            <el-select
                                v-model="form.coupon_id"
                                placeholder="请选择优惠券"
                                filterable
                                size="mini"
                                style="width:60%"
                                @change="slectChange(form.coupon_id)"
                            >
                                <el-option
                                    v-for="item in CouponsOptions"
                                    :key="item.id"
                                    :label="item.coupon_name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            label="优惠券数量"
                            :label-width="formLabelWidth"
                            prop="quantity"
                        >
                            <el-input
                                type="quantity"
                                v-model.number="form.quantity"
                                size="mini"
                                placeholder="请设置优惠券数量"
                            ></el-input>
                        </el-form-item>
                    </el-card>
                    <el-button
                        type="primary"
                        size="mini"
                        style="margin-left:20%;margin-bottom:10px;margin-top:10px"
                        @click="handleAdd"
                        >添 加</el-button
                    >
                    <div class="addparams">
                        <el-card
                            v-for="(item, index) in this.form.couponData"
                            :key="index"
                        >
                            <!-- <el-form :model="item" :rules="paramRules"> -->
                            <el-form-item
                                label="优惠券"
                                :prop="`couponData.${index}.coupon_id`"
                                :rules="{
                                    required: true,
                                    message: '请选择优惠券',
                                    trigger: 'blur'
                                }"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="item.coupon_id"
                                    filterable
                                    placeholder="请选择优惠券"
                                    size="mini"
                                    style="width:60%"
                                    @change="
                                        addSlectChange(item.coupon_id, index)
                                    "
                                >
                                    <el-option
                                        v-for="item in CouponsOptions"
                                        :key="item.id"
                                        :label="item.coupon_name"
                                        :value="item.id"
                                    >
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="优惠券数量"
                                :label-width="formLabelWidth"
                                :prop="`couponData.${index}.quantity`"
                                :rules="[
                                    {
                                        required: true,
                                        message: '请设置优惠券数量',
                                        trigger: 'blur'
                                    },
                                    {
                                        type: 'number',
                                        message: '优惠券数量必须为数字值'
                                    }
                                ]"
                            >
                                <el-input
                                    :type="`couponData.${index}.quantity`"
                                    v-model.number="item.quantity"
                                    size="mini"
                                    placeholder="请设置优惠券数量"
                                ></el-input>
                            </el-form-item>
                            <!-- </el-form> -->

                            <el-popconfirm
                                title="确定删除吗？"
                                @confirm="delCoupons(index)"
                            >
                                <el-button
                                    v-if="isDelCoupons"
                                    slot="reference"
                                    type="danger"
                                    size="mini"
                                    style="margin-left:14%;"
                                    >删 除</el-button
                                >
                            </el-popconfirm>
                        </el-card>
                    </div>
                    <el-form-item style="margin-left:17%">
                        <el-button @click="innerVisible = false"
                            >取消</el-button
                        >
                        <el-button
                            type="primary"
                            @click="inSubmitForm('addRuleForm')"
                            >确定</el-button
                        >
                    </el-form-item>
                </el-form>
            </el-dialog>

            <!-- 列表 -->
            <div class="table" v-if="tableData.length">
                <el-card class="card" shadow="hover">
                    <el-table
                        border
                        size="mini"
                        :data="tableData"
                        style="width: 100%"
                    >
                        <el-table-column
                            align="center"
                            label="优惠券ID"
                            prop="coupon_id"
                            min-width="80"
                        >
                        </el-table-column>
                        <el-table-column
                            align="center"
                            label="优惠券名称"
                            prop="coupon_name"
                            min-width="180"
                        >
                            <!-- <template #default="imageUrl">
                            <el-image
                                style="width: 100px; height: 100px"
                                :src="imageUrl.row.icon"
                                fit="cover"
                            ></el-image>
                        </template> -->
                        </el-table-column>

                        <el-table-column
                            prop="quantity"
                            align="center"
                            label="数量"
                            min-width="150"
                        >
                        </el-table-column>
                        <el-table-column
                            prop="operator"
                            label="操作"
                            fixed="right"
                            min-width="150"
                            align="center"
                        >
                            <template slot-scope="row">
                                <el-button
                                    @click="view(row.row)"
                                    type="text"
                                    size="mini"
                                    :disabled="!row.row.id"
                                    >编辑</el-button
                                >
                                <el-popconfirm
                                    title="确定删除吗？"
                                    @confirm="del(row.row)"
                                >
                                    <el-button
                                        slot="reference"
                                        type="text"
                                        size="mini"
                                        style="margin-left:10%;"
                                        :disabled="!row.row.id"
                                        >删除</el-button
                                    >
                                </el-popconfirm>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </div>
            <el-empty v-else></el-empty>

            <div>
                <el-dialog
                    :close-on-click-modal="false"
                    title="编辑"
                    :visible.sync="viewDialogStatus"
                    width="30%"
                    :before-close="closeViewCoupons"
                    append-to-body
                >
                    <ViewCoupons
                        ref="viewTag"
                        v-if="viewDialogStatus"
                        :inRowData="inRowData"
                        @closeViewCoupons="closeViewCoupons"
                        @couponPackageDetail="couponPackageDetail"
                    ></ViewCoupons>
                    <!-- :rowData="rowData" -->
                </el-dialog>
            </div>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import ViewCoupons from "./viewCoupons.vue";
export default {
    components: {
        ViewCoupons
    },
    props: ["rowData"],
    data() {
        return {
            //点击添加优惠券的弹框
            innerVisible: false,
            // 点击列表编辑的弹框
            viewDialogStatus: false,
            tableData: [
                {
                    coupon_id: "",
                    coupon_name: "",
                    quantity: "",
                    operator: ""
                }
            ],
            //优惠券选择框内容
            CouponsOptions: [
                {
                    id: 1,
                    coupon_name: "5元新人优惠券",
                    coupon_face_value: "39.00"
                },
                {
                    id: 2,
                    coupon_name: "闪购频道优惠券",
                    coupon_face_value: "39.00"
                },
                {
                    id: 3,
                    coupon_name: "满减优惠券",
                    coupon_face_value: "39.00"
                }
            ],
            inRowData: {},
            // 外层Dailog的from
            outForm: {
                coupon_package_name: "",
                id: ""
            },
            outFormRules: {
                coupon_package_name: [
                    {
                        required: true,
                        message: "请输入券包名称",
                        trigger: "blur"
                    }
                ]
            },

            // 内层Dailog的from
            form: {
                coupon_id: "",
                quantity: "",
                couponData: []
            },
            formRules: {
                coupon_id: [
                    {
                        required: true,
                        message: "请选择优惠券",
                        trigger: "blur"
                    }
                ],
                quantity: [
                    {
                        required: true,
                        message: "请设置优惠券数量",
                        trigger: "blur"
                    },
                    { type: "number", message: "优惠券数量必须为数字值" }
                ]
            },
            coupon_name: "",
            coupon_face_value: "",
            isDelCoupons: false,
            formLabelWidth: "140px",
            dataAll: {}
        };
    },
    mounted() {
        console.log("编辑数据", this.rowData);
        this.outForm.coupon_package_name = this.rowData.coupon_package_name;
        this.outForm.id = this.rowData.id;

        this.getCouponList();
        this.couponPackageDetail();
    },
    watch: {},
    methods: {
        //优惠券列表
        async getCouponList() {
            let res = await this.$request.newZoneCoupons.getCouponList();
            if (res.data.error_code == 0) {
                // this.CouponsOptions = res.data.data.list;
                this.CouponsOptions = res.data.data.list.map(item => {
                    item.coupon_name = `${item.id}-${item.coupon_name}`;
                    return item;
                });
            }
        },
        //优惠券包详情
        async couponPackageDetail() {
            let res = await this.$request.newZoneCoupons.couponPackageDetail({
                id: this.rowData.id
            });
            if (res.data.error_code == 0) {
                this.tableData = JSON.parse(
                    JSON.stringify(res.data.data.package_details)
                );
            }
        },
        // 初始选择框
        slectChange(id) {
            let arr = this.CouponsOptions.find(item => {
                return item.id == id;
            });
            this.coupon_name = arr.coupon_name;
            this.coupon_face_value = arr.coupon_face_value;
        },
        //添加参数的选择框
        addSlectChange(id, index) {
            let arr = this.CouponsOptions.find(item => {
                return item.id == id;
            });
            this.form.couponData[index].coupon_name = arr.coupon_name;
            this.form.couponData[index].coupon_face_value =
                arr.coupon_face_value;
        },
        //打开内层新增Dialog
        handleAddparams() {
            this.innerVisible = true;
        },
        //增加优惠券数组
        handleAdd() {
            this.isDelCoupons = true;
            const obj = {
                coupon_id: "",
                quantity: ""
            };
            this.form.couponData.push(obj);
        },
        //点击编辑新增优惠券时弹框中的删除
        delCoupons(index) {
            this.form.couponData.splice(index, 1);
        },
        // 点击编辑弹框里列表数据的删除
        async del(row) {
            let res = await this.$request.newZoneCoupons.delCoupon({
                id: row.id
            });
            if (res.data.error_code == 0) {
                this.$Message.success("删除成功");
                this.couponPackageDetail();
            }
        },
        // 点击取消
        closeDiog() {
            this.$emit("closeViewDialogStatus");
        },
        //点击列表编辑打开弹框
        view(row) {
            this.viewDialogStatus = true;
            this.inRowData = row;
        },
        // 点击列表编辑关闭弹框
        closeViewCoupons() {
            this.viewDialogStatus = false;
        },
        // 内层表单提交
        inSubmitForm(addRuleForm) {
            this.$refs[addRuleForm].validate(valid => {
                if (valid) {
                    const data1 = {
                        coupon_id: this.form.coupon_id,
                        quantity: this.form.quantity,
                        coupon_name: this.coupon_name,
                        coupon_face_value: this.coupon_face_value
                    };
                    let arr = JSON.parse(JSON.stringify(this.form.couponData));
                    arr.unshift(data1);
                    let arr2 = arr;
                    let data = {
                        group: arr2
                    };
                    this.dataAll = {
                        // coupon_package_name: this.outForm.coupon_package_name,
                        couponData: [...this.tableData, ...data.group]
                    };
                    this.tableData = this.dataAll.couponData;
                    console.log("内层表单", this.dataAll);
                    this.$refs[addRuleForm].resetFields();
                    this.innerVisible = false;

                    // this.$request.newZoneCoupons
                    //     .editCouponPackage(dataAll)
                    //     .then(res => {
                    //         console.log("券包编辑返回结果", res);
                    //         if (res.data.error_code == 0) {
                    //             this.$Message.success(res.data.error_msg);
                    //             this.couponPackageDetail();
                    //             this.innerVisible = false;
                    //         }
                    //     });
                } else {
                    return false;
                }
            });
        },
        //外层表单提交
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    // this.$emit("getCouponPackageList");
                    // this.$emit("closeViewDialogStatus");

                    let data = {
                        id: this.outForm.id,
                        couponData: this.dataAll.couponData,
                        coupon_package_name: this.outForm.coupon_package_name
                    };
                    this.$request.newZoneCoupons
                        .editCouponPackage(data)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$Message.success(res.data.error_msg);
                                // this.couponPackageDetail();
                                // this.innerVisible = false;
                                this.$emit("getCouponPackageList");
                                this.$emit("closeViewDialogStatus");
                            }
                        });
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
.el-form-item {
    margin-bottom: 0;
}
.is-always-shadow {
    box-shadow: none;
}
.el-card {
    width: 90%;
    // height: 470px;
    margin: 5px auto;
}
.el-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
/deep/ .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
</style>
