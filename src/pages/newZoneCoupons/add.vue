<template>
    <div>
        <el-form :model="form" :rules="formRules" ref="ruleForm">
            <el-form-item
                label="券包名称"
                :label-width="formLabelWidth"
                size="mini"
                prop="coupon_package_name"
            >
                <el-input
                    v-model="form.coupon_package_name"
                    size="mini"
                    placeholder="请输入优惠券包名称"
                ></el-input>
            </el-form-item>

            <el-card style="margin-top:10px">
                <el-form-item
                    label="优惠券"
                    prop="coupon_id"
                    :label-width="formLabelWidth"
                >
                    <el-select
                        v-model="form.coupon_id"
                        filterable
                        placeholder="请选择优惠券"
                        size="mini"
                        style="width:60%"
                        @change="slectChange(form.coupon_id)"
                    >
                        <el-option
                            v-for="item in CouponsOptions"
                            :key="item.id"
                            :label="item.coupon_name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="优惠券数量"
                    :label-width="formLabelWidth"
                    prop="quantity"
                >
                    <el-input
                        type="quantity"
                        v-model.number="form.quantity"
                        size="mini"
                        placeholder="请设置优惠券数量"
                    ></el-input>
                </el-form-item>
            </el-card>
            <!-- 添加参数 -->
            <el-button
                type="primary"
                size="mini"
                style="margin-left:16%"
                @click="handleAddparams"
                >添 加</el-button
            >
            <div class="addparams">
                <el-card
                    v-for="(item, index) in this.form.couponData"
                    :key="index"
                >
                    <!-- <el-form :model="item" :rules="paramRules"> -->
                    <el-form-item
                        label="优惠券"
                        :prop="`couponData.${index}.coupon_id`"
                        :rules="{
                            required: true,
                            message: '请选择优惠券',
                            trigger: 'blur'
                        }"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="item.coupon_id"
                            filterable
                            placeholder="请选择优惠券"
                            size="mini"
                            style="width:60%"
                            @change="addSlectChange(item.coupon_id, index)"
                        >
                            <el-option
                                v-for="item in CouponsOptions"
                                :key="item.id"
                                :label="item.coupon_name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="优惠券数量"
                        :label-width="formLabelWidth"
                        :prop="`couponData.${index}.quantity`"
                        :rules="[
                            {
                                required: true,
                                message: '请设置优惠券数量',
                                trigger: 'blur'
                            },
                            {
                                type: 'number',
                                message: '优惠券数量必须为数字值'
                            }
                        ]"
                    >
                        <!-- message: '请设置优惠券数量', -->
                        <el-input
                            :type="`couponData.${index}.quantity`"
                            v-model.number="item.quantity"
                            size="mini"
                            placeholder="请设置优惠券数量"
                        ></el-input>
                    </el-form-item>
                    <!-- </el-form> -->

                    <el-popconfirm
                        title="确定删除吗？"
                        @confirm="delCoupons(index)"
                    >
                        <el-button
                            v-if="isDelCoupons"
                            slot="reference"
                            type="danger"
                            size="mini"
                            style="margin-left:10%;"
                            >删 除</el-button
                        >
                    </el-popconfirm>
                </el-card>
            </div>
            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        // const checkQuantity = (rule, value, callback) => {
        //     let reg = /^[1-9]*[1-9][0-9]*$/;
        //     if (value == "") {
        //         callback(new Error("请输入优惠券数量"));
        //     } else if (!reg.test(value)) {
        //         callback(new Error("请输入大于0的正整数"));
        //     } else {
        //         callback();
        //     }
        // };
        return {
            //优惠券选择框内容
            CouponsOptions: [
                {
                    id: 1,
                    coupon_name: "5元新人优惠券",
                    coupon_face_value: "39.00"
                },
                {
                    id: 2,
                    coupon_name: "闪购频道优惠券",
                    coupon_face_value: "39.00"
                },
                {
                    id: 3,
                    coupon_name: "满减优惠券",
                    coupon_face_value: "39.00"
                }
            ],
            // 是否显示删除按钮
            isDelCoupons: false,
            form: {
                coupon_package_name: "",
                coupon_id: "",
                quantity: "",
                couponData: []
            },
            coupon_name: "",
            coupon_face_value: "",
            formRules: {
                coupon_package_name: [
                    {
                        required: true,
                        message: "请输入券包名称",
                        trigger: "blur"
                    }
                ],
                coupon_id: [
                    {
                        required: true,
                        message: "请选择优惠券",
                        trigger: "blur"
                    }
                ],
                quantity: [
                    {
                        required: true,
                        message: "请设置优惠券数量",
                        // validator: checkQuantity,
                        trigger: "blur"
                    },
                    { type: "number", message: "优惠券数量必须为数字值" }
                ]
            },
            formLabelWidth: "140px"
        };
    },
    mounted() {
        this.getCouponList();
    },
    watch: {},
    methods: {
        //优惠券列表
        async getCouponList() {
            let res = await this.$request.newZoneCoupons.getCouponList();
            if (res.data.error_code == 0) {
                this.CouponsOptions = res.data.data.list.map(item => {
                    item.coupon_name = `${item.id}-${item.coupon_name}`;
                    return item;
                });
            }
        },

        //添加优惠券参数
        handleAddparams() {
            this.isDelCoupons = true;
            const obj = {
                coupon_id: "",
                quantity: ""
            };
            this.form.couponData.push(obj);
        },
        // 初始选择框
        slectChange(id) {
            let arr = this.CouponsOptions.find(item => {
                return item.id == id;
            });
            this.coupon_name = arr.coupon_name;
            this.coupon_face_value = arr.coupon_face_value;
        },
        //添加参数的选择框
        addSlectChange(id, index) {
            let arr = this.CouponsOptions.find(item => {
                return item.id == id;
            });
            this.form.couponData[index].coupon_name = arr.coupon_name;
            this.form.couponData[index].coupon_face_value =
                arr.coupon_face_value;
        },
        // 删除优惠券参数
        delCoupons(index) {
            this.form.couponData.splice(index, 1);
        },
        // 点击取消
        closeDiog() {
            this.$emit("close");
        },

        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    const data1 = {
                        coupon_id: this.form.coupon_id,
                        quantity: this.form.quantity,
                        coupon_name: this.coupon_name,
                        coupon_face_value: this.coupon_face_value
                    };
                    let arr = JSON.parse(JSON.stringify(this.form.couponData));
                    arr.unshift(data1);
                    let arr2 = arr;
                    let data = {
                        coupon_package_name: this.form.coupon_package_name,
                        couponData: arr2
                    };
                    this.$request.newZoneCoupons
                        .addCouponPackage(data)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$Message.success("添加成功");
                                this.$emit("close");
                                this.$emit("getCouponPackageList");
                            }
                        });
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
.el-form-item {
    margin-bottom: 0;
}
.is-always-shadow {
    box-shadow: none;
}
.el-card {
    width: 90%;
    // height: 470px;
    margin: 5px auto;
}
.el-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
/deep/ .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
</style>
