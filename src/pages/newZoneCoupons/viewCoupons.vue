<template>
    <div>
        <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
        >
            <el-form-item
                label="优惠券"
                :label-width="formLabelWidth"
                prop="coupon_id"
            >
                <el-select
                    v-model="ruleForm.coupon_id"
                    filterable
                    placeholder="请选择优惠券"
                    size="mini"
                    style="width:60%"
                    disabled
                >
                    <el-option
                        v-for="item in CouponsOptions"
                        :key="item.id"
                        :label="item.coupon_name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="优惠券数量"
                :label-width="formLabelWidth"
                prop="quantity"
            >
                <el-input
                    type="quantity"
                    v-model.number="ruleForm.quantity"
                    size="mini"
                    placeholder="请设置优惠券数量"
                    style="width:60%"
                ></el-input>
            </el-form-item>

            <el-form-item style="margin-left:18%">
                <el-button @click="close()">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["inRowData"],
    data() {
        return {
            //优惠券选择框内容
            CouponsOptions: [],
            ruleForm: {
                coupon_name: "",
                coupon_id: "",
                quantity: "",
                id: ""
            },
            rules: {
                coupon_id: [
                    {
                        required: true,
                        message: "请选择优惠券",
                        trigger: "blur"
                    }
                ],
                quantity: [
                    {
                        required: true,
                        message: "请设置优惠券数量",
                        trigger: "blur"
                    },
                    { type: "number", message: "优惠券数量必须为数字值" }
                ]
            },
            formLabelWidth: "140px"
        };
    },
    mounted() {
        this.getCouponList();
        console.log("编辑数据", this.inRowData);
        this.ruleForm.id = this.inRowData.id;
        this.ruleForm.coupon_name = this.inRowData.coupon_name;
        this.ruleForm.coupon_id = this.inRowData.coupon_id;
        this.ruleForm.quantity = this.inRowData.quantity;
    },
    methods: {
        // // 初始选择框
        // slectChange(id) {
        //     console.log("改变了", id);
        //     let arr = this.CouponsOptions.find(item => {
        //         return item.id == id;
        //     });
        //     this.ruleForm.coupon_name = arr.coupon_name;
        //     // this.coupon_face_value = arr.coupon_face_value;
        //     console.log("选择了", arr);
        // },
        async getCouponList() {
            let res = await this.$request.newZoneCoupons.getCouponList();
            if (res.data.error_code == 0) {
                // this.CouponsOptions = res.data.data.list;
                this.CouponsOptions = res.data.data.list.map(item => {
                    item.coupon_name = `${item.id}-${item.coupon_name}`;
                    return item;
                });
            }
        },
        close() {
            this.$emit("closeViewCoupons");
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    let data = {
                        id: this.ruleForm.id,
                        quantity: this.ruleForm.quantity
                    };
                    this.$request.newZoneCoupons
                        .editCouponPackageDetail(data)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$Message.success(res.data.error_msg);
                                this.$emit("couponPackageDetail");
                                this.close();
                            }
                        });
                } else {
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
