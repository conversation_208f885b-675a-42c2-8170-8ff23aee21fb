<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    v-model="id"
                    placeholder="请输入ID"
                    size="mini"
                    @keyup.enter.native="search"
                    class="m-r-10 w-mini"
                ></el-input>
                <el-input
                    @keyup.enter.native="search"
                    v-model="coupon_package_name"
                    size="mini"
                    placeholder="请输入券包名称"
                    class="m-r-10 w-large"
                ></el-input>
                <el-button type="primary" size="mini" @click="search()"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >添加</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="ID"
                        prop="id"
                        min-width="80"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="券包名称"
                        prop="coupon_package_name"
                        min-width="180"
                    >
                        <!-- <template #default="imageUrl">
                            <el-image
                                style="width: 100px; height: 100px"
                                :src="imageUrl.row.icon"
                                fit="cover"
                            ></el-image>
                        </template> -->
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="价值"
                        prop="coupon_package_value"
                        min-width="100"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="coupon_quantity"
                        align="center"
                        label="数量"
                        min-width="150"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="创建人"
                        min-width="80"
                        prop="admin_name"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="创建时间"
                        min-width="160"
                        prop="created_time"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="状态"
                        min-width="160"
                        prop="status"
                    >
                        <template #default="status">
                            <span>{{
                                `${status.row.status == 1 ? "启用" : "禁用"}`
                            }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="operator"
                        label="操作"
                        fixed="right"
                        width="250"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                            <el-popconfirm
                                confirm-button-text="确定"
                                cancel-button-text="取消"
                                title="确定改变状态吗？"
                                @confirm="
                                    updateEnable(
                                        row.row,
                                        row.row.status == 2 ? '1' : '2'
                                    )
                                "
                            >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    v-if="row.row.status == 2"
                                    type="text"
                                    style="margin-left:10px"
                                    >启用</el-button
                                >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    type="text"
                                    v-if="row.row.status == 1"
                                    style="margin-left:10px"
                                    >禁用</el-button
                                >
                            </el-popconfirm>
                            <!-- <el-button
                                @click="view(row.row)"
                                type="primary"
                                size="mini"
                                style="margin-left:10px"
                                >发放</el-button
                            > -->
                            <el-button
                                size="mini"
                                type="text"
                                @click="openPushCoupons(row.row)"
                                style="margin-left:10px"
                                >发放</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="新增券包"
                :visible.sync="dialogStatus"
                width="60%"
            >
                <Add
                    ref="addCoupons"
                    v-if="dialogStatus"
                    @close="close"
                    @getCouponPackageList="getCouponPackageList"
                ></Add>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑券包"
                :visible.sync="viewDialogStatus"
                width="60%"
                :before-close="closeViewDialogStatus"
            >
                <Views
                    ref="viewTag"
                    v-if="viewDialogStatus"
                    :isEdit="isEdit"
                    :rowData="rowData"
                    @closeViewDialogStatus="closeViewDialogStatus"
                    @getCouponPackageList="getCouponPackageList"
                ></Views>
            </el-dialog>
        </div>
        <!-- 发放优惠券包 -->
        <div>
            <el-dialog
                title="用户列表"
                :close-on-click-modal="false"
                :visible.sync="PushCouponsStatus"
                width="70%"
            >
                <PushCoupons
                    @closePushCouponsStatus="PushCouponsStatus = false"
                    :rowData="rowData"
                    v-if="PushCouponsStatus"
                ></PushCoupons>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import Views from "./view.vue";
import PushCoupons from "./pushCoupons.vue";

export default {
    components: { Add, Views, PushCoupons },

    data() {
        return {
            rowData: {},
            tableData: [],
            objectOptions: [],
            id: "",
            coupon_package_name: "",
            dialogStatus: false,
            viewDialogStatus: false,
            PushCouponsStatus: false,
            pageAttr: {
                page: 1,
                limit: 10
            },
            total: 0,
            isEdit: false
        };
    },
    mounted() {
        this.getCouponPackageList();
    },
    methods: {
        //新人券包列表
        async getCouponPackageList() {
            let res = await this.$request.newZoneCoupons.getCouponPackageList({
                id: this.id,
                coupon_package_name: this.coupon_package_name,
                page: this.pageAttr.page,
                limit: this.pageAttr.limit
            });
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.pageAttr.page = 1;
            this.getCouponPackageList();
        },
        //打开发放弹框
        openPushCoupons(row) {
            this.rowData = row;
            this.PushCouponsStatus = true;
        },
        //更改状态
        async updateEnable(row, status) {
            let data = {
                id: row.id,
                status: status
            };
            this.$request.newZoneCoupons
                .updateCouponPackageStatus(data)
                .then(res => {
                    if (res.data.error_code == 0) {
                        this.getCouponPackageList();
                    }
                });
        },

        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getCouponPackageList();
        },
        view(row) {
            this.viewDialogStatus = true;
            this.rowData = row;
            this.isEdit = true;
        },
        close() {
            this.dialogStatus = false;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getCouponPackageList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getCouponPackageList();
        }
    },

    filters: {}
};
</script>
<style lang="scss" scoped>
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
