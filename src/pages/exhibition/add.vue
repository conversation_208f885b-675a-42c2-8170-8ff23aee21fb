<template>
    <div>
        <el-form :model="editorData" ref="ruleForm" :rules="formRules">
            <el-form-item
                label="展会名称"
                :label-width="formLabelWidth"
                prop="name"
            >
                <el-input
                    v-model="editorData.name"
                    autocomplete="off"
                    placeholder="展会名称"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="展会描述"
                :label-width="formLabelWidth"
                prop="description"
            >
                <el-input
                    v-model="editorData.description"
                    autocomplete="off"
                    placeholder="展会描述"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="展会开始时间"
                :label-width="formLabelWidth"
                prop="start_date"
            >
                <el-date-picker
                    v-model="editorData.start_date"
                    type="datetime"
                    placeholder="选择开始时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="展会结束时间"
                :label-width="formLabelWidth"
                prop="end_date"
            >
                <el-date-picker
                    v-model="editorData.end_date"
                    type="datetime"
                    placeholder="选择结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="顶部图片"
                :label-width="formLabelWidth"
                prop="top_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="top_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="精选产品按钮图片"
                :label-width="formLabelWidth"
                prop="featured_products_button_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="featured_products_button_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="精选产品按钮图片(选中)"
                :label-width="formLabelWidth"
                prop="featured_products_button_h_image_url	"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="featured_products_button_h_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="参展品牌按钮"
                :label-width="formLabelWidth"
                prop="exhibiting_brands_button_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="exhibiting_brands_button_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="参展品牌按钮(选中)"
                :label-width="formLabelWidth"
                prop="exhibiting_brands_button_h_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="exhibiting_brands_button_h_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="精选产品栏目"
                :label-width="formLabelWidth"
                prop="featured_products_section_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="featured_products_section_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="参展品牌栏目"
                :label-width="formLabelWidth"
                prop="exhibiting_brands_section_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="exhibiting_brands_section_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="底部图片"
                :label-width="formLabelWidth"
                prop="bottom_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="bottom_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="中部颜色"
                label-width="150px"
                prop="middle_color_value"
            >
                <el-color-picker
                    v-model="editorData.middle_color_value"
                ></el-color-picker>
            </el-form-item>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeViewDialogStatus">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        return {
            dir: "vinehoo/vos/marketing/",
            formLabelWidth: "180px",
            editorData: {},
            formRules: {},
            // 图片

            featured_products_button_image_url: [],
            featured_products_button_h_image_url: [],
            exhibiting_brands_button_h_image_url: [],
            exhibiting_brands_button_image_url: [],
            featured_products_section_image_url: [],
            exhibiting_brands_section_image_url: [],
            top_image_url: [],
            bottom_image_url: []
        };
    },
    props: {
        editorDataForm: {}
    },
    mounted() {
        if (this.editorDataForm.id) {
            this.editorData = this.editorDataForm;

            if (this.editorDataForm.featured_products_button_image_url.length) {
                this.featured_products_button_image_url = this.editorData.featured_products_button_image_url;
            }
            if (this.editorDataForm.top_image_url.length) {
                this.top_image_url = this.editorData.top_image_url;
            }

            if (
                this.editorDataForm.featured_products_button_h_image_url.length
            ) {
                this.featured_products_button_h_image_url = this.editorData.featured_products_button_h_image_url;
            }
            if (
                this.editorDataForm.exhibiting_brands_button_h_image_url.length
            ) {
                this.exhibiting_brands_button_h_image_url = this.editorData.exhibiting_brands_button_h_image_url;
            }
            if (this.editorDataForm.exhibiting_brands_button_image_url.length) {
                this.exhibiting_brands_button_image_url = this.editorData.exhibiting_brands_button_image_url;
            }
            if (
                this.editorDataForm.featured_products_section_image_url.length
            ) {
                this.featured_products_section_image_url = this.editorData.featured_products_section_image_url;
            }
            if (
                this.editorDataForm.exhibiting_brands_section_image_url.length
            ) {
                this.exhibiting_brands_section_image_url = this.editorData.exhibiting_brands_section_image_url;
            }
            if (this.editorDataForm.bottom_image_url.length) {
                this.bottom_image_url = this.editorData.bottom_image_url;
            }
        }
    },
    methods: {
        closeViewDialogStatus() {
            this.$emit("close");
        },
        submitForm(ruleForm) {
            console.log(this.editorDataForm);
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    let data = {
                        ...this.editorData
                    };

                    data.featured_products_button_image_url = this.featured_products_button_image_url.join(
                        ","
                    );

                    data.top_image_url = this.top_image_url.join(",");
                    data.featured_products_button_h_image_url = this.featured_products_button_h_image_url.join(
                        ","
                    );
                    data.exhibiting_brands_button_h_image_url = this.exhibiting_brands_button_h_image_url.join(
                        ","
                    );
                    data.exhibiting_brands_button_image_url = this.exhibiting_brands_button_image_url.join(
                        ","
                    );
                    data.featured_products_section_image_url = this.featured_products_section_image_url.join(
                        ","
                    );
                    data.exhibiting_brands_section_image_url = this.exhibiting_brands_section_image_url.join(
                        ","
                    );
                    data.bottom_image_url = this.bottom_image_url.join(",");

                    if (!this.editorDataForm.id) {
                        this.$request.apppush
                            .addExhibitionList(data)
                            .then(res => {
                                if (res.data.error_code == 0) {
                                    this.$message.success("操作成功");
                                    this.$emit("close");
                                }
                            });
                    } else {
                        this.$request.apppush
                            .editExhibitionList(data)
                            .then(res => {
                                if (res.data.error_code == 0) {
                                    this.$message.success("操作成功");
                                    this.$emit("close");
                                }
                            });
                    }
                } else {
                    return false;
                }
            });
        }
    }
};
</script>
