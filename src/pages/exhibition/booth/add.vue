<template>
    <div>
        <el-form :model="editorData" ref="ruleForm" :rules="formRules">
            <el-form-item
                label="展台名称"
                :label-width="formLabelWidth"
                prop="name"
            >
                <el-input
                    v-model="editorData.name"
                    autocomplete="off"
                    placeholder="展台名称"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="展台编号"
                :label-width="formLabelWidth"
                prop="number"
            >
                <el-input
                    v-model="editorData.number"
                    autocomplete="off"
                    placeholder="展台编号"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="所属展会"
                :label-width="formLabelWidth"
                prop="exhibition_id"
            >
                <el-select
                    v-model="editorData.exhibition_id"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in exhibitionList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeViewDialogStatus">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        return {
            dir: "vinehoo/vos/marketing/",
            formLabelWidth: "180px",
            editorData: {},
            formRules: {},
            exhibitionList: []
            // 图片
        };
    },
    props: {
        editorDataForm: {}
    },
    mounted() {
        this.getExhibitionList();
        if (this.editorDataForm != {}) {
            this.editorData = this.editorDataForm;
        }
    },
    methods: {
        async getExhibitionList() {
            const res = await this.$request.apppush.getExhibitionList();
            try {
                if (res.data.error_code == 0) {
                    this.exhibitionList = res.data.data;
                }
            } catch (e) {
                console.log(e);
            }
        },
        closeViewDialogStatus() {
            this.$emit("close");
        },
        submitForm(ruleForm) {
            console.log(this.editorDataForm);
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    let data = {
                        ...this.editorData
                    };
                    if (!this.editorDataForm.id) {
                        this.$request.apppush.addBooths(data).then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.$emit("close");
                            }
                        });
                    } else {
                        this.$request.apppush.updateBooths(data).then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.$emit("close");
                            }
                        });
                    }
                } else {
                    return false;
                }
            });
        }
    }
};
</script>
