<template>
    <div>
        <div>
            <el-button
                @click="addconfig"
                size="mini"
                type="success"
                style="margin-bottom: 10px;"
                >添加展台</el-button
            >
        </div>
        <el-table border size="mini" :data="tableData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="100" align="center">
            </el-table-column>
            <el-table-column
                prop="name"
                label="展台名称"
                min-width="140"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="number"
                label="展台编号"
                width="90"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="exhibition_name"
                align="center"
                label="展会名称"
                min-width="200"
            >
            </el-table-column>
            <el-table-column
                prop="updated_at"
                label="更新时间"
                width="180"
                align="center"
            >
            </el-table-column>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="170"
            >
                <template slot-scope="row">
                    <el-button
                        size="mini"
                        type="text"
                        @click="preview(row.row, 'view')"
                        >预览</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="preview(row.row, 'ERcode')"
                        >二维码</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="editorConfig(row.row)"
                        >编辑</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="deleteConfig(row.row)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="展台信息"
                :visible.sync="addDialogStatus"
                width="70%"
                :before-close="close"
            >
                <add
                    v-if="addDialogStatus"
                    :editorDataForm="editorDataForm"
                    @close="close"
                >
                </add>
            </el-dialog>
        </div>
        <el-dialog
            title="小程序二维码"
            :visible.sync="qrCodeDialogStatus"
            width="200px"
            v-if="qrCodeDialogStatus"
        >
            <div id="qrcode" ref="qrcode"></div>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import add from "./add.vue";
import QRCode from "qrcodejs2";
export default {
    components: {
        add,
        VosOss
    },
    data() {
        return {
            qrCodeDialogStatus: false,
            dir: "vinehoo/vos/marketing/",
            formLabelWidth: "150px",
            tableData: [],
            editorDataForm: {},
            addDialogStatus: false
        };
    },

    mounted() {
        this.getBoothsList();
    },
    methods: {
        crateQRcode(url) {
            setTimeout(() => {
                this.qrCodeImage = this.$nextTick(() => {
                    this.qrCodeImage = new QRCode("qrcode", {
                        width: 150, //宽度
                        correctLevel: 3,
                        height: 150, // 高度
                        text: url // 二维码内容
                    });
                });
            }, 50);
            this.qrCodeDialogStatus = true;
        },
        preview(row, type) {
            let data = { booth_id: row.id, id: row.default_brand_id };
            if (!data.booth_id || !data.id) {
                this.$message.error("操作失败，请先绑定展会或展台");
                return;
            } else {
                if (type == "view") {
                    window.open(
                        "https://activity.vinehoo.com/activities-v3/brandExhibition?brand=" +
                            JSON.stringify(data)
                    );
                    console.log(
                        "https://activity.vinehoo.com/activities-v3/brandExhibition?brand=" +
                            JSON.stringify(data)
                    );
                } else {
                    const url =
                        "https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/common-path.html?cloudMiniPath=packageF/pages/web-view/web-view&url=https://activity.vinehoo.com/activities-v3/brandExhibition?brand=" +
                        JSON.stringify(data);
                    this.crateQRcode(url);
                }
            }
        },
        addconfig() {
            this.editorDataForm = {};
            this.addDialogStatus = true;
        },
        async deleteConfig(row) {
            const data = {
                id: row.id
            };
            const res = await this.$request.apppush.deleteBooths(data);
            if (res.data.error_code == 0) {
                this.$message.success("删除成功");
                this.getBoothsList();
            }
        },
        close() {
            this.addDialogStatus = false;
            this.getBoothsList();
        },
        editorConfig(row) {
            this.editorDataForm = row;
            this.addDialogStatus = true;
        },

        async getBoothsList() {
            const res = await this.$request.apppush.getBoothsList();
            try {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    this.tableData = res.data.data;
                }
            } catch (e) {
                console.log(e);
            }
        }
    }
};
</script>
