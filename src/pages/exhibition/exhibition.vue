<template>
    <div>
        <div>
            <el-button
                @click="addconfig"
                size="mini"
                type="success"
                style="margin-bottom: 10px;"
                >添加展会</el-button
            >
        </div>
        <el-table border size="mini" :data="tableData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="100" align="center">
            </el-table-column>
            <el-table-column
                prop="name"
                label="展会名称"
                width="100"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="description"
                label="展会描述"
                min-width="220"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="number"
                align="center"
                label="展会时间"
                width="300"
            >
                <template slot-scope="row">
                    {{ row.row.start_date }} - {{ row.row.end_date }}
                </template>
            </el-table-column>
            <el-table-column
                prop="updated_at"
                label="更新时间"
                width="180"
                align="center"
            >
            </el-table-column>
            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="170"
            >
                <template slot-scope="row">
                    <el-button
                        size="mini"
                        type="text"
                        @click="preview(row.row, 'view')"
                        >预览</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="preview(row.row, 'ERcode')"
                        >二维码</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="editorConfig(row.row)"
                        >编辑</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="deleteConfig(row.row)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="展会信息"
                :visible.sync="addDialogStatus"
                width="70%"
                :before-close="close"
            >
                <add
                    v-if="addDialogStatus"
                    :editorDataForm="editorDataForm"
                    @close="close"
                >
                </add>
            </el-dialog>
        </div>
        <el-dialog
            title="小程序二维码"
            :visible.sync="qrCodeDialogStatus"
            width="200px"
            v-if="qrCodeDialogStatus"
        >
            <div id="qrcode" ref="qrcode"></div>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import QRCode from "qrcodejs2";
import add from "./add.vue";
export default {
    components: {
        add,
        VosOss
    },
    data() {
        return {
            dir: "vinehoo/vos/marketing/",
            formLabelWidth: "150px",
            qrCodeDialogStatus: false,
            tableData: [],
            editorDataForm: {},
            formRules: {
                name: [
                    {
                        required: true,
                        message: "请输入名称",
                        trigger: "blur"
                    }
                ],
                type: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "blur"
                    }
                ],
                number: [
                    {
                        required: true,
                        message: "请输入兔头数量或优惠券ID",
                        trigger: "blur"
                    }
                ],
                probability: [
                    {
                        required: true,
                        message: "请输入中奖几率",
                        trigger: "blur"
                    }
                ],
                sort: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "blur"
                    }
                ]
            },
            addDialogStatus: false
        };
    },

    mounted() {
        this.getExhibitionList();
    },
    methods: {
        crateQRcode(url) {
            setTimeout(() => {
                this.qrCodeImage = this.$nextTick(() => {
                    this.qrCodeImage = new QRCode("qrcode", {
                        width: 150, //宽度
                        correctLevel: 3,
                        height: 150, // 高度
                        text: url // 二维码内容
                    });
                });
            }, 50);
            this.qrCodeDialogStatus = true;
        },
        preview(row, type) {
            const id = row.id;
            if (type == "view") {
                window.open(
                    "https://activity.vinehoo.com/activities-v3/slightlyDrunkSect?id=" +
                        id
                );
            } else {
                const base = encodeURIComponent(
                    "https://activity.vinehoo.com/activities-v3/slightlyDrunkSect?id=" +
                        id
                );
                const url =
                    "https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/common-path.html?cloudMiniPath=packageF/pages/web-view/web-view&url=" +
                    base;
                this.crateQRcode(url);
            }
        },
        addconfig() {
            this.editorDataForm = {};
            this.addDialogStatus = true;
        },
        async deleteConfig(row) {
            const data = {
                id: row.id
            };
            const res = await this.$request.apppush.deleteExhibition(data);
            if (res.data.error_code == 0) {
                this.$message.success("删除成功");
                this.getExhibitionList();
            }
        },
        close() {
            this.addDialogStatus = false;
            this.getExhibitionList();
        },
        editorConfig(row) {
            this.editorDataForm = row;

            this.editorDataForm.featured_products_button_image_url = this.editorDataForm.featured_products_button_image_url.split(
                ","
            );
            this.editorDataForm.featured_products_button_h_image_url = this.editorDataForm.featured_products_button_h_image_url.split(
                ","
            );
            this.editorDataForm.exhibiting_brands_button_h_image_url = this.editorDataForm.exhibiting_brands_button_h_image_url.split(
                ","
            );
            this.editorDataForm.exhibiting_brands_button_image_url = this.editorDataForm.exhibiting_brands_button_image_url.split(
                ","
            );
            this.editorDataForm.featured_products_section_image_url = this.editorDataForm.featured_products_section_image_url.split(
                ","
            );
            this.editorDataForm.exhibiting_brands_section_image_url = this.editorDataForm.exhibiting_brands_section_image_url.split(
                ","
            );
            this.editorDataForm.bottom_image_url = this.editorDataForm.bottom_image_url.split(
                ","
            );
            this.editorDataForm.top_image_url = this.editorDataForm.top_image_url.split(
                ","
            );

            this.addDialogStatus = true;
        },

        async getExhibitionList() {
            const res = await this.$request.apppush.getExhibitionList();
            try {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    this.tableData = res.data.data;
                }
            } catch (e) {
                console.log(e);
            }
        }
    }
};
</script>
