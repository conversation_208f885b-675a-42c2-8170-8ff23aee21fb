<template>
    <div>
        <div>
            <el-button
                @click="addconfig"
                size="mini"
                type="success"
                style="margin-bottom: 10px;"
                >添加品牌</el-button
            >
        </div>
        <el-table border size="mini" :data="tableData" style="width: 100%">
            <el-table-column prop="id" label="ID" width="100" align="center">
            </el-table-column>
            <el-table-column
                prop="name"
                label="品牌名称"
                width="180"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="description"
                label="品牌介绍"
                min-width="180"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="is_own_brand"
                align="center"
                label="是否自有品牌"
                width="80"
            >
                <template slot-scope="row">
                    {{ row.row.is_own_brand ? "是" : "否" }}
                </template>
            </el-table-column>
            <el-table-column
                label="所属展会/展台"
                min-width="140"
                align="center"
            >
                <template slot-scope="row">
                    {{ row.row.exhibition_name }} / {{ row.row.booth_name }}
                </template>
            </el-table-column>
            <el-table-column
                label="排序值"
                width="80"
                prop="sort"
                align="center"
            >
            </el-table-column>

            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="170"
            >
                <template slot-scope="row">
                    <el-button
                        size="mini"
                        type="text"
                        @click="goodsConfig(row.row)"
                        >商品配置</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="editorConfig(row.row)"
                        >编辑</el-button
                    >
                    <el-button
                        size="mini"
                        type="text"
                        @click="deleteConfig(row.row)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-dialog
            title="小程序二维码"
            :visible.sync="qrCodeDialogStatus"
            width="200px"
            v-if="qrCodeDialogStatus"
        >
            <div id="qrcode" ref="qrcode"></div>
        </el-dialog>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="品牌信息"
                :visible.sync="addDialogStatus"
                width="70%"
                :before-close="close"
            >
                <add
                    v-if="addDialogStatus"
                    :editorDataForm="editorDataForm"
                    @close="close"
                >
                </add>
            </el-dialog>
        </div>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="商品信息"
                :visible.sync="goodsDialogStatus"
                width="70%"
                :before-close="close"
            >
                <goods
                    :brandId="brandId"
                    :exhibition_id="exhibition_id"
                    :goodsList="goodsList"
                    :booth_id="booth_id"
                    v-if="goodsDialogStatus"
                    @close="close"
                >
                </goods>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import add from "./add.vue";
import goods from "./goods";
import QRCode from "qrcodejs2";
export default {
    components: {
        add,
        VosOss,
        goods
    },
    data() {
        return {
            dir: "vinehoo/vos/marketing/",
            formLabelWidth: "150px",
            tableData: [],
            editorDataForm: {},
            addDialogStatus: false,
            goodsDialogStatus: false,
            goodsList: [],
            qrCodeDialogStatus: false,
            brandId: "",
            booth_id: "",
            exhibition_id: ""
        };
    },

    mounted() {
        this.getBrandsList();
    },
    methods: {
        crateQRcode(url) {
            this.qrCodeDialogStatus = true;

            setTimeout(() => {
                this.qrCodeImage = this.$nextTick(() => {
                    this.qrCodeImage = new QRCode("qrcode", {
                        width: 150, //宽度
                        correctLevel: 3,
                        height: 150, // 高度
                        text: url // 二维码内容
                    });
                });
            }, 50);
        },
        preview(row, type) {
            let data = { id: row.id, booth_id: row.booth_id };
            if (!data.id || !data.booth_id) {
                this.$message.error("操作失败，请先绑定展会或展台");
                return;
            } else {
                if (type == "view") {
                    window.open(
                        "https://activity.vinehoo.com/activities-v3/brandExhibition?brand=" +
                            JSON.stringify(data)
                    );
                    console.log(
                        "https://activity.vinehoo.com/activities-v3/brandExhibition?brand=" +
                            JSON.stringify(data)
                    );
                } else {
                    const url =
                        "https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/common-path.html?cloudMiniPath=packageF/pages/web-view/web-view&url=https://activity.vinehoo.com/activities-v3/brandExhibition?brand=" +
                        JSON.stringify(data);
                    this.crateQRcode(url);
                }
            }
        },
        addconfig() {
            this.editorDataForm = {};
            this.addDialogStatus = true;
        },
        async deleteConfig(row) {
            const data = {
                id: row.id
            };
            const res = await this.$request.apppush.deleteBrands(data);
            if (res.data.error_code == 0) {
                this.$message.success("删除成功");
                this.getBrandsList();
            }
        },
        close() {
            this.goodsDialogStatus = false;
            this.addDialogStatus = false;
            this.getBrandsList();
        },
        async goodsConfig(row) {
            const res = await this.$request.apppush.getProductsList({
                brand_id: row.id
            });
            try {
                if (res.data.error_code == 0) {
                    this.goodsList = res.data.data;
                    this.brandId = row.id;
                    this.exhibition_id = row.exhibition_id;
                    this.booth_id = row.booth_id;

                    this.goodsDialogStatus = true;
                }
            } catch (e) {
                console.log(e);
            }
        },
        editorConfig(row) {
            this.editorDataForm = row;
            this.editorDataForm.logo_url = this.editorDataForm.logo_url.split(
                ","
            );
            this.editorDataForm.top_image_url = this.editorDataForm.top_image_url.split(
                ","
            );
            this.editorDataForm.brand_bottom_image_url = this.editorDataForm.brand_bottom_image_url.split(
                ","
            );
            this.addDialogStatus = true;
        },

        async getBrandsList() {
            const res = await this.$request.apppush.getBrandsList();
            try {
                if (res.data.error_code == 0) {
                    console.log(res.data);
                    this.tableData = res.data.data;
                }
            } catch (e) {
                console.log(e);
            }
        }
    }
};
</script>
