<template>
    <div>
        <el-form :model="editorData" ref="ruleForm" :rules="formRules">
            <el-form-item
                label="品牌名称"
                :label-width="formLabelWidth"
                prop="name"
            >
                <el-input
                    v-model="editorData.name"
                    autocomplete="off"
                    placeholder="品牌名称"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="品牌介绍"
                :label-width="formLabelWidth"
                prop="description"
            >
                <el-input
                    v-model="editorData.description"
                    autocomplete="off"
                    placeholder="品牌介绍"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="顶部图片"
                :label-width="formLabelWidth"
                prop="top_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="top_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="品牌底部"
                :label-width="formLabelWidth"
                prop="brand_bottom_image_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="brand_bottom_image_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="品牌中部颜色"
                label-width="150px"
                prop="brand_middle_color_value"
            >
                <el-color-picker
                    v-model="editorData.brand_middle_color_value"
                ></el-color-picker>
            </el-form-item>
            <el-form-item
                label="描述文字颜色"
                label-width="150px"
                prop="brand_description_text_color"
            >
                <el-color-picker
                    v-model="editorData.brand_description_text_color"
                ></el-color-picker>
            </el-form-item>
            <el-form-item
                label="Logo图片"
                :label-width="formLabelWidth"
                prop="logo_url"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="logo_url"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="是否自有品牌"
                :label-width="formLabelWidth"
                prop="is_own_brand"
            >
                <el-checkbox v-model="editorData.is_own_brand"
                    >自有品牌</el-checkbox
                >
            </el-form-item>

            <el-form-item
                label="所属展会"
                :label-width="formLabelWidth"
                prop="exhibition_id"
            >
                <el-select
                    @change="exhibitionChange"
                    v-model="editorData.exhibition_id"
                    placeholder="请选择"
                >
                    <el-option
                        v-for="item in exhibitionList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="所属展台"
                :label-width="formLabelWidth"
                prop="booth_id"
            >
                <el-select v-model="editorData.booth_id" placeholder="请选择">
                    <el-option
                        v-for="item in boothList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item
                label="排序值"
                :label-width="formLabelWidth"
                prop="sort"
            >
                <el-input-number
                    v-model="editorData.sort"
                    autocomplete="off"
                    placeholder="排序值"
                ></el-input-number>
            </el-form-item>
            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeViewDialogStatus">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        return {
            dir: "vinehoo/vos/marketing/",
            formLabelWidth: "180px",
            editorData: {},
            formRules: {},
            exhibitionList: [],
            boothList: [],
            // 图片
            logo_url: [],
            brand_bottom_image_url: [],
            top_image_url: []
        };
    },
    props: {
        editorDataForm: {}
    },
    mounted() {
        this.getExhibitionList();
        if (this.editorDataForm.id) {
            this.editorData = this.editorDataForm;
            this.getBoothsList(this.editorDataForm.exhibition_id);
            if (this.editorDataForm.logo_url.length) {
                this.logo_url = this.editorData.logo_url;
            }
            if (this.editorDataForm.top_image_url.length) {
                this.top_image_url = this.editorData.top_image_url;
            }
            if (this.editorDataForm.brand_bottom_image_url.length) {
                this.brand_bottom_image_url = this.editorData.brand_bottom_image_url;
            }
        }
    },
    methods: {
        exhibitionChange(val) {
            this.getBoothsList(val);
        },
        async getBoothsList(exhibition_id) {
            this.boothList = [];
            const res = await this.$request.apppush.getBoothsList({
                exhibition_id
            });
            try {
                if (res.data.error_code == 0) {
                    this.boothList = res.data.data;
                    // this.editorData.booth_id = "";
                }
            } catch (e) {
                console.log(e);
            }
        },
        async getExhibitionList() {
            const res = await this.$request.apppush.getExhibitionList();
            try {
                if (res.data.error_code == 0) {
                    this.exhibitionList = res.data.data;
                }
            } catch (e) {
                console.log(e);
            }
        },
        closeViewDialogStatus() {
            this.$emit("close");
        },
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    let data = {
                        ...this.editorData
                    };
                    data.logo_url = this.logo_url.join(",");
                    data.top_image_url = this.top_image_url.join(",");
                    data.brand_bottom_image_url = this.brand_bottom_image_url.join(
                        ","
                    );
                    if (!this.editorDataForm.id) {
                        this.$request.apppush.addBrands(data).then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.$emit("close");
                            }
                        });
                    } else {
                        this.$request.apppush.updateBrands(data).then(res => {
                            if (res.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.$emit("close");
                            }
                        });
                    }
                } else {
                    return false;
                }
            });
        }
    }
};
</script>
