<template>
    <div>
        <div>
            <el-button
                @click="addconfig"
                size="mini"
                type="success"
                style="margin-bottom: 10px;"
                >添加商品</el-button
            >
        </div>
        <el-table border size="mini" :data="tableData" style="width: 100%">
            <el-table-column
                prop="period"
                label="期数"
                width="100"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="product_title"
                label="商品标题"
                min-width="200"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="price"
                label="商品价格"
                width="90"
                align="center"
            >
            </el-table-column>
            <el-table-column label="排序值" width="130" align="center">
                <template slot-scope="row">
                    <el-input-number
                        style="width: 100px;"
                        v-model="row.row.sort"
                        autocomplete="off"
                        size="mini"
                        @change="updateGoodsSort($event, row)"
                        placeholder="排序值"
                    ></el-input-number>
                </template>
            </el-table-column>
            <el-table-column
                prop="is_featured"
                align="center"
                label="是否精选"
                width="100"
            >
                <template slot-scope="row">
                    <el-checkbox
                        size="mini"
                        :true-label="1"
                        :false-label="0"
                        @change="updateGoodsFeatured($event, row)"
                        v-model="row.row.is_featured"
                        >精选</el-checkbox
                    >
                    <!-- {{ row.row.is_featured ? "是" : "否" }} -->
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="100"
            >
                <template slot-scope="row">
                    <!-- <el-button
                        size="mini"
                        type="text"
                        @click="editorConfig(row.row)"
                        >编辑</el-button
                    > -->
                    <el-button
                        size="mini"
                        type="text"
                        @click="deleteConfig(row.row)"
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-dialog
            :close-on-click-modal="false"
            append-to-body
            title="添加商品"
            :visible.sync="addGoodsDialogStatus"
            width="70%"
        >
            <el-form :model="goodsData" ref="ruleForm">
                <el-form-item
                    label="期数"
                    :label-width="formLabelWidth"
                    prop="name"
                >
                    <el-input
                        size="mini"
                        style="margin-right: 10px;"
                        class="w-mini"
                        v-model="goodsData.period"
                        autocomplete="off"
                        placeholder="品牌名称"
                    ></el-input>
                    <el-button size="mini" type="primary" @click="getGoodsInfo"
                        >查询</el-button
                    >
                </el-form-item>
                <el-form-item
                    label="商品标题"
                    v-if="goodsData.title"
                    :label-width="formLabelWidth"
                >
                    {{ goodsData.title }}
                </el-form-item>
                <el-form-item
                    label="是否精选"
                    v-if="goodsData.title"
                    :label-width="formLabelWidth"
                >
                    <el-checkbox v-model="goodsData.is_featured"
                        >精选产品</el-checkbox
                    >
                </el-form-item>

                <el-form-item
                    label="商品描述"
                    v-if="goodsData.title"
                    :label-width="formLabelWidth"
                >
                    {{ goodsData.brief }}
                </el-form-item>
                <el-form-item
                    label="商品价格"
                    v-if="goodsData.title"
                    :label-width="formLabelWidth"
                >
                    {{ goodsData.price }}
                </el-form-item>
                <el-form-item
                    label="商品图片"
                    v-if="goodsData.title"
                    :label-width="formLabelWidth"
                >
                    <img
                        :src="goodsData.product_img"
                        style="width: 100px; height: 100px;"
                        alt=""
                    />
                </el-form-item>
                <el-form-item
                    label="排序值"
                    v-if="goodsData.title"
                    :label-width="formLabelWidth"
                >
                    <el-input-number
                        v-model="sort"
                        autocomplete="off"
                        placeholder="排序值"
                    ></el-input-number>
                </el-form-item>
                <el-form-item
                    style="margin-top:50px;margin-left:40%"
                    v-if="goodsData.title"
                >
                    <el-button @click="addGoodsDialogStatus = false"
                        >取 消</el-button
                    >
                    <el-button type="primary" @click="submitForm('ruleForm')"
                        >确 定</el-button
                    >
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
export default {
    components: {},
    data() {
        return {
            formLabelWidth: "120px",
            tableData: [],
            goodsData: {
                period: ""
            },
            sort: 0,
            addGoodsDialogStatus: false
        };
    },
    props: {
        goodsList: [],
        brandId: "",
        booth_id: "",
        exhibition_id: ""
    },
    mounted() {
        this.tableData = this.goodsList;
    },
    methods: {
        updateGoodsSort(value, row) {
            const data = {
                ...row.row,
                sort: value
            };
            this.$request.apppush.updateGoods(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$message.success("更新成功");
                    this.goodsConfig(this.brandId);
                }
            });
        },
        updateGoodsFeatured(value, row) {
            const data = {
                ...row.row,
                is_featured: value ? 1 : 0
            };
            this.$request.apppush.updateGoods(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$message.success("更新成功");
                    this.goodsConfig(this.brandId);
                }
            });
            console.log(data);
        },
        submitForm() {
            const data = {
                period: this.goodsData.period,
                product_title: this.goodsData.title,
                description: this.goodsData.brief,
                price: this.goodsData.price,
                market_price: this.goodsData.market_price,
                product_image_url: this.goodsData.product_img,
                is_featured: this.goodsData.is_featured,
                brand_id: this.brandId,
                exhibition_id: this.exhibition_id,
                booth_id: this.booth_id,
                sort: this.sort
            };
            this.$request.apppush.addGoods(data).then(res => {
                if (res.data.error_code == 0) {
                    this.$message.success("添加成功");
                    this.goodsConfig(this.brandId);
                    this.addGoodsDialogStatus = false;
                }
            });
        },
        getGoodsInfo() {
            this.$request.activities
                .getGoodsById({
                    period: this.goodsData.period
                })
                .then(res => {
                    if (res.data.data) {
                        this.goodsData = {
                            ...res.data.data,
                            period: res.data.data.id,
                            is_featured: false
                        };
                    } else {
                        this.goodsData = {};
                        this.$message.error("查询失败，请检查期数是否正确");
                    }
                });
        },
        addconfig() {
            this.addGoodsDialogStatus = true;
        },
        async deleteConfig(row) {
            const data = {
                id: row.id
            };
            const res = await this.$request.apppush.deleteProducts(data);
            if (res.data.error_code == 0) {
                this.$message.success("删除成功");
                this.goodsConfig(this.brandId);
            }
        },
        async goodsConfig(id) {
            const res = await this.$request.apppush.getProductsList({
                brand_id: id
            });
            try {
                if (res.data.error_code == 0) {
                    this.tableData = res.data.data;
                }
            } catch (e) {
                console.log(e);
            }
        },
        close() {
            this.$emit("close");
        }
    }
};
</script>
