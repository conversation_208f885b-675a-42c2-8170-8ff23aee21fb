<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-input
                    v-model="title"
                    placeholder="请输入标题"
                    size="mini"
                    @keyup.enter.native="search"
                    class="m-r-10 w-large"
                ></el-input>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="status"
                    filterable
                    size="mini"
                    placeholder="状态"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in objectOptions"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="pageAttr.channel"
                    filterable
                    size="mini"
                    placeholder="频道"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in channelOptions"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>

                <!-- <el-button @click="reset" size="mini">重置</el-button> -->
                <el-button type="primary" size="mini" @click="search()"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >添加banner广告</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="标题"
                        prop="title"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="图片"
                        prop="icon"
                        min-width="110"
                    >
                        <template #default="imageUrl">
                            <el-image
                                style="width: 100px; height: 100px"
                                :src="imageUrl.row.image"
                                fit="cover"
                            ></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="频道"
                        prop="channel"
                        min-width="150"
                    >
                        <template #default="channel">
                            <span> {{ channel.row.channel.map(item => item.name).join("、") }} </span>

                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="client"
                        align="center"
                        label="客户端"
                        min-width="150"
                    >
                        <template #default="client">
                           
                            <span> {{ client.row.client.map(item => item.name).join("、") }} </span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="client_path.path_name"
                        align="center"
                        label="路径"
                        min-width="150"
                    >
                    </el-table-column>

                    <el-table-column
                        align="center"
                        label="时间"
                        min-width="200"
                        prop=""
                    >
                        <template #default="row">
                            <div>创建 : {{ row.row.created_at }}</div>
                            <div>上架 : {{ row.row.start_time }}</div>
                            <div>下架 : {{ row.row.end_time }}</div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        align="center"
                        label="排序"
                        min-width="70"
                        prop="sort"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="操作人"
                        min-width="100"
                        prop="operator_name"
                    >
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        min-width="180"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                            <!-- <el-button
                                size="mini"
                                v-if="row.row.status == 0"
                                @click="updateEnable(row.row, 1)"
                                type="success"
                                >上架</el-button
                            >

                            <el-button
                                size="mini"
                                type="danger"
                                v-if="row.row.status == 1"
                                @click="updateEnable(row.row, 0)"
                                >下架</el-button
                            > -->
                            <el-popconfirm
                                confirm-button-text="确定"
                                cancel-button-text="取消"
                                title="确定改变状态吗？"
                                @confirm="
                                    updateEnable(
                                        row.row,
                                        row.row.status == 0 ? '1' : '0'
                                    )
                                "
                            >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    v-if="row.row.status == 0"
                                    type="text"
                                    style="margin-left:10px"
                                    >上架</el-button
                                >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    type="text"
                                    v-if="row.row.status == 1"
                                    style="margin-left:10px"
                                    >下架</el-button
                                >
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="新增banner广告"
                :visible.sync="dialogStatus"
                width="40%"
            >
                <div style="height: 570px;overflow-y: auto;">
                    <Add
                        ref="addBanner"
                        v-if="dialogStatus"
                        @close="close"
                        @getAdBannerList="getAdBannerList"
                    ></Add>
                </div>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑banner广告"
                :visible.sync="viewDialogStatus"
                width="40%"
                :before-close="closeViewDialogStatus"
            >
                <div style="height: 570px;overflow-y: auto;">
                    <Views
                        ref="viewBanner"
                        v-if="viewDialogStatus"
                        :rowData="rowData"
                        :isEdit="isEdit"
                        @closeViewDialogStatus="closeViewDialogStatus"
                        @getAdBannerList="getAdBannerList"
                    ></Views>
                </div>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import Views from "./view.vue";
export default {
    components: { Add, Views },

    data() {
        return {
            rowData: {},
            tableData: [],
            objectOptions: [
                {
                    id: 1,
                    name: "上架"
                },
                {
                    id: 0,
                    name: "下架"
                }
            ],
            channelOptions: [
                {
                    id: 0,
                    name: "首页"
                },
                {
                    id: 1,
                    name: "闪购"
                },
                {
                    id: 2,
                    name: "秒发"
                },
                {
                    id: 3,
                    name: "社区"
                },
                {
                    id: 4,
                    name: "兔头"
                },
                {
                    id: 5,
                    name: "个人中心"
                }
            ],
            title: "",
            status: "",
            dialogStatus: false,
            viewDialogStatus: false,
            pageAttr: {
                page: 1,
                limit: 10,
                channel: ""
            },
            total: 0,
            isEdit: false
        };
    },
    mounted() {
        this.getAdBannerList();
    },
    methods: {
        //banner广告列表
        async getAdBannerList() {
            let data = {
                type: 1,
                page: this.pageAttr.page,
                limit: this.pageAttr.limit,
                title: this.title,
                status: this.status,
                channel: this.pageAttr.channel
            };
            let res = await this.$request.adBanner.getAdBannerList(data);
            console.log("adBanner列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.pageAttr.page = 1;
            this.getAdBannerList();
        },
        //更改状态
        async updateEnable(row, status) {
            let data = {
                id: row.id,
                status: status,
                type: 1
            };
            this.$request.adBanner.updateStatus(data).then(res => {
                console.log(res);
                if (res.data.error_code == 0) {
                    this.getAdBannerList();
                }
            });
        },
        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getAdBannerList();
        },
        view(row) {
            this.viewDialogStatus = true;
            let data = {
                id: row.id
            };
            this.$request.clientPath.EditPathDetail(data).then(res => {
                if (res.data.error_code == 0) {
                    console.log("编辑res", res);
                    this.rowData = res.data.data;
                    console.log("88888888", this.rowData);
                }
            });
            this.isEdit = true;
        },
        close() {
            this.dialogStatus = false;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getAdBannerList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getAdBannerList();
        }
    },

    filters: {}
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
