<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item
                label="标题"
                :label-width="formLabelWidth"
                prop="title"
            >
                <el-input
                    size="mini"
                    v-model="form.title"
                    placeholder="请输入标题"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="频道"
                :label-width="formLabelWidth"
                prop="channel"
            >
                <el-checkbox-group v-model="channel">
                    <el-checkbox label="0">首页</el-checkbox>
                    <el-checkbox label="1">闪购</el-checkbox>
                    <el-checkbox label="2">秒发</el-checkbox>
                    <el-checkbox label="3">社区</el-checkbox>
                    <el-checkbox label="4">兔头商店</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item
                label="图片"
                :label-width="formLabelWidth"
                prop="image"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                    :limitWhList="[1080, 314]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>

            <el-form-item
                label="排序"
                :label-width="formLabelWidth"
                prop="sort"
            >
                <el-input-number
                    size="mini"
                    v-model="form.sort"
                    controls-position="right"
                    @change="handleChange"
                    :min="0"
                ></el-input-number>
            </el-form-item>
            <PathConfig
                :path_id="form.path_id"
                :isEdit="isEdit"
                :rowData="rowData"
                ref="pathConfig"
            ></PathConfig>
            <el-form-item
                label="上架时间"
                :label-width="formLabelWidth"
                prop="start_time"
            >
                <el-date-picker
                    v-model="form.start_time"
                    type="datetime"
                    placeholder="请选择上架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="下架时间"
                :label-width="formLabelWidth"
                prop="end_time"
            >
                <el-date-picker
                    v-model="form.end_time"
                    type="datetime"
                    placeholder="请选择下架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="是否启用"
                :label-width="formLabelWidth"
                prop="status"
            >
                <el-radio-group v-model="form.status">
                    <el-radio :label="0">禁用</el-radio>
                    <el-radio :label="1">启用</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import PathConfig from "../../components/pathConfig/pathConfig.vue";
export default {
    components: {
        VosOss,
        PathConfig
    },
    props: ["rowData", "isEdit"],
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的数字"));
            } else {
                callback();
            }
        };
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        return {
            dialogFormVisible: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            client: [],
            channel: [],
            form: {
                channel: [],
                client: [],
                title: "",
                path_id: "",
                image: "",
                sort: 1,
                start_time: "",
                end_time: "",
                status: 1
            },
            pathOptions: [],
            formRules: {
                status: [
                    {
                        required: true,
                        message: "请选择是否启用",
                        trigger: "blur"
                    }
                ],
                start_time: [
                    {
                        required: true,
                        message: "请选择模式",
                        trigger: "blur"
                    }
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择模式",
                        trigger: "blur"
                    }
                ],
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur"
                    }
                ],
                client: [
                    {
                        required: true,
                        message: "请选择客户端",
                        trigger: "blur"
                    }
                ],
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                path_id: [
                    {
                        required: true,
                        message: "请选择路径",
                        trigger: "blur"
                    }
                ],
                image: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ],
                sort: [
                    {
                        required: true,
                        validator: weightValidator,
                        trigger: "blur"
                    }
                ]
            },
            formLabelWidth: "150px",
            pageAttr: {
                page: 1,
                limit: 10
            }
        };
    },
    mounted() {
        setTimeout(() => {
            this.form = this.rowData;
            console.log("编辑form", this.form);
            this.icon_map = this.form.image.split(",");
            this.channel = this.form.channel.map(item => {
                return String(item.id);
            });
            this.form.path_id = this.rowData.path;
        }, 600);
    },
    methods: {
        closeDiog() {
            this.$emit("closeViewDialogStatus");
            this.$emit("getAdCapsule");
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    let {
                        EditClientArr,
                        EditParamArr,
                        pathNum2
                    } = this.$refs.pathConfig.getEditData();
                    this.form.path_id = pathNum2;
                    let data = {
                        title: this.form.title,
                        image: this.icon_map.join(","),
                        path: pathNum2,
                        sort: this.form.sort,
                        type: 7,
                        client: EditClientArr,
                        channel: this.channel,
                        params: EditParamArr,
                        id: this.form.id,
                        start_time: this.form.start_time,
                        end_time: this.form.end_time,
                        status: this.form.status
                    };
                    this.$request.adBanner.editBanner(data).then(res => {
                        if (res.data.error_code == 0) {
                            this.$Message.success("编辑成功");
                            this.$emit("closeViewDialogStatus");
                            this.$emit("getAdCapsule");
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
