<template>
  <div class="mini-program-config">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>小程序跳转配置</span>
      </div>
      
      <el-form :model="form" :rules="rules" ref="form" label-width="180px">
        <el-form-item label="小程序" prop="appType">
          <el-select v-model="form.appType" placeholder="请选择小程序">
            <el-option label="酒云网" value="jiuyunwang"></el-option>
            <el-option label="酒历" value="jiuli"></el-option>
            <el-option label="酒展通" value="jiuzhantong"></el-option>
            <el-option label="Winenotes" value="winenotes"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="小程序appId" prop="appId">
          <el-input v-model="form.appId" placeholder="请输入小程序appId"></el-input>
        </el-form-item>

        <el-form-item label="云开发环境ID" prop="envId">
          <el-input v-model="form.envId" placeholder="请输入云开发环境ID"></el-input>
        </el-form-item>

        <el-form-item label="小程序原始ID" prop="originalId">
          <el-input v-model="form.originalId" placeholder="请输入小程序原始ID"></el-input>
        </el-form-item>

        <el-form-item label="小程序页面路径" prop="pagePath">
          <el-select v-model="form.pagePath" placeholder="请选择页面路径">
            <el-option
              v-for="path in pagePathOptions"
              :key="path.path"
              :label="path.name"
              :value="path.path"
            ></el-option>
          </el-select>
        </el-form-item>

        <template v-if="selectedPathConfig && selectedPathConfig.hasParams">
          <el-form-item 
            v-for="param in selectedPathConfig.params" 
            :key="param"
            :label="param + '参数'" 
            :prop="'pageParams.' + param"
          >
            <el-input 
              v-model="form.pageParams[param]" 
              :placeholder="'请输入' + param + '参数值'"
            ></el-input>
          </el-form-item>
        </template>

        <el-form-item-group>
          <div class="group-title">埋点标识</div>
          <el-form-item label="one_source_platform" prop="one_source_platform">
            <el-input 
              v-model="form.one_source_platform" 
              placeholder="请输入one_source_platform"
            ></el-input>
          </el-form-item>

          <el-form-item label="one_source_event" prop="one_source_event">
            <el-input 
              v-model="form.one_source_event" 
              placeholder="请输入one_source_event"
            ></el-input>
          </el-form-item>
        </el-form-item-group>

        <el-form-item>
          <el-button type="primary" @click="submitForm('form')">生成链接</el-button>
          <el-button @click="resetForm('form')">重置</el-button>
        </el-form-item>
      </el-form>

      <div v-if="generatedLink" class="generated-link-container">
        <div class="link-header">生成的链接：</div>
        <div class="link-content">
          <span class="link-text">{{ generatedLink }}</span>
          <el-button type="primary" size="small" @click="copyToClipboard">复制</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'MiniProgramConfig',
  data() {
    return {
      form: {
        appType: '',
        appId: '',
        envId: '',
        originalId: '',
        pagePath: '',
        pageParams: {},
        one_source_platform: 'vinehoo',
        one_source_event: 'message'
      },
      generatedLink: '',
      appIdMap: {
        jiuyunwang: 'wx3e0b582d1f902659',
        jiuli: 'wxa761ee456d6ded89',
        jiuzhantong: 'wx793844f102a302e4',
        winenotes: 'wx0a09b52b76413eeb'
      },
      originalIdMap: {
        jiuyunwang: 'gh_e64b2bfc0bc5',
        jiuli: 'gh_d37574dadb20',
        jiuzhantong: 'gh_2190ce11f2e7',
        winenotes: 'gh_cb5097958969'
      },
      envIdMap: {
        jiuyunwang: 'cloud1-9gkbx77e27bcf5b3',
        jiuli: 'cloud1-5gatzm758e649c23',
        jiuzhantong: 'cloud1-4g12fwhga2465036',
        winenotes: 'prod-2g5b801oc48c9ceb'
      },
      hostMap: {
        jiuyunwang: 'https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/vinehoo.html',
        jiuli: 'https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/vinehoo.html',
        jiuzhantong: 'https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/vinehoo.html',
        winenotes: 'https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/vinehoo.html'
      },
      titleMap: {
        jiuyunwang: '酒云网',
        jiuli: '酒历',
        jiuzhantong: '酒展通',
        winenotes: 'Winenotes'
      },
      pagePathConfigs: {
        jiuyunwang: [
          { 
            name: '首页', 
            path: '/pages/index/index',
            hasParams: false
          },
          { 
            name: '优惠券列表', 
            path: '/packageE/pages/coupon-list/coupon-list',
            hasParams: false
          },
          { 
            name: '商品详情页', 
            path: '/pages/goods-detail/goods-detail',
            hasParams: true,
            params: ['id']
          }
        ],
        jiuli: [],
        jiuzhantong: [],
        winenotes: []
      },
      rules: {
        appType: [
          { required: true, message: '请选择小程序', trigger: 'change' }
        ],
        appId: [
          { required: true, message: '请输入小程序appId', trigger: 'blur' }
        ],
        envId: [
          { required: true, message: '请输入云开发环境ID', trigger: 'blur' }
        ],
        originalId: [
          { required: true, message: '请输入小程序原始ID', trigger: 'blur' }
        ],
        pagePath: [
          { required: true, message: '请选择小程序页面路径', trigger: 'change' }
        ],
        'pageParams.id': [
          { required: true, message: '请输入id参数', trigger: 'blur' }
        ],
        'pageParams.uid': [
          { required: true, message: '请输入uid参数', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    pagePathOptions() {
      return this.pagePathConfigs[this.form.appType] || []
    },
    selectedPathConfig() {
      if (!this.form.appType || !this.form.pagePath) return null
      return this.pagePathConfigs[this.form.appType].find(config => config.path === this.form.pagePath)
    }
  },
  watch: {
    'form.appType'(newVal) {
      // Reset pagePath and pageParams when appType changes
      this.form.pagePath = ''
      this.form.pageParams = {}
      // Set appId, originalId and envId based on selected appType
      this.form.appId = this.appIdMap[newVal] || ''
      this.form.originalId = this.originalIdMap[newVal] || ''
      this.form.envId = this.envIdMap[newVal] || ''
    },
    'form.pagePath'() {
      // Reset pageParams when pagePath changes
      this.form.pageParams = {}
      // Reset validation for pageParams
      if (this.$refs.form) {
        this.$refs.form.clearValidate(['pageParams.id', 'pageParams.uid'])
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const host = this.hostMap[this.form.appType]
          const pageParams = this.selectedPathConfig?.hasParams 
            ? '?' + Object.entries(this.form.pageParams)
                .map(([key, value]) => `${key}=${value}`)
                .join('&')
            : ''
          
          // Combine path, params, and tracking params
          const trackingParams = `${pageParams ? '&' : '?'}one_source_platform=${this.form.one_source_platform}&one_source_event=${this.form.one_source_event}`
          const fullPath = this.form.pagePath + pageParams + trackingParams
          const encodedPath = btoa(fullPath)
          
          // Create URLSearchParams without the path parameter
          const baseParams = new URLSearchParams({
            appId: this.form.appId,
            env: this.form.envId,
            username: this.form.originalId,
            title: this.titleMap[this.form.appType]
          }).toString()
          
          // Manually append the encoded path parameter
          this.generatedLink = `${host}?${baseParams}&path=${encodedPath}`
          this.copyToClipboard()
        } else {
          return false
        }
      })
    },
    copyToClipboard() {
      navigator.clipboard.writeText(this.generatedLink).then(() => {
        this.$message.success('链接已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败，请手动复制')
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped>
.mini-program-config {
  padding: 20px;
}
.box-card {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}
:deep(.el-form-item__label) {
  text-align: left;
}
.generated-link-container {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}
.link-header {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: bold;
}
.link-content {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}
.link-text {
  color: #409EFF;
  word-break: break-all;
  font-family: monospace;
  flex: 1;
}
.group-title {
  font-size: 14px;
  color: #606266;
  padding: 0 0 10px 10px;
  font-weight: bold;
}
</style> 