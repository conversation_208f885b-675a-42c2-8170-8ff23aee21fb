<template>
    <div>
        <el-card shadow="hover">
            <el-form :inline="true" size="mini">
                <el-form-item label="">
                    <el-select
                        v-model="query.show"
                        placeholder="请选择状态"
                        clearable
                        @change="search"
                    >
                        <el-option label="下架" value="0"> </el-option>
                        <el-option label="上架" value="1"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button type="success" @click="add">新增</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card shadow="hover" style="margin-top: 10px;">
            <el-table border size="mini" :data="tableData" style="width: 100%">
                <el-table-column
                    align="center"
                    label="图片"
                    prop="icon"
                    min-width="70"
                >
                    <template slot-scope="{ row }">
                        <el-image
                            style="width: 100px; height: 100px"
                            :src="row.params.icon"
                            fit="cover"
                        ></el-image>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                        align="center"
                        label="图片地址"
                        prop="icon"
                        min-width="100"
                    >
                    </el-table-column> -->
                <el-table-column
                    align="center"
                    label="类型"
                    prop="path"
                    min-width="100"
                >
                    <template #default="path">
                        <span>{{
                            `${path.row.path == "url" ? "h5外链" : ""}
                                ${path.row.path == "xcx" ? "小程序" : ""}
                                 ${path.row.path == "xcxzb" ? "小程序直播" : ""}
                                ${path.row.path == "wxkf" ? "微信客服" : ""}`
                        }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column
                        align="center"
                        label="链接"
                        prop="url"
                        min-width="120"
                    >
                    </el-table-column> -->
                <el-table-column
                    align="center"
                    label="是否新人"
                    prop="external_action"
                    min-width="100"
                >
                    <template #default="external_action">
                        <span>{{
                            `${
                                external_action.row.external_action == "1"
                                    ? "是"
                                    : ""
                            }
                                ${
                                    external_action.row.external_action == "0"
                                        ? "否"
                                        : ""
                                }`
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    label="状态"
                    prop="show"
                    min-width="100"
                >
                    <template #default="show">
                        <span style="color:#67C23A" v-if="show.row.show === '1'"
                            >上架</span
                        >
                        <span style="color:#F56C6C" v-else>下架</span>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="room"
                    align="center"
                    label="直播间ID"
                    prop="room_id"
                    min-width="100"
                >
                </el-table-column>
                <!-- <el-table-column
                        v-if="wxminiUrl"
                        align="center"
                        label="小程序路径"
                        prop="page"
                        min-width="120"
                    >
                    </el-table-column> -->

                <el-table-column
                    prop="address"
                    label="操作"
                    fixed="right"
                    min-width="80"
                    align="center"
                >
                    <template slot-scope="row">
                        <el-button
                            type="text"
                            size="mini"
                            @click="changeShow(row.row)"
                        >
                            {{ row.row.show === "1" ? "下架" : "上架" }}
                        </el-button>
                        <el-button
                            @click="view(row.row)"
                            type="text"
                            size="mini"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="基本信息"
                :visible.sync="viewDialogStatus"
                width="40%"
                :before-close="closeViewDialogStatus"
            >
                <div style="height: 570px;overflow-y: auto;">
                    <Views
                        ref="viewSuspendBtn"
                        v-if="viewDialogStatus"
                        :rowData="rowData"
                        @closeViewDialogStatus="closeViewDialogStatus"
                        @getAdSuspendBtnList="getAdSuspendBtnList"
                    ></Views>
                </div>
            </el-dialog>
        </div>
        <div style="display: flex; justify-content: center;margin-top: 10px;">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="query.page"
                :page-size="query.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Views from "./view.vue";
export default {
    components: { Views },

    data() {
        return {
            rowData: {},
            tableData: [],
            title: "",
            status: "",
            dialogStatus: false,
            viewDialogStatus: false,
            wxminiUrl: false,
            room: false,
            total: 0,
            isEdit: false,
            query: {
                show: "",
                page: 1,
                limit: 10
            }
        };
    },
    mounted() {
        this.getAdSuspendBtnList();
    },
    methods: {
        async getAdSuspendBtnList() {
            let res = await this.$request.adSuspendBtn.getAdSuspendBtnList(
                this.query
            );
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        search() {
            this.query.page = 1;
            this.getAdSuspendBtnList();
        },
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getAdSuspendBtnList();
        },
        view(row) {
            this.viewDialogStatus = true;
            this.$nextTick(() => {
                this.$refs.viewSuspendBtn.getEditData(row);
            });
        },
        close() {
            this.dialogStatus = false;
        },
        add() {
            this.viewDialogStatus = true;
        },
        changeShow(row) {
            let data = JSON.parse(JSON.stringify(row));
            data.show = data.show === "1" ? "0" : "1";
            data.params.icon = data.params.icon.split(".com")[1];
            this.$request.adSuspendBtn.editAdSuspendBtn(data).then(res => {
                if (res.data.error_code == 0) {
                    this.getAdSuspendBtnList();
                }
            });
        },

        handleSizeChange(val) {
            this.query.page = 1;
            this.query.limit = val;
            this.getAdSuspendBtnList();
        },
        handleCurrentChange(val) {
            this.query.page = val;
            this.getAdSuspendBtnList();
        }
    },

    filters: {}
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
