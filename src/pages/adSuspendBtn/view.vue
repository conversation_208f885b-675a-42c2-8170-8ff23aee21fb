<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item
                label="图片"
                :label-width="formLabelWidth"
                prop="icon"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                    :limitWhList="[300, 300]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item
                label="图片宽度"
                :label-width="formLabelWidth"
                prop="w"
            >
                <el-input
                    size="mini"
                    v-model="form.w"
                    placeholder="请输入图片宽度"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="图片高度"
                :label-width="formLabelWidth"
                prop="h"
            >
                <el-input
                    size="mini"
                    v-model="form.h"
                    placeholder="请输入图片高度"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="跳转类型"
                :label-width="formLabelWidth"
                prop="path"
            >
                <el-radio v-model="form.path" label="1">h5外链</el-radio>
                <el-radio v-model="form.path" label="2">小程序</el-radio>
                <el-radio v-model="form.path" label="3">小程序直播</el-radio>
                <el-radio v-model="form.path" label="4">微信客服</el-radio>
            </el-form-item>
            <!-- h5外链 -->
            <el-form-item
                v-if="form.path == 1"
                label="h5外链地址"
                :label-width="formLabelWidth"
                prop="url"
            >
                <el-input
                    size="mini"
                    v-model="form.url"
                    placeholder="请输入h5外链地址"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <!-- 小程序 -->
            <div v-if="form.path == 2">
                <el-form-item
                    label="小程序原始ID"
                    :label-width="formLabelWidth"
                    prop="id"
                >
                    <el-input
                        size="mini"
                        v-model="form.id"
                        placeholder="请输入小程序原始ID"
                        style="width:400px"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="小程序路径"
                    :label-width="formLabelWidth"
                    prop="page"
                >
                    <el-input
                        size="mini"
                        v-model="form.page"
                        placeholder="请输入小程序路径"
                        style="width:400px"
                    ></el-input>
                </el-form-item>
            </div>
            <!-- 小程序直播 -->
            <div v-if="form.path == 3">
                <el-form-item
                    label="小程序原始ID"
                    :label-width="formLabelWidth"
                    prop="id"
                >
                    <el-input
                        size="mini"
                        v-model="form.id"
                        placeholder="请输入小程序原始ID"
                        style="width:400px"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="直播间ID"
                    :label-width="formLabelWidth"
                    prop="room_id"
                >
                    <el-input
                        size="mini"
                        v-model="form.room_id"
                        placeholder="请输入直播间ID"
                        style="width:400px"
                    ></el-input>
                </el-form-item>
            </div>

            <!-- 微信客服 -->
            <el-form-item
                v-if="form.path == 4"
                label="微信客服"
                :label-width="formLabelWidth"
                prop="url"
            >
                <el-input
                    size="mini"
                    v-model="form.url"
                    placeholder="请输入微信客服"
                    style="width:400px"
                ></el-input>
            </el-form-item>

            <el-form-item
                label="是否上架"
                :label-width="formLabelWidth"
                prop="show"
            >
                <el-radio v-model="form.show" label="1">上架</el-radio>
                <el-radio v-model="form.show" label="0">下架</el-radio>
            </el-form-item>
            <el-form-item
                label="是否验证新用户"
                :label-width="formLabelWidth"
                prop="external_action"
            >
                <el-radio v-model="form.external_action" label="1">是</el-radio>
                <el-radio v-model="form.external_action" label="0">否</el-radio>
            </el-form-item>

            <el-form-item
                label="上架时间"
                :label-width="formLabelWidth"
                prop="start_time"
            >
                <el-date-picker
                    v-model="form.start_time"
                    type="datetime"
                    placeholder="请选择上架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item
                label="下架时间"
                :label-width="formLabelWidth"
                prop="end_time"
            >
                <el-date-picker
                    v-model="form.end_time"
                    type="datetime"
                    placeholder="请选择下架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    props: ["rowData"],
    data() {
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
        };
        return {
            dialogFormVisible: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            form: {
                icon: "",
                w: "",
                h: "",
                id: "gh_e64b2bfc0bc5",
                page: "",
                url: "",
                path: "1",
                external_action: "",
                room_id: "",
                show: "1",
                parent_id: "",
                start_time: "",
                end_time: ""
            },
            formRules: {
                url: [
                    {
                        required: true,
                        message: "请输入h5外链地址",
                        trigger: "blur"
                    }
                ],
                page: [
                    {
                        required: true,
                        message: "请输入小程序路径",
                        trigger: "blur"
                    }
                ],
                id: [
                    {
                        required: true,
                        message: "请输入小程序原始ID",
                        trigger: "blur"
                    }
                ],
                room_id: [
                    {
                        required: true,
                        message: "请输入直播间ID",
                        trigger: "blur"
                    }
                ],
                show: [
                    {
                        required: true,
                        message: "请选择是否上架",
                        trigger: "blur"
                    }
                ],
                w: [
                    {
                        required: true,
                        message: "请输入图片宽度",
                        trigger: "blur"
                    }
                ],
                h: [
                    {
                        required: true,
                        message: "请输入图片高度",
                        trigger: "blur"
                    }
                ],
                path: [
                    {
                        required: true,
                        message: "请选择转跳类型",
                        trigger: "blur"
                    }
                ],
                icon: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ],
                external_action: [
                    {
                        required: true,
                        message: "请选择是否验证新用户",
                        trigger: "blur"
                    }
                ],
                start_time: [
                    {
                        required: true,
                        message: "请选择上架时间",
                        trigger: "blur"
                    }
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择下架时间",
                        trigger: "blur"
                    }
                ]
            },
            formLabelWidth: "150px",
            is_edit: false
        };
    },
    watch: {
        "form.path": {
            handler(newVal, oldVal) {
                if (newVal == "1" || newVal == "4") {
                    this.form.page = "";
                    this.form.room_id = "";
                } else if (newVal == "2") {
                    this.form.url = "";
                    this.form.room_id = "";
                } else if (newVal == "3") {
                    this.form.url = "";
                    this.form.page = "";
                }
            }
        }
    },
    mounted() {},
    methods: {
        getEditData(row) {
            console.log("getEditData");
            const { path, show, room_id, external_action } = row;
            const { page, icon, id, url, w, h } = row.params;
            this.is_edit = true;
            this.form.show = show;
            this.form.w = w;
            this.form.h = h;
            if (path == "url") {
                this.form.path = "1";
            } else if (path == "xcx") {
                this.form.path = "2";
            } else if (path == "xcxzb") {
                this.form.path = "3";
            } else if (path == "wxkf") {
                this.form.path = "4";
            }
            this.form.external_action = external_action;
            this.icon_map = icon.split(",");
            this.form.id = id;
            this.form.room_id = room_id;
            this.form.page = page;
            this.form.url = url;
            this.form.parent_id = row.id;
            this.form.start_time = row.start_time;
            this.form.end_time = row.end_time;
        },
        closeDiog() {
            this.$emit("closeViewDialogStatus");
        },
        //表单提交，在父组件调用
        submitForm() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    let pathTag = "";
                    if (this.form.path == 1) {
                        pathTag = "url";
                    } else if (this.form.path == 2) {
                        pathTag = "xcx";
                    } else if (this.form.path == 3) {
                        pathTag = "xcxzb";
                    } else if (this.form.path == 4) {
                        pathTag = "wxkf";
                    }
                    let imgUrl = "";
                    if (this.icon_map) {
                        imgUrl = this.icon_map.join(",");
                    }
                    let data = {
                        params: {
                            icon: imgUrl,
                            w: this.form.w,
                            h: this.form.h,
                            id: this.form.id,
                            page: this.form.page,
                            url: this.form.url
                        },
                        room_id: this.form.room_id,
                        path: pathTag,
                        show: this.form.show,
                        external_action: this.form.external_action,
                        id: this.form.parent_id,
                        start_time: this.form.start_time,
                        end_time: this.form.end_time
                    };
                    let method = this.is_edit
                        ? "editAdSuspendBtn"
                        : "addAdSuspendBtn";
                    this.$request.adSuspendBtn[method](data).then(res => {
                        console.log("返回的值", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("编辑成功");
                            this.$emit("closeViewDialogStatus");
                        }
                    });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
