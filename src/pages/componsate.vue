<template>
    <div>
        <el-row>
            <el-col :span="8">
                <el-card>
                    <el-row slot="header" type="flex">
                        <h4 style="margin-bottom: 0;">延期补偿设置</h4>
                    </el-row>
                    <el-form
                        v-if="isEdit"
                        ref="formRef"
                        label-width="120px"
                        :model="model"
                    >
                        <el-form-item label="延期时长">
                            <el-radio-group v-model="model.delay_time">
                                <el-radio :label="24" style="margin-bottom: 0;"
                                    >24小时</el-radio
                                >
                                <el-radio :label="48" style="margin-bottom: 0;"
                                    >48小时</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <div
                            v-for="(item, index) in model.rules"
                            :key="item.id"
                        >
                            <el-form-item
                                label="延期订单金额"
                                :prop="`rules[${index}].money`"
                                :rules="formRules.money"
                            >
                                <el-row type="flex" align="middle">
                                    <el-input v-model="item.money"></el-input>
                                    <span
                                        style="flex-shrink: 0; margin: 0 10px;"
                                        >元以下</span
                                    >
                                    <el-button
                                        type="primary"
                                        size="mini"
                                        @click="onAddRule(index)"
                                        >添加</el-button
                                    >
                                    <el-button
                                        v-if="model.rules.length > 1"
                                        type="danger"
                                        size="mini"
                                        @click="onDeleteRule(index)"
                                        >删除</el-button
                                    >
                                </el-row>
                            </el-form-item>
                            <el-form-item
                                label="发放优惠券"
                                :prop="`rules[${index}].coupon_id`"
                                :rules="formRules.coupon_id"
                            >
                                <el-select v-model="item.coupon_id">
                                    <el-option
                                        v-for="couponItem in couponList"
                                        :key="couponItem.id"
                                        :label="couponItem.coupon_name"
                                        :value="couponItem.id"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <el-row type="flex" justify="end">
                            <el-button size="small" @click="isEdit = false">
                                取消
                            </el-button>
                            <el-button
                                type="primary"
                                size="small"
                                @click="onSave"
                                >保存</el-button
                            >
                        </el-row>
                    </el-form>
                    <el-form v-else label-width="120px" class="show-form">
                        <el-form-item label="延期时长"
                            >{{ componsateInfo.delay_time }}小时</el-form-item
                        >
                        <el-form-item label="延期订单金额">{{
                            componsateInfo.money
                        }}</el-form-item>
                        <el-form-item label="发放优惠券">{{
                            componsateInfo.coupon_name
                        }}</el-form-item>
                        <el-row type="flex" justify="end">
                            <el-button
                                type="warning"
                                size="small"
                                @click="onEdit"
                                >编辑</el-button
                            >
                        </el-row>
                    </el-form>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import componsateApi from "@/services/compensate";
export default {
    data: () => ({
        componsateInfo: {},
        isEdit: false,
        model: {
            id: "",
            delay_time: 24,
            rules: [{ money: "", coupon_id: "" }]
        },
        couponList: [],
        formRules: {
            money: [
                { required: true, message: "请输入金额", trigger: "blur" },
                {
                    pattern: /^[1-9]\d*(\.\d{1,2})?$|^0\.([0-9]\d?|0[0-9])$|^0$/,
                    message: "请输入正确的金额",
                    trigger: "blur"
                }
            ],
            coupon_id: [
                { required: true, message: "请选择优惠券", trigger: "change" }
            ]
        }
    }),
    methods: {
        getComponsateDetail() {
            componsateApi.getCompensateInfo().then(res => {
                if (res.data.error_code === 0) {
                    this.componsateInfo = res.data.data;
                }
            });
        },
        getCompensateCouponList() {
            componsateApi.getCompensateCouponList().then(res => {
                if (res.data.error_code === 0) {
                    this.couponList = res.data.data;
                }
            });
        },
        onEdit() {
            this.getCompensateCouponList();
            const { id, delay_time, rules } = this.componsateInfo;
            this.model = {
                id,
                delay_time,
                rules: rules.map(({ money, coupon_id }, index) => ({
                    id: Date.now() + index,
                    money,
                    coupon_id
                }))
            };
            this.isEdit = true;
        },
        onAddRule(index) {
            this.model.rules.splice(index + 1, 0, {
                id: Date.now(),
                money: "",
                coupon_id: ""
            });
        },
        onDeleteRule(index) {
            this.model.rules.splice(index, 1);
        },
        onSave() {
            this.$refs.formRef.validate(valid => {
                if (!valid) return;
                const { id, delay_time, rules } = this.model;
                const data = {
                    id,
                    delay_time,
                    rules: rules.map(({ money, coupon_id }) => ({
                        money,
                        coupon_id
                    }))
                };
                componsateApi.updateCompensateInfo(data).then(res => {
                    if (res.data.error_code === 0) {
                        this.$message.success("操作成功");
                        this.getComponsateDetail();
                        this.isEdit = false;
                    }
                });
            });
        }
    },
    created() {
        this.getComponsateDetail();
    }
};
</script>

<style lang="scss" scoped>
.show-form {
    .el-form-item {
        margin-bottom: 0;
    }
}
</style>
