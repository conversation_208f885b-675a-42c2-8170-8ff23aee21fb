<template>
    <div>
        <div class="king_container">
            <el-form
                style="width:55%"
                :model="form"
                :rules="formRules"
                ref="ruleForm"
            >
                <el-form-item
                    label="频道"
                    :label-width="formLabelWidth"
                    prop="channel"
                >
                    <el-radio-group
                        :value="form.channel"
                        @input="changeChannel"
                    >
                        <el-radio label="1">闪购</el-radio>
                         <el-radio  label="2">秒发</el-radio>
                        <el-radio label="8">跨境</el-radio>
                        <el-radio label="9">尾货</el-radio>
                        <el-radio label="20">推荐</el-radio>
                        <el-radio label="0">首页</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item
                    label="栏目名称"
                    :label-width="formLabelWidth"
                    prop="name"
                >
                    <el-input
                        size="mini"
                        v-model="form.name"
                        autocomplete="off"
                        placeholder="请输入栏目名称"
                    ></el-input>
                </el-form-item>
                <!-- <el-form-item
                    label="区域"
                    prop="page_area"
                    :label-width="formLabelWidth"
                >
                    <el-select
                        v-model="form.page_area"
                        placeholder="请选择区域"
                        size="mini"
                    >
                        <el-option
                            v-for="item in pageAreaOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item> -->

                <el-form-item
                    label="图标"
                    :label-width="formLabelWidth"
                    prop="icon"
                >
                    <vos-oss
                        v-if="form.channel == 0"
                        key="1"
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                        :limitWhList="[750, 750]"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                    <vos-oss
                        v-else
                        key="2"
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                        :limitWhList="[84, 84]"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
                <el-form-item
                    label="顶部图片"
                    :label-width="formLabelWidth"
                    prop="top_image"
                    v-if="form.page_mode==0"
                >
                    <vos-oss
                        key="3"
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="topImageArr"
                       
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                   
                </el-form-item>
                <el-form-item
                    label="背景颜色"
                     v-if="form.page_mode==0"
                    :label-width="formLabelWidth"
                    prop="background_color"
                >
                    <el-color-picker
                        v-model="form.background_color"
                        @change="colorChange"
                    ></el-color-picker>
                </el-form-item>
                <!-- <el-form-item
                    label="徽章"
                    :label-width="formLabelWidth"
                    prop="badge"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="badge_map"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item> -->
                <el-form-item label="展示模式" prop="page_mode"  :label-width="formLabelWidth">
                <el-radio  @input="changePageMode" v-model="form.page_mode" :label="0">浏览</el-radio>
                <el-radio  @input="changePageMode" v-model="form.page_mode" :label="1">筛选</el-radio>
                </el-form-item>
                <el-form-item
                    label="排序"
                    :label-width="formLabelWidth"
                    prop="sort"
                >
                    <el-input-number
                        size="mini"
                        v-model="form.sort"
                        controls-position="right"
                        @change="handleChange"
                        :min="0"
                    ></el-input-number>
                </el-form-item>
                <el-form-item
                label="商品排序"
                :label-width="formLabelWidth"
                prop="goods_sort_type"
            >
                <!-- <el-input v-model="form.name" autocomplete="off"></el-input> -->
                <el-radio v-model="form.goods_sort_type" :label="0">添加时间倒序</el-radio>
                <el-radio v-model="form.goods_sort_type" :label="1">上架时间倒序</el-radio>
                <el-radio v-model="form.goods_sort_type" :label="2">闪购排序值</el-radio>
                <el-radio v-model="form.goods_sort_type" :label="3">随机</el-radio>
            </el-form-item>
            <el-form-item
                label="客户端"
                :label-width="formLabelWidth"
                prop="client"
            >
                <el-checkbox-group v-model="form.client">
                    <el-checkbox
                        label="0"
                        >ios</el-checkbox
                    >

                    <el-checkbox
                        label="1"
                       
                        >安卓</el-checkbox
                    >

                    <el-checkbox
                        label="2"
                       
                        >小程序</el-checkbox
                    >

                    <el-checkbox
                        label="3"
                      
                        >h5</el-checkbox
                    >

                    <el-checkbox
                        label="4"
                        >PC</el-checkbox
                    >
                </el-checkbox-group>
            </el-form-item>
            <el-form-item
                    label="子项"
                    :label-width="formLabelWidth"
                    prop="filter"
                    v-if="form.page_mode == 1"
                >
                <div style="min-height: 40px; display: flex; align-items: center; "> 
                    <div style="min-width: 180px; min-height: 28px; padding: 0 15px; border: 1px solid #DCDFE6; display: inline-block">{{ filterStr }}</div>
                    <el-button 
                    type="primary" size="mini" @click="filterSet()" style="margin-left: 20px;">
                    管理子项
                    </el-button >
                </div>
                </el-form-item>
            <AddGoodsConfig
                   v-if="form.page_mode == 0"
                    ref="AddGoodsConfig"
                    
                ></AddGoodsConfig>
                <el-form-item> </el-form-item>
            </el-form>
            <div style="width:300px;">
                <img
                    v-if="form.channel == '0'"
                    style="width:100%"
                    :src="oss_url + '/vinehoo/vos/marketing/kingkong_index.png'"
                />
                <img
                    v-if="form.channel == '5'"
                    style="width:100%"
                    :src="
                        oss_url + '/vinehoo/vos/marketing/kingkong_presonal.png'
                    "
                />
                <img
                    v-if="form.channel == '1' || form.channel == '7' || form.channel == '8' || form.channel == '9'"
                    style="width:100%"
                    src="https://images.vinehoo.com/vinehoo/vos/marketing/kingkong_cross.png"
                />
                <img
                    v-if="form.channel == '2'"
                    style="width:100%"
                    src="https://images.vinehoo.com/vinehoo/vos/marketing/miaofaColumn.png"
                />
            </div>
        </div>
        <div style="margin-top:50px;margin-left:40%">
            <el-button @click="closeDiog">取 消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm')"
                >确 定</el-button
            >
        </div>
        <el-dialog
                :close-on-click-modal="false"
                title="子项管理"
                :visible.sync="filterViewDialogStatus"
                width="60%"
                append-to-body
            >
            <!-- :before-close="closeViewDialogStatus" -->
            <el-button
                type="primary"
                size="defualt"
                @click="addType"
                style="margin-bottom: 10px;"
                >添加</el-button
            >
           
                <div class="table" v-if="filterList.length">
                <el-card class="card" shadow="hover">
                    <!-- 添加标题头 -->
                    <div class="table-header" style="display: flex; align-items: center; padding: 12px 8px; background: #f5f7fa; border-bottom: 1px solid #EBEEF5;">
                        <span style="flex: 1; padding-left: 20px; font-weight: bold;">子项名称（拖拽可进行排序）</span>
                        <div style="width: 170px; text-align: center; font-weight: bold;">操作</div>
                    </div>
                    
                    <draggable 
                        v-model="filterList"
                        :animation="200"
                        handle=".el-table__row"
                        @start="dragStart"
                        @end="dragEnd"
                    >
                        <transition-group>
                            <div v-for="item in filterList" 
                                :key="item.sort"
                                class="el-table__row"
                                style="display: flex; align-items: center; padding: 8px; border-bottom: 1px solid #EBEEF5;"
                            >
                                <span style="flex: 1; padding-left: 20px;">{{item.name}} </span>
                                <span style="font-size: 10px;"> {{ item.add_method == 1 ? '自动':'手动' }}</span>
                                <div style="width: 170px; text-align: center;">
                                    <el-button
                                        @click="view(item)"
                                        type="text"
                                        size="mini"
                                    >编辑</el-button>
                                    <el-button
                                        @click="deleteData(item)"
                                        type="text"
                                        size="mini"
                                        style="margin-left:10px"
                                    >删除</el-button>
                                </div>
                            </div>
                        </transition-group>
                    </draggable>
                </el-card>
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="filterViewDialogStatus = false"
                    >取 消</el-button
                >
                <el-button type="primary" @click="filterSure"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <el-dialog
                :close-on-click-modal="false"
                title="添加子项"
                :visible.sync="addChildrenVisiable"
                width="60%"
                append-to-body
            >
            <addChild 
                v-if="addChildrenVisiable" 
                ref="addChild"
                @submit="handleChildSubmit"
                @cancel="addChildrenVisiable = false"
            ></addChild>
        </el-dialog>
    </div>
    
</template>

<script>
import VosOss from "vos-oss";
import AddGoodsConfig from "../../components/addGoodsCon/addGoodsCon.vue";
import draggable from "vuedraggable";
import addChild from "./addChild.vue";
// import { from } from "core-js/core/array";
export default {
    components: {
        VosOss,
        AddGoodsConfig,
        draggable,
        addChild
    },
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的数字"));
            } else {
                callback();
            }
        };
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
        };
        return {
            dialogFormVisible: false,
            icon_map: [],
            badge_map: [],
            topImageArr:[],
            dir: "vinehoo/vos/marketing/",
            form: {
                channel: "1",
                client: ["0", "1", "2", "3",'4'],
                name: "",
                path_id: " ",
                icon: "",
                badge: "",
                sort: 1,
                param: [],
                goods_sort_type: 0,
                add_metshod: 0,
                filter:[],
                page_mode:0,
                top_image:'',
                background_color:'',
            },
            addChildrenVisiable:false,
            filterViewDialogStatus:false,
            filterList:[],
            filterStr:'',
            formRules: {
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur"
                    }
                ],
                client: [
                    {
                        required: true,
                        message: "请选择户端",
                        trigger: "blur"
                    }
                ],
                name: [
                    {
                        required: true,
                        message: "请输入栏目名称",
                        trigger: "blur"
                    }
                ],
              
                icon: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ],
                sort: [
                    {
                        required: true,
                        validator: weightValidator,
                        trigger: "blur"
                    }
                ],
              
            },
            formLabelWidth: "150px",
            oss_url: "",
            isDragging: false,
            dragOptions: {
                animation: 200,
                group: "description",
                disabled: false,
                ghostClass: "ghost"
            }
        };
    },
    computed: {
        pageAreaOptions() {
             if (this.form.channel == "5") {
                return [
                    {
                        label: "区域一",
                        value: 1
                    },
                    {
                        label: "区域二",
                        value: 2
                    },
                    {
                        label: "区域三",
                        value: 3
                    }
                    // {
                    //     label: "区域四",
                    //     value: 4
                    // }
                ];
            } else  {
                return [
                    {
                        label: "区域一",
                        value: 1
                    }
                ];
            } 
        }
    },
    mounted() {
        this.oss_url =
            process.env.NODE_ENV == "development"
                ? "https://images.wineyun.com"
                : "https://images.vinehoo.com";
    },
    methods: {
        changeChannel(value) {
            this.form.page_area = "";
            console.log("--------000");
            console.log(value);
            this.icon_map = [];
            this.form.icon ='';
            this.form.channel = value;
        },
        //关闭弹窗
        closeDiog() {
            this.$emit("close");
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
           
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    this.form.icon = this.icon_map.join(","); //图片转为字符串
                    this.form.top_image = this.topImageArr.join(",");
                    this.form.badge = this.badge_map.join(","); //图片转为字符串
                    if(this.form.page_mode == 0){
                        const {
                        add_methodnew, auto_add_typeNew,  resultArr
                    } = this.$refs.AddGoodsConfig.getEditData(); //接受路径配置传过来的参数
                    this.form.add_method = add_methodnew;
                    
                    if(this.form.add_method == 1) {
                       
                    this.form.auto_add_type  = auto_add_typeNew;
                    this.form.auto_add_content = resultArr;
                    }
                    }
                   
                   this.form.filter=this.filterList.filter(item => item.name && item.name.trim() !== '')
                    console.log("表单", this.form);
                    this.$request.KingArea.addColumnRequest(this.form).then(
                        res => {
                            console.log("返回的值", res);
                            if (res.data.error_code == 0) {
                                this.$Message.success("添加成功");
                                this.$emit("close");
                                this.$emit("getColumnManage");
                            }
                        }
                    );
                } else {
                    console.log("失败");
                    return false;
                }
            });
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        },
        
         //筛选项
         filterSet(row){
            this.filterViewDialogStatus =true;
            // this.filterList = row.filter.map(item => ({ ...item, key: new Date().getTime(),}));
            // this.rowData = row;
        },
        deleteFilter(item, index){
            console.log(item);
            
            this.filterList.splice(index, 1);
        },
        addType(){
           this.addChildrenVisiable = true;
        },
        filterSure(){
            this.filterViewDialogStatus =false;
            this.filterList = this.filterList.filter(item => item.name && item.name.trim() !== '');
            this.filterStr = this.filterList.map(obj => obj.name).join('、');
        },
        view(row) {
            this.addChildrenVisiable = true;
            this.$nextTick(() => {
                this.$refs.addChild.setFormData(row);
            });
        },
        addType() {
            this.addChildrenVisiable = true;
            this.$nextTick(() => {
                this.$refs.addChild.setFormData(null);
            });
        },
        handleChildSubmit(formData) {
        console.log(formData);
        
            if (formData.sort>=0) {
                // 编辑现有数据
                const index = this.filterList.findIndex(item => item.sort === formData.sort);
                if (index > -1) {
                    this.filterList[index] = { ...formData };
                }
            } else {
                // 添加新数据
                let sortNum = this.filterList.length;
                this.filterList.push({
                    ...formData,
                    id: 0, // 生成临时 id
                    sort:sortNum
                });
            }
            this.addChildrenVisiable = false;
        },
        deleteData(row) {
            this.$confirm('确认删除该子项?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const index = this.filterList.findIndex(item => item.sort === row.sort);
                if (index > -1) {
                    this.filterList.splice(index, 1);
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    });
                    // 更新显示的字符串
                    this.filterStr = this.filterList.map(obj => obj.name).join('、');
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        },
        // ���拽相关方法
        dragStart() {
            this.isDragging = true;
        },
        dragEnd() {
            this.isDragging = false;
            // 更新显示的字符串
            this.filterStr = this.filterList.map(obj => obj.name).join('、');
        },
        changePageMode(value){
            if(value===1){
                this.topImageArr = [];
                this.form.background_color = '';
            }
            
        },
        colorChange(val){
            if(val=== null){
                this.form.background_color = '';
                
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.el-input {
    position: relative;
    font-size: 14px;
    display: inline-block;
    width: 60%;
}
/deep/ .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}
/deep/ .el-form-item {
    margin-bottom: 10px;
}
/deep/ .el-form-item__error {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 75%;
    left: 0;
}
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
.king_container {
    display: flex;
}
.handle {
    color: #909399;
}
.handle:hover {
    color: #409EFF;
}
.ghost {
    opacity: 0.5;
    background: #c8ebfb;
}

.el-table__row {
    cursor: move;
}

.el-table__row:hover {
    cursor: move;
}

.table-header {
    font-size: 14px;
    color: #909399;
}

.el-table__row {
    background: #fff;
    cursor: move;
    &:hover {
        background: #f5f7fa;
    }
}

.sortable-ghost {
    opacity: 0.5;
    background: #c8ebfb !important;
}

.sortable-drag {
    opacity: 0.9;
    background: #fff;
}
</style>

