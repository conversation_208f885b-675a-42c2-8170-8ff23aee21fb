<template>
    <el-form
      ref="addGoodsRef"
      :inline="false"
      :rules="addGoodsDataRules"
      :model="addGoodsData"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="期数" prop="period">
        <el-input
          v-model.number="addGoodsData.period"
          placeholder="请输入期数"
          clearable
          style="width: 180px; margin-right: 20px"
        ></el-input>
        <el-button
          type="primary"
          @click="importGoodsData"
          :disabled="!addGoodsData.period"
          >查询</el-button
        >
      </el-form-item>
      <el-form-item label="商品简称">
        <el-input
          style="width: 180px"
          v-model="addGoodsData.short_name"
          placeholder="请输入商品简称"
          clearable
          maxlength="10"
          show-word-limit
        />
      </el-form-item>
      <!-- <el-form-item label="产品简介">
        <el-input
          style="width: 300px"
          v-model="addGoodsData.short_desc"
          placeholder="请输入产品简介"
          clearable
          maxlength="15"
          show-word-limit
        />
      </el-form-item> -->
      <el-form-item label="商品名称" prop="title">
        <el-input
          style="width: 180px"
          v-model="addGoodsData.title"
          placeholder="请输入商品名称"
          clearable
          disabled
        ></el-input>
      </el-form-item>
      <el-form-item
       v-if="chanel==1"
                    label="筛选项"
                    prop="filter_id"
                >
                <el-select
                   
                   v-model="addGoodsData.filter_id"
                   
                   filterable
                   placeholder="请输入筛选项">
                   <el-option
                   v-for="item in filterOptions"
                   :key="item.id"
                   :label="item.name"
                   :value="item.id">
                   </el-option>
               </el-select>
                </el-form-item>
      <el-form-item label="图片" prop="image">
        <vos-oss
          list-type="picture-card"
          :showFileList="true"
          :limit="1"
          :dir="dir"
          :fileList.sync="good_image"
          :disabled="true"
          ref="goodsImageRef"
        >
          <i slot="default" class="el-icon-plus"></i>
        </vos-oss>
        <span class="pic-tips">仅限正方形白底PNG 800*800</span>
      </el-form-item>
    
      <el-form-item label="状态" prop="status">
        <el-radio v-model="addGoodsData.status" :label="1">显示</el-radio>
        <el-radio v-model="addGoodsData.status" :label="0">隐藏</el-radio>
      </el-form-item>
     
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="addGoodsData.sort" :min="0" :step="1" />
      </el-form-item>
    </el-form>
  </template>
  <script>
  import VosOss from "vos-oss";
  export default {
    components: {
      VosOss,
    },
    props: ["rowData","cid","isEdit", "chanel"],
    data() {
      const checkFormId = (rues, value, callback) => {
        if (value != "") {
          if (/^[0-9]*$/.test(value)) {
            callback();
          } else {
            callback(new Error("请输入正确的期数"));
          }
        } else {
          callback(new Error("请输入期数"));
        }
      };
      const checkFormImage = (rues, value, callback) => {
        if (this.good_image.length == 0) {
          callback(new Error("请上传图片"));
        } else {
          callback();
        }
      };
      const checkFilter = (rules, value, callback) => {
            if ( this.chanel == 1&&!this.addGoodsData.filter_id
            ) {
                callback(new Error("请选择筛选项"));
            } else {
                callback();
            }
        };
      return {
       
        dir: "vinehoo/vos/marketing/",
        good_image:[],
        addGoodsData: {
            cid:0,
            period: null,
          periods_type: null,
          short_name: "",
          title: "",
          image: "",
          status: 1,
          sort: "",
          short_desc: "",
           filter_id:""
        },
        filterOptions:[],
        addGoodsDataRules: {
            period: [
            {
              required: true,
              validator: checkFormId,
            },
          ],
          filter_id:[
                    {
                        required: true,
                        validator: checkFilter,
                        trigger: "blur"
                    }
                ],
        //   goods_name: [
        //     {
        //       required: true,
        //       trigger: "blur",
        //       message: "请输入商品名称",
        //     },
        //   ],
          images: [
            {
              validator: checkFormImage,
              trigger: "change",
              required: true,
            },
          ],
          status: [
            {
              required: true,
              trigger: "change",
              message: "请选择状态",
            },
          ],
          
          sort: [
            {
              required: false,
            },
          ],
          
        },
       
      };
    },
  
    mounted() {

     
      if(this.isEdit) {
        this.dataEcho(this.rowData);
       
      } 
      this.getFilterListReq();
    },
    beforeDestroy() {
      this.addGoodsData = this.$options.data().formData  
    },
    methods: {
    
     
      dataEcho(row) {
        console.log('rowData', row);
        if (row != undefined) {
          this.addGoodsData.period = row.period;
          this.addGoodsData.id = row.id;
          this.addGoodsData.title = row.title;
          this.addGoodsData.periods_type = row.periods_type;
         
          this.addGoodsData.image = row.image;
          this.addGoodsData.status = row.status;
          this.addGoodsData.sort = row.sort;
          this.addGoodsData.short_desc = row.short_desc;
          this.good_image = [ row.image];
          this.$refs.goodsImageRef.handleviewFileList(this.good_image);
          this.addGoodsData.filter_id = this.rowData.filter_id[0];
          console.log('addgoods', this.addGoodsData);
        }
      },
      importGoodsData() {
        this.$request.activities
          .getGoodsById({
            period: this.addGoodsData.period,
          })
          .then((result) => {
            if (result.data.error_code == 0) {
            //   this.originGoodsId = this.addGoodsData.periods;
            if(result.data.data){
              this.addGoodsData.title = result.data.data.title;
            //   this.addGoodsData.periods = result.data.data.periods;
              this.addGoodsData.images = result.data.data.images;
              this.addGoodsData.periods_type = result.data.data.periods_type;
              this.good_image = [result.data.data.product_img.split(",")[0]];
             
              this.$refs.goodsImageRef.handleviewFileList(this.good_image);
            } else {
              this.$message.error("暂无数据");
            }
            
            }
          });
      },
      comfirmAddGoods() {
        this.addGoodsData.cid = this.cid;
        console.log(this.addGoodsData);
        this.$refs.addGoodsRef.validate((valid) => {
          if (valid) {
           
    
            let GoodsPath = this.isEdit ? "updateGoods" : "addGoods";
            let editID = this.isEdit ? { id: this.addGoodsData.id } : {};
            let data = {
              ...this.addGoodsData,
            };
            data.filter_id = this.chanel == 1 ? [this.addGoodsData.filter_id] : []
            this.$request.KingArea[GoodsPath](data).then((result) => {
              console.warn(result);
              if (result.data.error_code == 0) {
                this.$message({
                  type: "success",
                  message: "操作成功",
                });
                this.addGoodsVisible = false;
                this.$emit("closeGood");
                this.$emit("getGoodsList");
              }
            });
          }
        });
      },
      getFilterListReq(){
            this.$request.KingArea
                        .getcolumnFilterList({
                            id: this.cid
                        })
                        .then(res => {
                           
                            if (res.data.error_code == 0) {
                               this.filterOptions = res.data.data;
                            }
                        });
        },
    },
  };
  </script>
  <style lang="scss" scoped>
  .unit {
    margin-left: 10px;
  }
  .pic-tips {
    font-size: 12px;
    color: #6b6b6b;
    line-height: 12px;
  }
  // .set_special_price {
  //     position: absolute;
  //    top: 50%;
  // }
  
  .abc {
    width: 55px;
  
    /deep/ .el-input__inner {
      padding: 2px;
      height: 25px;
      text-align: center;
    }
  }
  </style>
  