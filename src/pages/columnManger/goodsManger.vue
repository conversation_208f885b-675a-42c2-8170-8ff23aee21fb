<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-select
                    v-show="chanel == 1"
                    class="m-r-10"
                    v-model="pageAttr.filter_id"
                    filterable
                    multiple
                    clearable=""
                    size="mini"
                    placeholder="筛选项"
                >
                    <el-option
                        v-for="item in filterOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>

                <!-- 新增：商品状态筛选 -->
                <el-select
                    class="m-r-10"
                    v-model="pageAttr.onsale_status"
                    multiple
                    clearable
                    size="mini"
                    placeholder="商品状态"
                >
                    <el-option label="待上架" :value="0" />
                    <el-option label="待售中" :value="1" />
                    <el-option label="在售中" :value="2" />
                    <el-option label="已下架" :value="3" />
                    <el-option label="已售罄" :value="4" />
                </el-select>

                <!-- 新增：添加类型筛选 -->
                <el-select
                    class="m-r-10"
                    v-model="pageAttr.add_type"
                    multiple
                    clearable
                    size="mini"
                    placeholder="添加类型"
                >
                    <el-option label="手动" :value="0" />
                    <el-option label="自动" :value="1" />
                </el-select>

                <el-button
                    class="m-r-10"
                    type="primary"
                    size="mini"
                    @click="search"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="(isEdit = false), (goodsdialogStatus = true)"
                    >添加商品</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                    @sort-change="onSortChange"
                >
                    <el-table-column
                        align="center"
                        label="期数"
                        prop="period"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="商品名称"
                        prop="title"
                        min-width="200"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="图片"
                        prop="image"
                        min-width="120"
                    >
                        <template #default="imageUrl">
                            <el-image
                                style="width: 100px; height: 100px"
                                :src="imageUrl.row.image"
                                fit="cover"
                            ></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-if="chanel == 1"
                        align="center"
                        label="筛选项"
                        prop="filter_id"
                        min-width="120"
                    >
                        <template #default="scope">
                            <span>{{
                                scope.row.filter.length
                                    ? scope.row.filter[0].name
                                    : ""
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="排序"
                        min-width="70"
                        prop="sort"
                        sortable
                    >
                    </el-table-column>
                    <el-table-column
                        prop="status"
                        align="center"
                        label="状态"
                        min-width="100"
                    >
                        <template #default="status">
                            <span>{{
                                status.row.status == 0 ? "隐藏" : "显示"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="商品状态"
                        min-width="100"
                        prop="onsale_status"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.onsale_status | onsaleStatsText }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="add_type"
                        align="center"
                        label="添加类型"
                        min-width="100"
                    >
                        <template #default="status">
                            <span>{{
                                status.row.add_type == 0 ? "手动" : "自动"
                            }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        min-width="170"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="editClick(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >

                            <el-button
                                @click="confirmDelete(row.row)"
                                type="text"
                                size="mini"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="添加商品"
                :visible.sync="goodsdialogStatus"
                width="80%"
                append-to-body
            >
                <div style="overflow-y: auto;">
                    <goodView
                        ref="goodViewTag"
                        v-if="goodsdialogStatus"
                        :cid="id"
                        :isEdit="isEdit"
                        :rowData="rowData"
                        :chanel="chanel"
                        @getGoodsList="getGoodsList"
                        @closeGood="closeGood"
                    ></goodView>
                </div>
                <span
                    slot="footer"
                    style="display: flex; justify-content: center"
                >
                    <el-button @click="goodsdialogStatus = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmAddGoods"
                        >确定</el-button
                    >
                </span>
            </el-dialog>
        </div>

        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import goodView from "./goodView.vue";

export default {
    components: { goodView },
    props: ["id", "chanel"],
    data() {
        return {
            rowData: {},
            pageAttr: {
                page: 1,
                limit: 10,
                status: "",
                filter_id: [],
                onsale_status: [2],
                add_type: [],
                sort: ""
            },
            tableData: [],
            total: 0,
            goodsdialogStatus: false,
            isEdit: false,
            filterOptions: []
        };
    },
    filters: {
        onsaleStatsText(val) {
            switch (val) {
                case 0:
                    return "待上架";
                case 1:
                    return "待售中";
                case 2:
                    return "在售中";
                case 3:
                    return "已下架";
                case 4:
                    return "已售罄";
                default:
                    return "-";
            }
        }
    },
    mounted() {
        // this.getChannel();
        if (this.chanel == 1) {
            this.getFilterListReq();
        }
        this.getGoodsList();
    },
    methods: {
        search() {
            this.pageAttr.page = 1;
            this.getGoodsList();
        },
        //商品管理列表
        async getGoodsList() {
            const param = {
                cid: this.id,
                // page_area: this.page_area,
                page: this.pageAttr.page,
                limit: this.pageAttr.limit,
                filter_id: this.pageAttr.filter_id,
                onsale_status: this.pageAttr.onsale_status,
                add_type: this.pageAttr.add_type.length
                    ? this.pageAttr.add_type
                    : "",
                sort: this.pageAttr.sort
            };
            console.log("列表-----", param);
            let res = await this.$request.KingArea.getGoodsMangerList(param);

            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        // //提交新增表单
        // submitForm() {
        //     this.$refs.addTag.submitForm();
        // },
        // //提交编辑表单
        // ViewSubmitForm() {
        //     this.$refs.viewTag.submitForm();
        // },
        // //查询
        // search() {
        //     this.pageAttr.page = 1;
        //     this.getColumnManage();
        // },

        comfirmAddGoods() {
            console.log("0909099");
            this.$refs.goodViewTag.comfirmAddGoods();
        },
        onSortChange(e) {
            console.log("onSortChange", e);
            const { prop, order } = e;
            let sort_field = "";
            let sort_order = "";
            switch (order) {
                case "ascending":
                    sort_field = prop;
                    sort_order = "asc";
                    break;
                case "descending":
                    sort_field = prop;
                    sort_order = "desc";
                    break;
            }
            this.pageAttr.page = 1;
            this.pageAttr.sort = sort_order;
            this.getGoodsList();
        },
        confirmDelete(row) {
            this.$confirm(
                "确定要删除这个商品吗？删除后无法恢复。",
                "删除确认",
                {
                    confirmButtonText: "确定删除",
                    cancelButtonText: "取消",
                    type: "warning"
                }
            )
                .then(() => {
                    this.deleteData(row);
                })
                .catch(() => {
                    this.$message.info("已取消删除");
                });
        },
        async deleteData(row) {
            let data = {
                id: row.id
            };
            let res = await this.$request.KingArea.deleteGoods(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getGoodsList();
            }
        },
        closeGood() {
            this.goodsdialogStatus = false;
        },
        editClick(row) {
            console.log(row);
            this.isEdit = true;
            this.goodId = row.id;
            this.rowData = row;
            this.goodsdialogStatus = true;
        },
        filterAddType(value, row) {
            return row.add_type === value;
        },
        //   shaixuanfilter(value, row) {

        //     return  row.filter_id.length ? row.filter_id[0] == value : false;
        //   },

        filterOnsaleStatus(value, row) {
            return row.onsale_status === value;
        },

        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getGoodsList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getGoodsList();
        },
        getFilterListReq() {
            this.$request.KingArea.getcolumnFilterList({
                id: this.id,
                query_type: 1
            }).then(res => {
                if (res.data.error_code == 0) {
                    this.filterOptions = res.data.data;
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
