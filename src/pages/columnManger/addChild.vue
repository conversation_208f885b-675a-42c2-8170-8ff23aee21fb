<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="formRules"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item label-width="150px" label="子项名称">
                <el-input v-model="form.name" style="width: 140px;"></el-input>
            </el-form-item>
            <AddGoodsConfig
                ref="childAddGoodsConfig"
                :add_method="form.add_method"
                :auto_add_type="form.auto_add_type"
                :auto_add_content="form.auto_add_content"
                :isEdit="isEdit"
            ></AddGoodsConfig>
            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('formRules')">确 定</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
// import { sort } from "core-js/core/array";
import AddGoodsConfig from "../../components/addGoodsCon/addGoodsCon.vue";
export default {
    components: {
        AddGoodsConfig
    },
    data() {
        return {
            form: {
                name: "",
                id: 0,
                add_method: 0,
                auto_add_type: 0,
                auto_add_content: [],
            },
            formRules: {
                name: [
                    {
                        required: true,
                        message: "输入名称类型",
                        trigger: "blur"
                    }
                ],
            },
            formLabelWidth: "150px",
            loading: false,
            isEdit:false
        };
    },
    methods: {
        setFormData(data) {
            console.log('data---' ,data);
            
            if (data) {
                this.isEdit =true;
                this.form = JSON.parse(JSON.stringify({
                    name: data.name,
                    id: data.id,
                    sort :data.sort,
                    add_method: data.add_method || 0,
                    auto_add_type: data.auto_add_type || 0,
                    auto_add_content: data.auto_add_content || [],
                }));
               
            } else {
                this.form = {
                    name: "",
                    id: 0,
                    add_method: 0,
                    auto_add_type: 0,
                    auto_add_content: [],
                };
            }
        },

        closeDiog() {
            this.$emit('cancel');
        },

        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    const {
                        add_methodnew, 
                        auto_add_typeNew,  
                        resultArr
                    } = this.$refs.childAddGoodsConfig.getEditData();
                    
                    const submitData = {
                        ...this.form,
                        add_method: add_methodnew,
                        auto_add_type: auto_add_typeNew,
                        auto_add_content: resultArr
                    };
                    this.$emit('submit', submitData);
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
