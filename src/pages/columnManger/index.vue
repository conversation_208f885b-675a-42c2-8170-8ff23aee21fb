<template>
    <div class="order-layout">
        <div class="order-form">
            <el-card>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="channel"
                    filterable
                    size="mini"
                    placeholder="请选择频道"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in objectOptions"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <!-- <el-select
                    class="m-r-10 w-mini"
                    v-model="page_area"
                    filterable
                    size="mini"
                    placeholder="请选择区域"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in pageAreaOptions"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select> -->
                <el-select
                    class="m-r-10 w-mini"
                    v-model="pageAttr.status"
                    filterable
                    size="mini"
                    placeholder="状态"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in stautusOptions"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>

                <el-button type="primary" size="mini" @click="search()"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >新增</el-button
                >
            </el-card>
        </div>
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="频道"
                        prop="channel"
                        min-width="80"
                    >
                    <template slot-scope="scope">
                           
                           {{scope.row.channel | onChanelText}}
                       
                   </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="图标"
                        prop="icon"
                        min-width="120"
                    >
                        <template #default="imageUrl">
                            <el-image
                                style="width: 100px; height: 100px"
                                :src="imageUrl.row.icon"
                                fit="cover"
                            ></el-image>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="标题"
                        prop="name"
                        min-width="150"
                    >
                    </el-table-column>
                   
                    <el-table-column
                        align="center"
                        label="状态"
                        min-width="100"
                        prop="status"
                    >
                        <template #default="status">
                            <span>{{
                                status.row.status == 0 ? "禁用" : "启用"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="排序"
                        min-width="70"
                        prop="sort"
                    >
                    </el-table-column>
                    <el-table-column
                        prop="updateor"
                        align="center"
                        label="最近修改人"
                        min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="创建人"
                        min-width="80"
                        prop="createor"
                    >
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="展示模式"
                        min-width="100"
                        prop="page_mode"
                    >
                    <template #default="scope">
                            <span>{{
                                scope.row.page_mode == 1 ? "筛选" : "浏览"
                            }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        align="center"
                        label="最近修改时间"
                        min-width="160"
                        prop="update_time"
                    >
                    </el-table-column>
                    

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        min-width="170"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="showGoodsList(row.row)"
                                type="text"
                                size="mini"
                                >商品管理</el-button
                            >
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >

                            <el-popconfirm
                                confirm-button-text="确定"
                                cancel-button-text="取消"
                                title="确定改变状态吗？"
                                @confirm="
                                    updateEnable(
                                        row.row,
                                        row.row.status == 0 ? '1' : '0'
                                    )
                                "
                            >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    v-if="row.row.status == 0"
                                    type="text"
                                    style="margin-left:10px"
                                    >启用</el-button
                                >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    type="text"
                                    v-if="row.row.status == 1"
                                    style="margin-left:10px"
                                    >禁用</el-button
                                >
                            </el-popconfirm>
                            <el-button
                                @click="deleteData(row.row)"
                                type="text"
                                size="mini"
                                style="margin-left:10px"
                                >删除</el-button
                            >
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>
        <!-- 新增 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="新增栏目"
                :visible.sync="dialogStatus"
                width="60%"
            >
                <div style="overflow-y: auto;">
                    <Add
                        ref="addTag"
                        v-if="dialogStatus"
                        @close="close"
                        @getColumnManage="getColumnManage"
                    ></Add>
                </div>
            </el-dialog>
        </div>
        <!-- 编辑 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑栏目"
                :visible.sync="viewDialogStatus"
                width="60%"
                :before-close="closeViewDialogStatus"
            >
                <div style="overflow-y: auto;">
                    <Views
                        ref="viewTag"
                        v-if="viewDialogStatus"
                        :editId="editId"
                        :isEdit="isEdit"
                        @closeViewDialogStatus="closeViewDialogStatus"
                        @getColumnManage="getColumnManage"
                    ></Views>
                </div>
            </el-dialog>
        </div>
         <!-- 商品管理 -->
         <div>
            <el-dialog
                :close-on-click-modal="false"
                title="商品管理"
                :visible.sync="goodsListVisible"
                width="90%"
                :before-close="closeGoodsListVisible"
            >
                <div style="overflow-y: auto;">
                    <goodsManger
                        ref="viewTag"
                        v-if="goodsListVisible"
                        :id="editId"
                        :chanel="rowData.page_mode"
                        @closeGoodsListVisible="closeGoodsListVisible"
                        @getColumnManage="getColumnManage"
                    ></goodsManger>
                </div>
            </el-dialog>
        </div>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Add from "./add.vue";
import Views from "./view.vue";
import goodsManger from "./goodsManger.vue";
export default {
    components: { Add, Views,goodsManger },

    data() {
        return {
            rowData: {},
            editId: "",
            tableData: [],
            objectOptions: [
                {
                    name: "闪购",
                    id: 1
                },
                {
                    name: "秒发",
                    id: 2
                },
                {
                    name: "跨境",
                    id: 8
                },
                {
                    name: "尾货",
                    id: 9
                },
                {
                    name: "推荐",
                    id: 20
                },
                {
                    name: "首页",
                    id: 0
                }
                ],
            channel: "",
            page_area: "",
            dialogStatus: false,
            viewDialogStatus: false,
            goodsListVisible:false,
            pageAttr: {
                page: 1,
                limit: 10,
                status: ""
            },
            total: 0,
            isEdit: false,
            pageAreaOptions: [
                {
                    label: "区域一",
                    value: 1
                },
                {
                    label: "区域二",
                    value: 2
                },
                {
                    label: "区域三",
                    value: 3
                }
                // {
                //     label: "区域四",
                //     value: 4
                // }
            ],
            stautusOptions: [
                {
                    label: "禁用",
                    value: 0
                },
                {
                    label: "启用",
                    value: 1
                }
            ]
        };
    },
    mounted() {
        // this.getChannel();
        this.getColumnManage();
    },
    methods: {
        // //获取频道
        // async getChannel() {
        //     let res = await this.$request.KingArea.getChannel({ type: 1 });
        //     console.log("频道", res);
        //     if (res.data.error_code == 0) {
        //         this.objectOptions = res.data.data;
        //     }
        // },
        //栏目管理列表
        async getColumnManage() {
            let res = await this.$request.KingArea.getColumnManagerList({
                channel: this.channel,
                // page_area: this.page_area,
                page: this.pageAttr.page,
                status: this.pageAttr.status,
                limit: this.pageAttr.limit
            });
            console.log("列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        //提交新增表单
        submitForm() {
            this.$refs.addTag.submitForm();
        },
        //提交编辑表单
        ViewSubmitForm() {
            this.$refs.viewTag.submitForm();
        },
        //查询
        search() {
            this.pageAttr.page = 1;
            this.getColumnManage();
        },
        //更改状态
        async updateEnable(row, status) {
            let data = {
                id: row.id,
                status: status
            };
            let res = await this.$request.KingArea.updateCloumnStatus(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getColumnManage();
            }
        },

        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            this.getColumnManage();
        },
         //关闭商品管理弹框
         closeGoodsListVisible() {
            this.goodsListVisible = false;
            this.getColumnManage();
        },
        view(row) {
            this.viewDialogStatus = true;
            this.editId = row.id;
            // let data = {
            //     id: row.id
            // };
            // this.$request.KingArea.KingDetailPath(data).then(res => {
            //     if (res.data.error_code == 0) {
            //         console.log("编辑res", res);
            //         this.rowData = res.data.data;
            //         console.log("88888888", this.rowData);
            //     }
            // });
            this.isEdit = true;
        },
        
        showGoodsList(row) {
            this.editId = row.id;
            this.rowData = row;
            this.goodsListVisible = true;
        },
        async deleteData(row) {
           
            
            let data = {
                id: row.id,
            };
            let res = await this.$request.KingArea.deleteColumn(data);
            if (res.data.error_code == 0) {
                this.$message.success("操作成功");
                this.getColumnManage();
            }
        },
        close() {
            this.dialogStatus = false;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            this.getColumnManage();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            this.getColumnManage();
        }
    },

    filters: {
        onChanelText(val) {
            switch (val) {
                case 1:
                    return "闪购";
                case 2:
                    return "秒发";
                case 8:
                    return "跨境";
                case 9:
                return "尾货";
                case 20:
                return "推荐";
                case 0:
                return "首页";
                default:
                    return "-";
            }
        },
       
    },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
