<template>
    <div class="goods-sort-container">
        <!-- 顶部操作栏 -->
        <div class="top-actions">
            <div class="action-buttons">
                <el-button
                    type="success"
                    size="small"
                    icon="el-icon-position"
                    @click="openPromoteDialog"
                    >投放</el-button
                >
                <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-setting"
                    @click="openConfigDialog"
                    >配置权重</el-button
                >
                <el-button
                    type="info"
                    size="small"
                    icon="el-icon-refresh"
                    @click="refreshData"
                    :loading="refreshLoading"
                    >刷新</el-button
                >
                <div class="auto-refresh-container">
                    <el-checkbox
                        v-model="autoRefresh"
                        @change="handleAutoRefreshChange"
                        >自动刷新</el-checkbox
                    >
                    <el-select
                        v-model="refreshInterval"
                        size="small"
                        placeholder="选择频率"
                        :disabled="!autoRefresh"
                        @change="handleIntervalChange"
                    >
                        <el-option
                            v-for="item in refreshIntervalOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div class="coin-info" v-if="promotionCoins > 0">
                <span class="coin-label">投放币:</span>
                <span class="coin-value">{{ promotionCoins }}</span>
            </div>
        </div>
        <div class="content-wrapper">
            <!-- 商品列表 - 左侧 -->
            <div class="goods-list-container">
                <div class="goods-list" ref="goodsList">
                    <div
                        v-for="(item, index) in displayedGoods"
                        :key="index"
                        class="goods-item"
                        :data-item-id="item.id"
                    >
                        <!-- 商品图片 -->
                        <div class="goods-image">
                            <img :src="item.banner_img" alt="商品图片" />
                            <!-- 商品曝光值指示器 - 左上角 -->

                            <div
                                class="item-exposure-badge"
                                @click="showScoreDetails(item)"
                            >
                                <span style="font-size: 15px"
                                    >序号：{{ index + 1 }}
                                </span>
                                分数：{{ item.score.toFixed(2) }}
                            </div>
                            <!-- 商品期数指示器 - 右上角 -->
                            <div
                                class="item-period-badge"
                                @click="copyToClipboard(item.id)"
                            >
                                期数：{{ item.id }}
                            </div>
                        </div>

                        <!-- 商品信息 -->
                        <div class="goods-info">
                            <!-- 商品标题和标签 -->
                            <div class="goods-header">
                                <h3 class="goods-title">{{ item.title }}</h3>
                            </div>

                            <!-- 商品描述 -->
                            <div class="goods-desc">
                                <p>{{ item.brief }}</p>
                            </div>
                            <!-- 商品标签 -->
                            <div
                                class="goods-labels"
                                v-if="item.label && item.label.length > 0"
                            >
                                <el-tag
                                    v-for="(label, labelIndex) in item.label"
                                    :key="labelIndex"
                                    size="mini"
                                    type="info"
                                    class="goods-label-tag"
                                >
                                    {{ label }}
                                </el-tag>
                            </div>

                            <!-- 商品底部价格信息 -->
                            <div class="goods-footer">
                                <div class="goods-price">
                                    <span class="price-symbol">¥</span>
                                    <span class="price-value">{{
                                        item.price
                                    }}</span>
                                </div>
                                <div class="goods-sales">
                                    已售{{ item.purchased
                                    }}<span v-if="item.limit_number">
                                        /限量{{ item.limit_number }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载更多指示器 -->
                    <div v-if="loading" class="loading-indicator">
                        <i class="el-icon-loading"></i> 加载中...
                    </div>

                    <!-- 无更多数据提示 -->
                    <div v-if="!hasMoreData && !loading" class="no-more-data">
                        没有更多商品了
                    </div>
                </div>
            </div>

            <!-- 已投放商品 - 右侧（表格形式） -->
            <div class="promoted-goods-container">
                <div class="promoted-header">
                    <h3>已投放商品</h3>
                    <!-- 添加Tab切换 -->
                    <el-tabs
                        v-model="activeTab"
                        @tab-click="handleTabClick"
                        class="promoted-tabs"
                    >
                        <el-tab-pane label="投放中" name="1"></el-tab-pane>
                        <el-tab-pane label="投放结束" name="2"></el-tab-pane>
                    </el-tabs>
                </div>
                <el-table
                    v-loading="promotedLoading"
                    :data="promotedGoods"
                    style="width: 100%"
                    size="small"
                    border
                    stripe
                    class="promoted-table"
                >
                    <el-table-column label="商品" min-width="180">
                        <template slot-scope="scope">
                            <div class="promoted-product-cell">
                                <div class="promoted-image">
                                    <img
                                        :src="scope.row.banner_img"
                                        alt="商品图片"
                                    />
                                </div>
                                <div class="promoted-title-container">
                                    <div class="promoted-title">
                                        {{ scope.row.title }}
                                    </div>
                                    <div class="promoted-brief">
                                        {{ scope.row.brief }}
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="period"
                        label="期数"
                        width="70"
                        align="center"
                    ></el-table-column>
                    <el-table-column label="类型" width="90" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.type == 1 ? "固定位置" : "滑动位置" }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="sort"
                        label="投放排名"
                        width="90"
                        align="center"
                    ></el-table-column>
                    <el-table-column label="当前排名" width="90" align="center">
                        <template slot-scope="scope">
                            <span
                                :class="{
                                    'rank-worse':
                                        scope.row.rank > scope.row.before_rank,
                                }"
                            >
                                {{ scope.row.rank }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="before_rank"
                        label="原始排名"
                        width="90"
                        align="center"
                    ></el-table-column>

                    <el-table-column label="价格" width="100" align="center">
                        <template slot-scope="scope">
                            <span class="promoted-price"
                                >¥{{ scope.row.price }}</span
                            >
                        </template>
                    </el-table-column>

                    <el-table-column label="当前分值" width="80" align="center">
                        <template slot-scope="scope">
                            <span
                                class="promoted-score"
                                @click="showScoreDetails(scope.row)"
                                >{{ scope.row.score.toFixed(2) }}</span
                            >
                        </template>
                    </el-table-column>

                    <el-table-column
                        prop="purchased"
                        label="已售"
                        width="70"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="uname"
                        label="投放人"
                        width="80"
                        align="center"
                    ></el-table-column>

                    <el-table-column
                        prop="start_time"
                        label="开始时间"
                        width="150"
                        align="center"
                    ></el-table-column>
                    <el-table-column
                        prop="end_time"
                        label="结束时间"
                        width="150"
                        align="center"
                    ></el-table-column>
                    <!-- 取消投放的按钮 - 只在投放中tab显示 -->
                    <el-table-column
                        v-if="activeTab === '1'"
                        label="操作"
                        width="110"
                        align="center"
                    >
                        <template slot-scope="scope">
                            <el-button
                                type="danger"
                                size="mini"
                                @click="cancelPromotion(scope.row)"
                                :loading="scope.row.cancelLoading"
                            >
                                取消投放
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container" v-if="promotedTotal > 0">
                    <el-pagination
                        @size-change="handlePromotedSizeChange"
                        @current-change="handlePromotedCurrentChange"
                        :current-page="promotedQuery.page"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="promotedQuery.limit"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="promotedTotal"
                    >
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 配置权重弹窗 -->
        <el-dialog
            title="配置权重"
            :visible.sync="configDialogVisible"
            width="700px"
            :close-on-click-modal="false"
        >
            <div class="config-form">
                <el-form label-position="top" size="small">
                    <!-- 权重配置 -->
                    <div class="weight-section">
                        <h3 class="section-title">权重配置</h3>
                        <div class="weight-list">
                            <el-form-item
                                v-for="(
                                    item, index
                                ) in weightConfigData.weightList"
                                :key="item.key"
                                :label="item.label"
                            >
                                <el-input-number
                                    v-model="
                                        weightConfigData.weightList[index].value
                                    "
                                    :min="0"
                                    :precision="2"
                                    :step="1"
                                    controls-position="right"
                                ></el-input-number>
                            </el-form-item>
                        </div>
                    </div>

                    <!-- 时间衰退配置 -->
                    <div
                        class="time-weight-section"
                        v-if="weightConfigData.timeWeightList.length > 0"
                    >
                        <h3 class="section-title">时间衰退配置</h3>
                        <el-table
                            :data="weightConfigData.timeWeightList"
                            border
                            style="width: 100%"
                            size="small"
                        >
                            <el-table-column
                                label="起始小时"
                                width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.start_hour"
                                        :min="0"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="结束小时"
                                width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.end_hour"
                                        :min="scope.row.start_hour + 1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="起始系数"
                                min-width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.start_val"
                                        :min="0"
                                        :precision="2"
                                        :step="0.1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="结束系数"
                                min-width="100"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.end_val"
                                        :min="0"
                                        :precision="2"
                                        :step="0.1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="操作"
                                width="80"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-delete"
                                        size="mini"
                                        circle
                                        @click="removeTimePeriod(scope.$index)"
                                    ></el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-button
                            type="primary"
                            size="small"
                            icon="el-icon-plus"
                            @click="addTimePeriod"
                            style="margin-top: 10px"
                            >添加时间段</el-button
                        >
                    </div>

                    <!-- 标签配置 -->
                    <div class="label-weight-section">
                        <h3 class="section-title">标签配置</h3>
                        <el-table
                            :data="weightConfigData.labelWeightList"
                            border
                            style="width: 100%"
                            size="small"
                        >
                            <el-table-column
                                label="标签"
                                width="140"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-select
                                        v-model="scope.row.label_id"
                                        placeholder="选择标签"
                                        size="mini"
                                        filterable
                                        clearable
                                        @change="
                                            handleLabelChange(
                                                scope.$index,
                                                scope.row.label_id
                                            )
                                        "
                                    >
                                        <el-option
                                            v-for="label in labelList"
                                            :key="label.id"
                                            :label="label.name"
                                            :value="label.id"
                                        ></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="开始位置" align="center">
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.start_index"
                                        :min="1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column label="结束位置" align="center">
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.end_index"
                                        :min="scope.row.start_index || 1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column label="间隔" align="center">
                                <template slot-scope="scope">
                                    <el-input-number
                                        v-model="scope.row.interval"
                                        :min="1"
                                        :step="1"
                                        controls-position="right"
                                        size="mini"
                                    ></el-input-number>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="操作"
                                width="80"
                                align="center"
                            >
                                <template slot-scope="scope">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-delete"
                                        size="mini"
                                        circle
                                        @click="removeLabelConfig(scope.$index)"
                                    ></el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <el-button
                            type="primary"
                            size="small"
                            icon="el-icon-plus"
                            @click="addLabelConfig"
                            style="margin-top: 10px"
                            >添加标签</el-button
                        >
                    </div>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <!-- <el-button @click="resetConfig">重置</el-button> -->
                <el-button type="primary" @click="saveConfigAndClose"
                    >保存配置</el-button
                >
            </span>
        </el-dialog>

        <!-- 投放弹窗 -->
        <el-dialog
            title="商品投放"
            :visible.sync="promoteDialogVisible"
            width="600px"
            :close-on-click-modal="false"
        >
            <div class="promote-form">
                <el-form label-position="top" :model="promoteForm" size="small">
                    <!-- 商品搜索/选择 -->
                    <el-form-item label="选择商品">
                        <el-input
                            v-model="searchKeyword"
                            placeholder="输入商品期数"
                            prefix-icon="el-icon-search"
                            clearable
                            @blur="searchGoods"
                            @keyup.enter.native="searchGoods"
                        ></el-input>

                        <div
                            class="search-results"
                            v-if="searchResults.length > 0"
                        >
                            <el-radio-group
                                v-model="promoteForm.selectedGoodId"
                            >
                                <div
                                    v-for="item in searchResults"
                                    :key="item.id"
                                    class="search-item"
                                >
                                    <el-radio :label="item.id">
                                        <div class="search-item-content">
                                            <div class="search-item-image">
                                                <img
                                                    :src="item.imageUrl"
                                                    alt="商品图片"
                                                />
                                            </div>
                                            <div class="search-item-info">
                                                <div class="search-item-title">
                                                    {{ item.title }}
                                                </div>
                                                <div class="search-item-price">
                                                    ¥{{ item.price }}
                                                </div>
                                                <!-- <div
                                                    class="search-item-position"
                                                    v-if="
                                                        getCurrentPositionIndex(
                                                            item.id
                                                        ) !== -1
                                                    "
                                                >
                                                    当前位置:
                                                    <span class="position-value"
                                                        >第{{
                                                            getCurrentPositionIndex(
                                                                item.id
                                                            ) + 1
                                                        }}位</span
                                                    >
                                                </div> -->
                                            </div>
                                        </div>
                                    </el-radio>
                                </div>
                            </el-radio-group>
                        </div>
                        <div
                            class="no-results"
                            v-else-if="searchKeyword && !loading"
                        >
                            未找到相关商品
                        </div>
                    </el-form-item>

                    <!-- 投放时间 - 分开的开始时间和结束时间 -->
                    <el-form-item label="开始时间">
                        <el-date-picker
                            v-model="promoteForm.startTime"
                            type="datetime"
                            placeholder="选择开始时间"
                            value-format="yyyy-MM-dd HH:mm"
                            format="yyyy-MM-dd HH:mm"
                            :disabled="!promoteForm.selectedGoodId"
                            style="width: 100%"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间">
                        <el-date-picker
                            v-model="promoteForm.endTime"
                            type="datetime"
                            placeholder="选择结束时间"
                            value-format="yyyy-MM-dd HH:mm"
                            format="yyyy-MM-dd HH:mm"
                            :disabled="
                                !promoteForm.selectedGoodId ||
                                !promoteForm.startTime
                            "
                            :picker-options="{
                                disabledDate(time) {
                                    return (
                                        promoteForm.startTime &&
                                        time.getTime() <
                                            new Date(
                                                promoteForm.startTime
                                            ).getTime()
                                    );
                                },
                            }"
                            style="width: 100%"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="目标投放位置">
                        <el-input-number
                            v-model="promoteForm.sort"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="类型">
                        <el-radio-group v-model="promoteForm.type">
                            <el-radio :label="1">固定位置</el-radio>
                            <el-radio :label="2">滑动位置</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <!-- 投放币信息 - 简化显示 -->
                    <!-- <el-form-item label="投放币信息">
                        <div class="coin-balance-container">
                            <div class="coin-balance">
                                <div class="balance-label">可用投放币</div>
                                <div class="balance-value">
                                    {{ promotionCoins }}
                                </div>
                            </div>
                        </div>
                    </el-form-item> -->
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="promoteDialogVisible = false"
                    >取消</el-button
                >
                <el-button
                    type="primary"
                    @click="submitPromotion"
                    :disabled="!canPromote"
                >
                    确认投放
                </el-button>
            </span>
        </el-dialog>

        <!-- 评分详情弹窗 -->
        <el-dialog
            title="评分详情"
            :visible.sync="scoreDetailsDialogVisible"
            width="800px"
            height="1000px"
            :close-on-click-modal="true"
        >
            <div class="score-details-container">
                <div class="score-details-header">
                    <div class="total-score">
                        <div class="score-value">
                            {{ currentItemScore.toFixed(2) }}
                        </div>
                        <div class="score-label">总分</div>
                    </div>
                </div>
                <div class="score-details-list two-columns">
                    <div
                        v-for="(item, index) in scoreDetails"
                        :key="index"
                        class="score-detail-item"
                    >
                        <div class="score-detail-label">{{ item.label }}</div>
                        <div class="score-detail-value">{{ item.value }}</div>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: "GoodsSort",
    data() {
        return {
            allGoods: [],
            displayedGoods: [],
            currentPage: 1,
            pageSize: 200,
            total: 0,
            loading: false,
            hasMoreData: true,
            exposureCount: 0,
            exposedItems: new Set(),
            itemExposureCounts: {},
            observer: null,
            config: {
                ratingRatio: 10,
                favoriteRatio: 50,
                likeRatio: 40,
            },
            defaultConfig: {
                ratingRatio: 10,
                favoriteRatio: 50,
                likeRatio: 40,
            },
            weightConfigData: {
                weightList: [], // 权重列表（除时间衰退外的权重）
                timeWeightList: [], // 时间衰退配置
                labelWeightList: [], // 标签配置
            },
            scoreDetails: [],
            configDialogVisible: false,
            promoteDialogVisible: false,
            promotionCoins: 0, // 投放币余额
            searchKeyword: "",
            searchResults: [],
            promoteForm: {
                selectedGoodId: null,
                startTime: null, // 开始时间
                endTime: null, // 结束时间
                sort: 0,
                type: 1, // 类型：1固定位置，2滑动位置
            },

            // 已投放商品列表
            promotedGoods: [],
            promotedTotal: 0,
            promotedLoading: false,
            promotedQuery: {
                status: 1, // 1:在投商品 2:投放结束
                page: 1,
                limit: 10,
            },
            activeTab: "1", // 当前激活的tab，'1'表示投放中，'2'表示投放结束
            refreshLoading: false, // 刷新按钮加载状态
            autoRefresh: false, // 是否自动刷新
            refreshInterval: 0.084, // 刷新间隔（分钟）
            refreshTimer: null, // 定时器ID
            refreshIntervalOptions: [
                { value: 0.084, label: "5秒" },
                { value: 0.167, label: "10秒" },
                { value: 0.5, label: "30分钟" },
                { value: 1, label: "1分钟" },
                { value: 2, label: "2分钟" },
                { value: 5, label: "5分钟" },
                { value: 10, label: "10分钟" },
                { value: 20, label: "20分钟" },
                { value: 30, label: "30分钟" },
            ],
            scoreDetailsDialogVisible: false, // 评分详情弹窗是否显示
            currentItemScore: 0, // 当前选中商品的评分
            labelList: [], // 标签列表
        };
    },
    created() {
        // Initialize with page 1
        this.currentPage = 1;
    },
    mounted() {
        // 从本地存储加载自动刷新设置
        this.loadRefreshSettings();

        // Load initial data from API
        this.getProductsByPage();
        this.setupIntersectionObserver();

        // 获取已投放商品列表
        this.getPromotedGoods();

        // 获取投放币余额
        this.getUserLimit();

        // 获取标签列表
        this.getLabelList();

        // Add scroll event listener to handle pagination
        this.$nextTick(() => {
            if (this.$refs.goodsList) {
                this.$refs.goodsList.addEventListener(
                    "scroll",
                    this.handleScroll
                );
            }
        });
    },
    beforeDestroy() {
        if (this.observer) {
            this.observer.disconnect();
        }

        // Remove scroll event listener
        if (this.$refs.goodsList) {
            this.$refs.goodsList.removeEventListener(
                "scroll",
                this.handleScroll
            );
        }

        // Clear auto-refresh timer if it exists
        this.clearRefreshTimer();
    },
    computed: {
        canPromote() {
            // 检查是否选择了商品、开始时间和结束时间
            return (
                this.promoteForm.selectedGoodId &&
                this.promoteForm.startTime &&
                this.promoteForm.endTime
            );
        },
    },
    methods: {
        async getProductsByPage() {
            if (this.loading) return;

            this.loading = true;
            const data = {
                page: this.currentPage,
                limit: this.pageSize,
            };
            try {
                const res = await this.$request.coupons.getProductsByPage(data);
                if (res.data.error_code == 0) {
                    const newItems = res.data.data.list;
                    this.total = res.data.data.total;

                    if (this.currentPage === 1) {
                        // First page - replace all items
                        this.allGoods = newItems;
                        this.displayedGoods = [...newItems];
                    } else {
                        // Append new items to existing list
                        this.allGoods = [...this.allGoods, ...newItems];
                        this.displayedGoods = [
                            ...this.displayedGoods,
                            ...newItems,
                        ];
                    }

                    // Check if we have more data
                    this.hasMoreData = this.displayedGoods.length < this.total;

                    // Setup intersection observer for new items
                    this.$nextTick(() => {
                        this.observeItems();
                    });
                }
            } catch (error) {
                console.error("Error fetching products:", error);
            } finally {
                this.loading = false;
            }
        },
        // Method removed as we're now loading directly from API
        setupIntersectionObserver() {
            if ("IntersectionObserver" in window) {
                this.observer = new IntersectionObserver(
                    (entries) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting) {
                                const itemId = entry.target.dataset.itemId;
                                if (itemId) {
                                    const item = this.displayedGoods.find(
                                        (item) => item.id.toString() === itemId
                                    );
                                    if (item) {
                                        this.increaseExposure(item);
                                    }
                                }
                            }
                        });
                    },
                    {
                        root: this.$refs.goodsList,
                        threshold: 0.5,
                    }
                );

                this.$nextTick(() => {
                    this.observeItems();
                });
            }
        },
        observeItems() {
            const itemElements = this.$el.querySelectorAll(".goods-item");
            itemElements.forEach((el) => {
                if (this.observer) {
                    this.observer.observe(el);
                }
            });
        },
        handleScroll() {
            const element = this.$refs.goodsList;
            if (!element) return;

            const isBottom =
                element.scrollHeight - element.scrollTop <=
                element.clientHeight + 100;

            if (isBottom && !this.loading && this.hasMoreData) {
                // Increment page number and load next page
                this.currentPage++;
                this.getProductsByPage();
            }
        },
        // Method removed as we're now loading directly from API with getProductsByPage
        increaseExposure(item) {
            console.log("增加曝光:", item.id);
            if (!this.exposedItems.has(item.id)) {
                this.exposedItems.add(item.id);
                this.exposureCount++;
                this.$set(this.itemExposureCounts, item.id, 1);
            } else {
                const newCount = (this.itemExposureCounts[item.id] || 0) + 1;
                this.$set(this.itemExposureCounts, item.id, newCount);
            }
            this.$forceUpdate();
        },
        getItemExposureCount(item) {
            return this.itemExposureCounts[item.id] || 0;
        },
        adjustOtherRatios(changedRatio) {
            const otherRatios = Object.keys(this.config).filter(
                (key) => key !== changedRatio
            );

            const currentTotal = Object.values(this.config).reduce(
                (sum, val) => sum + val,
                0
            );

            if (currentTotal > 100) {
                const excess = currentTotal - 100;
                const otherTotal =
                    this.config[otherRatios[0]] + this.config[otherRatios[1]];

                if (otherTotal > 0) {
                    const ratio1 = this.config[otherRatios[0]] / otherTotal;
                    const ratio2 = this.config[otherRatios[1]] / otherTotal;

                    this.config[otherRatios[0]] = Math.max(
                        0,
                        Math.floor(
                            this.config[otherRatios[0]] - excess * ratio1
                        )
                    );
                    this.config[otherRatios[1]] = Math.max(
                        0,
                        Math.floor(
                            this.config[otherRatios[1]] - excess * ratio2
                        )
                    );

                    const newTotal = Object.values(this.config).reduce(
                        (sum, val) => sum + val,
                        0
                    );
                    if (newTotal < 100) {
                        this.config[otherRatios[0]] += 100 - newTotal;
                    } else if (newTotal > 100) {
                        this.config[otherRatios[0]] = Math.max(
                            0,
                            this.config[otherRatios[0]] - (newTotal - 100)
                        );
                    }
                } else {
                    this.config[changedRatio] = 100;
                }
            }
        },
        async openConfigDialog() {
            // 获取权重配置
            await this.weightConfig();
            // 显示配置弹窗
            this.configDialogVisible = true;
        },
        openPromoteDialog() {
            this.promoteDialogVisible = true;
            this.searchKeyword = "";
            this.searchResults = [];
            this.promoteForm = {
                selectedGoodId: null,
                startTime: null,
                endTime: null,
                sort: 0,
                type: 1, // 默认选择固定位置
            };
        },

        async searchGoods() {
            if (!this.searchKeyword.trim()) {
                this.searchResults = [];
                return;
            }

            this.loading = true;
            try {
                const params = {
                    periods_type: 0, // 期数频道
                    is_leftover: 0, // 不显示尾货
                    periods: this.searchKeyword.trim(), // 搜索期数
                    page: 1,
                    limit: 10,
                };

                const res = await this.$request.coupons.getPeriodsList(params);
                if (res.data.error_code === 0) {
                    // 将API返回的数据转换为我们需要的格式
                    this.searchResults = (res.data.data.list || []).map(
                        (item) => ({
                            id: item.id,
                            title: item.title,
                            description: item.brief,
                            imageUrl: item.banner_img,
                            price: item.price,
                            soldCount: item.purchased || 0,
                            totalCount: item.inventory || 0,
                        })
                    );
                }
            } catch (error) {
                console.error("搜索期数失败:", error);
                this.$message.error("搜索期数失败");
                this.searchResults = [];
            } finally {
                this.loading = false;
            }
        },
        getCurrentPositionIndex(goodId) {
            return this.displayedGoods.findIndex((item) => item.id === goodId);
        },
        getMaxTargetPosition() {
            if (!this.promoteForm.selectedGoodId)
                return this.displayedGoods.length;

            const currentIndex = this.getCurrentPositionIndex(
                this.promoteForm.selectedGoodId
            );
            if (currentIndex === -1) return this.displayedGoods.length;

            // 如果当前已经在第一位，则不能再往前移动
            // 目标位置的最大值应该是当前位置-1
            return currentIndex === 0 ? 1 : currentIndex - 1;
        },
        calculateCoinConsumption() {
            // 暂时返回0，具体消耗规则需要根据新的时间参数来确定
            return 0;
        },
        async submitPromotion() {
            if (!this.canPromote) return;

            // 显示确认对话框
            try {
                // 获取选中商品的信息
                const selectedGood = this.searchResults.find(
                    (item) => item.id === this.promoteForm.selectedGoodId
                );
                const goodTitle = selectedGood ? selectedGood.title : "商品";
                const startTime = this.promoteForm.startTime;
                const endTime = this.promoteForm.endTime;

                await this.$confirm(
                    `您将投放商品"${goodTitle}"，投放时间从 ${startTime} 到 ${endTime}。确定要投放吗？`,
                    "投放确认",
                    {
                        confirmButtonText: "确定投放",
                        cancelButtonText: "取消",
                        type: "warning",
                    }
                );
            } catch (error) {
                // 用户取消操作
                return;
            }

            this.$set(this, "loading", true);

            try {
                // 将时间转换为时间戳
                const startTimestamp = Math.floor(
                    new Date(this.promoteForm.startTime).getTime() / 1000
                );
                const endTimestamp = Math.floor(
                    new Date(this.promoteForm.endTime).getTime() / 1000
                );

                // 调用投放接口
                const params = {
                    period: this.promoteForm.selectedGoodId, // 商品期数
                    start_time: startTimestamp, // 开始时间（时间戳）
                    end_time: endTimestamp, // 结束时间（时间戳）
                    sort: this.promoteForm.sort,
                    type: this.promoteForm.type,
                };

                const res = await this.$request.coupons.addSort(params);

                if (res.data.error_code === 0) {
                    // 投放成功，关闭弹窗
                    this.promoteDialogVisible = false;

                    // 刷新左侧商品列表
                    this.currentPage = 1; // 重置到第一页
                    await this.getProductsByPage();

                    // 刷新右侧已投放商品列表
                    await this.getPromotedGoods();

                    // 更新投放币余额
                    await this.getUserLimit();

                    // 成功提示
                    this.$message.success(
                        `商品投放成功，投放时间：${this.promoteForm.startTime} 至 ${this.promoteForm.endTime}`
                    );
                }
            } catch (error) {
                console.error("投放失败:", error);
                this.$message.error("投放失败，请重试");
            } finally {
                this.$set(this, "loading", false);
            }
        },
        async saveConfig() {
            // 验证时间衰退配置
            const timePeriods = this.weightConfigData.timeWeightList;
            if (timePeriods.length === 0) {
                this.$message.error(
                    "时间衰退配置不能为空，请至少添加一个时间段。"
                );
                return false;
            }

            // 检查时间段是否覆盖24小时且没有重叠
            // 按照起始小时排序，确保检查顺序
            timePeriods.sort((a, b) => a.start_hour - b.start_hour);

            for (let i = 0; i < timePeriods.length; i++) {
                const period = timePeriods[i];
                if (period.start_hour === null || period.end_hour === null) {
                    this.$message.error("时间段的起始小时和结束小时不能为空。");
                    return false;
                }

                if (period.start_hour > period.end_hour) {
                    this.$message.error("时间段的起始小时不能大于结束小时。");
                    return false;
                }

                // 检查与上一个时间段的衔接
                if (i > 0) {
                    const prevPeriod = timePeriods[i - 1];
                    if (period.start_hour < prevPeriod.end_hour) {
                        this.$message.error(
                            `时间段 ${period.start_hour}-${period.end_hour} 的起始小时不能小于上一个时间段 ${prevPeriod.start_hour}-${prevPeriod.end_hour} 的结束小时。`
                        );
                        return false;
                    }
                }
            }

            // 检查是否覆盖24小时
            const hoursCovered = new Array(24).fill(false);
            for (const period of timePeriods) {
                for (let i = period.start_hour; i <= period.end_hour; i++) {
                    hoursCovered[i] = true;
                }
            }

            for (let i = 0; i < 24; i++) {
                if (!hoursCovered[i]) {
                    this.$message.error(
                        `时间段未覆盖到所有24小时，小时 ${i} 未被覆盖。`
                    );
                    return false;
                }
            }

            try {
                // 构建保存参数
                const params = {
                    cf: {}, // 使用cf作为顶层包装对象
                };

                // 处理普通权重
                this.weightConfigData.weightList.forEach((item) => {
                    // 保持完整的对象结构，包含label和value
                    params.cf[item.key] = {
                        label: item.label,
                        value: item.value,
                    };
                });

                // 处理时间衰退配置
                params.cf.time_weight =
                    this.weightConfigData.timeWeightList.map((item) => ({
                        label: item.label,
                        start_hour: item.start_hour,
                        end_hour: item.end_hour,
                        start_val: item.start_val,
                        end_val: item.end_val,
                    }));

                // 处理标签配置
                params.cf.label_weight =
                    this.weightConfigData.labelWeightList.map((item) => ({
                        label_id: item.label_id,
                        label: item.label,
                        start_index: item.start_index,
                        end_index: item.end_index,
                        interval: item.interval,
                    }));

                // 调用保存接口
                const res = await this.$request.coupons.saveWeightConfig(
                    params
                );

                if (res.data.error_code === 0) {
                    this.$message.success("配置保存成功");
                    return true;
                } else {
                    this.$message.error(res.data.error_msg || "保存失败");
                    return false;
                }
            } catch (error) {
                console.error("保存权重配置失败:", error);
                this.$message.error("保存失败，请重试");
                return false;
            }
        },
        async saveConfigAndClose() {
            const success = await this.saveConfig();
            if (success) {
                this.configDialogVisible = false;
            }
        },
        async resetConfig() {
            try {
                // 重新获取配置
                await this.weightConfig();
                this.$message.info("配置已重置");
            } catch (error) {
                console.error("重置配置失败:", error);
                this.$message.error("重置失败，请重试");
            }
        },
        async weightConfig() {
            try {
                // 获取权重配置
                const res = await this.$request.coupons.weightConfig();

                if (res.data.error_code === 0) {
                    // 获取cf对象中的数据
                    const data = res.data.data.cf || res.data.data;

                    // 处理除时间衰退外的权重配置
                    const weightList = [];
                    for (const key in data) {
                        if (
                            key !== "time_weight" &&
                            key !== "label_weight" &&
                            data[key]
                        ) {
                            weightList.push({
                                key: key,
                                label: data[key].label,
                                value: data[key].value,
                            });
                        }
                    }

                    // 设置权重列表
                    this.weightConfigData.weightList = weightList;

                    // 设置时间衰退配置
                    if (data.time_weight && Array.isArray(data.time_weight)) {
                        this.weightConfigData.timeWeightList =
                            data.time_weight.map((item) => ({
                                ...item,
                                label:
                                    item.label ||
                                    `${item.start_hour}-${item.end_hour}点`, // Add a default label if missing
                            }));
                    }

                    // 设置标签配置
                    if (data.label_weight && Array.isArray(data.label_weight)) {
                        this.weightConfigData.labelWeightList =
                            data.label_weight.map((item) => ({
                                ...item,
                            }));
                    } else {
                        this.weightConfigData.labelWeightList = [];
                    }

                    return true;
                } else {
                    this.$message.error(
                        res.data.error_msg || "获取权重配置失败"
                    );
                    return false;
                }
            } catch (error) {
                console.error("获取权重配置失败:", error);
                this.$message.error("获取权重配置失败，请重试");
                return false;
            }
        },
        // 获取已投放商品列表
        async getPromotedGoods() {
            this.promotedLoading = true;
            try {
                const res = await this.$request.coupons.getPeriodsSortList(
                    this.promotedQuery
                );
                if (res.data.error_code === 0) {
                    this.promotedGoods = res.data.data.list || [];
                    this.promotedTotal = res.data.data.total || 0;
                }
            } catch (error) {
                console.error("获取已投放商品列表失败:", error);
                this.$message.error("获取已投放商品列表失败");
            } finally {
                this.promotedLoading = false;
            }
        },

        // 处理已投放商品分页大小变化
        handlePromotedSizeChange(val) {
            this.promotedQuery.limit = val;
            this.promotedQuery.page = 1;
            this.getPromotedGoods();
        },

        // 处理已投放商品分页页码变化
        handlePromotedCurrentChange(val) {
            this.promotedQuery.page = val;
            this.getPromotedGoods();
        },

        // 处理Tab切换
        handleTabClick(tab) {
            this.activeTab = tab.name;
            this.promotedQuery.status = parseInt(tab.name); // 1:投放中 2:投放结束
            this.promotedQuery.page = 1; // 重置到第一页
            this.getPromotedGoods();
        },

        // 获取投放币余额
        async getUserLimit() {
            try {
                const res = await this.$request.coupons.getUserLimit();
                if (res.data.error_code === 0) {
                    this.promotionCoins = res.data.data.limit || 0;
                }
            } catch (error) {
                console.error("获取投放币余额失败:", error);
                this.$message.error("获取投放币余额失败");
            }
        },

        // 获取标签列表
        async getLabelList() {
            try {
                const params = {
                    type: 2,
                    page: 1,
                    limit: 100,
                };
                const res = await this.$request.bullein.getLabelList(params);
                if (res.data.error_code === 0) {
                    this.labelList = (res.data.data.list || []).map((item) => ({
                        id: item.id,
                        name: item.name,
                    }));
                }
            } catch (error) {
                console.error("获取标签列表失败:", error);
                this.$message.error("获取标签列表失败");
            }
        },

        // 保存刷新设置到本地存储
        saveRefreshSettings() {
            try {
                const settings = {
                    autoRefresh: this.autoRefresh,
                    refreshInterval: this.refreshInterval,
                };
                localStorage.setItem(
                    "goodsSortRefreshSettings",
                    JSON.stringify(settings)
                );
                console.log("刷新设置已保存到本地存储");
            } catch (error) {
                console.error("保存刷新设置失败:", error);
            }
        },

        // 从本地存储加载刷新设置
        loadRefreshSettings() {
            try {
                const settingsStr = localStorage.getItem(
                    "goodsSortRefreshSettings"
                );
                if (settingsStr) {
                    const settings = JSON.parse(settingsStr);
                    this.autoRefresh = settings.autoRefresh || false;
                    this.refreshInterval = settings.refreshInterval || 5;

                    // 如果自动刷新开启，启动定时器
                    if (this.autoRefresh) {
                        this.$nextTick(() => {
                            this.startRefreshTimer();
                        });
                    }
                    console.log("已从本地存储加载刷新设置");
                }
            } catch (error) {
                console.error("加载刷新设置失败:", error);
                // 出错时使用默认设置
                this.autoRefresh = false;
                this.refreshInterval = 5;
            }
        },

        // 处理自动刷新开关变化
        handleAutoRefreshChange(value) {
            if (value) {
                this.startRefreshTimer();
            } else {
                this.clearRefreshTimer();
            }

            // 保存设置到本地存储
            this.saveRefreshSettings();
        },

        // 处理刷新间隔变化
        handleIntervalChange() {
            if (this.autoRefresh) {
                // 重新启动定时器以应用新的间隔
                this.clearRefreshTimer();
                this.startRefreshTimer();
            }

            // 保存设置到本地存储
            this.saveRefreshSettings();
        },

        // 启动刷新定时器
        startRefreshTimer() {
            this.clearRefreshTimer(); // 先清除现有定时器

            // 将分钟转换为毫秒
            const intervalMs = this.refreshInterval * 60 * 1000;

            this.refreshTimer = setInterval(() => {
                this.refreshData(false); // 不显示成功提示
            }, intervalMs);

            console.log(`已启动自动刷新，间隔: ${this.refreshInterval} 分钟`);
        },

        // 清除刷新定时器
        clearRefreshTimer() {
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
                this.refreshTimer = null;
                console.log("已停止自动刷新");
            }
        },

        // 刷新数据
        async refreshData(showMessage = true) {
            if (this.refreshLoading) return;

            this.refreshLoading = true;
            try {
                // 重置左侧商品列表到第一页
                this.currentPage = 1;
                this.promotedQuery.page = 1;
                await this.getProductsByPage();

                // 刷新右侧已投放商品列表
                await this.getPromotedGoods();

                // 更新投放币余额
                await this.getUserLimit();

                if (showMessage) {
                    this.$message.success("刷新成功");
                }
            } catch (error) {
                console.error("刷新数据失败:", error);
                if (showMessage) {
                    this.$message.error("刷新数据失败");
                }
            } finally {
                this.refreshLoading = false;
            }
        },

        // 取消投放
        async cancelPromotion(item) {
            if (!item || !item.id) {
                this.$message.error("无效的投放记录");
                return;
            }

            // 显示确认对话框
            try {
                await this.$confirm("确定要取消该商品的投放吗？", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                });
            } catch (error) {
                // 用户取消操作
                return;
            }

            // 设置加载状态
            this.$set(item, "cancelLoading", true);

            try {
                // 调用取消投放接口
                const params = {
                    id: item.id, // 去除可能的前缀
                };

                const res = await this.$request.coupons.cancelSort(params);

                if (res.data.error_code === 0) {
                    // 取消投放成功，更新投放币余额
                    await this.getUserLimit();

                    // 刷新已投放商品列表
                    this.getPromotedGoods();

                    this.$message.success("已成功取消投放");
                }
            } catch (error) {
                console.error("取消投放失败:", error);
                this.$message.error("取消投放失败，请重试");
            } finally {
                this.$set(item, "cancelLoading", false);
            }
        },

        // 显示评分详情弹窗
        showScoreDetails(item) {
            if (!item || typeof item.score === "undefined") {
                this.$message.error("无法获取评分详情");
                return;
            }

            // 设置当前商品评分
            this.currentItemScore = item.score;

            // 显示弹窗
            this.scoreDetails = item.scoreDetails;
            this.scoreDetailsDialogVisible = true;
        },

        // 复制文本到剪贴板
        copyToClipboard(text) {
            // 使用现代的Clipboard API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard
                    .writeText(text)
                    .then(() => {
                        // 显示成功提示
                        this.$message.success(`已复制期数: ${text}`);
                    })
                    .catch((err) => {
                        console.error("复制失败:", err);
                        this.$message.error("复制失败，请手动复制");
                        this.fallbackCopyToClipboard(text);
                    });
            } else {
                // 回退到旧方法
                this.fallbackCopyToClipboard(text);
            }
        },

        // 回退的复制方法
        fallbackCopyToClipboard(text) {
            // 创建一个临时的textarea元素
            const textarea = document.createElement("textarea");
            textarea.value = text;
            textarea.setAttribute("readonly", "");
            textarea.style.position = "absolute";
            textarea.style.left = "-9999px";
            document.body.appendChild(textarea);

            // 选择文本并复制
            textarea.select();
            try {
                document.execCommand("copy");
                this.$message.success(`已复制期数: ${text}`);
            } catch (err) {
                console.error("复制失败:", err);
                this.$message.error("复制失败，请手动复制");
            }

            // 移除临时元素
            document.body.removeChild(textarea);
        },

        // 添加时间段
        addTimePeriod() {
            this.weightConfigData.timeWeightList.push({
                label: "自定义",
                start_hour: 0,
                end_hour: 0,
                start_val: 1,
                end_val: 1,
            });
        },

        // 移除时间段
        removeTimePeriod(index) {
            this.weightConfigData.timeWeightList.splice(index, 1);
        },

        // 添加标签配置
        addLabelConfig() {
            this.weightConfigData.labelWeightList.push({
                label_id: null,
                label: "",
                start_index: 10,
                end_index: 20,
                interval: 2,
            });
        },

        // 移除标签配置
        removeLabelConfig(index) {
            this.weightConfigData.labelWeightList.splice(index, 1);
        },

        // 处理标签选择变化
        handleLabelChange(index, labelId) {
            const selectedLabel = this.labelList.find(
                (label) => label.id === labelId
            );
            if (selectedLabel) {
                this.weightConfigData.labelWeightList[index].label =
                    selectedLabel.name;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.goods-sort-container {
    /* 投放弹窗样式 */
    .promote-form {
        .search-results {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
            border: 1px solid #ebeef5;
            border-radius: 4px;

            .search-item {
                padding: 10px;
                border-bottom: 1px solid #ebeef5;

                &:last-child {
                    border-bottom: none;
                }

                .search-item-content {
                    display: flex;
                    align-items: center;

                    .search-item-image {
                        width: 60px;
                        height: 60px;
                        margin-right: 10px;
                        overflow: hidden;
                        border-radius: 4px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .search-item-info {
                        flex: 1;

                        .search-item-title {
                            font-size: 14px;
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 5px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .search-item-price {
                            font-size: 14px;
                            color: #ff6600;
                            font-weight: bold;
                            margin-bottom: 5px;
                        }

                        .search-item-position {
                            font-size: 12px;
                            color: #666;
                            margin-top: 3px;
                            background-color: #f0f9ff;
                            padding: 3px 8px;
                            border-radius: 4px;
                            display: inline-block;

                            .position-value {
                                color: #409eff;
                                font-weight: 600;
                            }
                        }
                    }
                }
            }
        }

        .no-results {
            text-align: center;
            padding: 20px;
            color: #909399;
            font-size: 14px;
        }

        .coin-balance-container {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

            .coin-balance,
            .coin-consumption,
            .coin-remain {
                flex: 1;
                text-align: center;

                .balance-label,
                .consumption-label,
                .remain-label {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 5px;
                }

                .balance-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #67c23a;
                }

                .consumption-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #f56c6c;
                }

                .remain-value {
                    font-size: 20px;
                    font-weight: bold;
                    color: #409eff;
                }
            }

            .coin-divider {
                width: 1px;
                height: 40px;
                background-color: #dcdfe6;
            }
        }
    }

    padding: 20px;

    .top-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .action-buttons {
            display: flex;
            gap: 10px;
            align-items: center;

            .auto-refresh-container {
                display: flex;
                align-items: center;
                margin-left: 15px;
                background-color: #f8f8f8;
                padding: 5px 10px;
                border-radius: 4px;

                .el-checkbox {
                    margin-right: 10px;
                }
                /deep/ label {
                    margin: 0 !important;
                }
                .el-select {
                    margin: 10px;
                    width: 100px;
                }
            }
        }

        .coin-info {
            display: flex;
            align-items: center;
            background-color: #f8f8f8;
            padding: 5px 12px;
            border-radius: 20px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

            .coin-label {
                font-size: 14px;
                color: #666;
                margin-right: 5px;
            }

            .coin-value {
                font-size: 16px;
                font-weight: bold;
                color: #ff9900;
            }
        }
    }

    .content-wrapper {
        display: flex;
        flex-direction: column;
        gap: 20px;

        @media screen and (min-width: 768px) {
            flex-direction: row;
            justify-content: space-between;
        }
    }

    .goods-list-container {
        width: 100%;
        max-width: 375px; /* 标准H5宽度 */
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 80px);
        border-radius: 20px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
        background-color: #f8f8f8;

        /* 手机顶部状态栏 */
        &::before {
            content: "";
            display: block;
            height: 25px;
            background-color: #fff;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            border-bottom: 1px solid #eee;
        }

        /* 手机底部导航条 */
        &::after {
            content: "";
            display: block;
            height: 5px;
            width: 40%;
            background-color: #ddd;
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 5px;
            z-index: 10;
        }

        @media screen and (min-width: 768px) {
            flex: 0 0 375px; /* 固定宽度 */
            margin: 0;
        }

        .goods-list {
            flex: 1;
            overflow-y: auto;
            padding: 30px 10px 40px;
            margin-right: -5px;
            scroll-behavior: smooth;

            /* 滚动条样式 */
            &::-webkit-scrollbar {
                width: 4px;
            }

            &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb {
                background: #ddd;
                border-radius: 4px;
            }

            &::-webkit-scrollbar-thumb:hover {
                background: #ccc;
            }
        }

        /* 移除曝光值显示样式 */
        .exposure-counter {
            display: none;
        }
    }

    /* 已投放商品区域样式 - 表格形式 */
    .promoted-goods-container {
        width: 100%;
        flex: 1;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        padding: 15px;
        margin-top: 20px;

        @media screen and (min-width: 768px) {
            margin-top: 0;
            height: calc(100vh - 80px);
            overflow-y: auto;
            margin-left: 20px;
        }

        .promoted-header {
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;

            h3 {
                font-size: 16px;
                color: #333;
                margin: 0 0 15px 0;
                font-weight: 600;
            }

            .promoted-tabs {
                margin-bottom: 0;

                ::v-deep .el-tabs__header {
                    margin: 0;
                }

                ::v-deep .el-tabs__nav-wrap::after {
                    display: none;
                }

                ::v-deep .el-tabs__item {
                    padding: 0 20px;
                    height: 36px;
                    line-height: 36px;
                    font-size: 14px;

                    &.is-active {
                        color: #409eff;
                        font-weight: 600;
                    }
                }

                ::v-deep .el-tabs__active-bar {
                    background-color: #409eff;
                }
            }

            .promoted-header-actions {
                display: flex;
                gap: 10px;
            }

            .search-container {
                width: 200px;
            }
        }

        .pagination-container {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
        }

        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;

            &.status-active {
                background-color: #67c23a;
                color: white;
            }

            &.status-inactive {
                background-color: #909399;
                color: white;
            }
        }

        .promoted-table {
            .el-table__header th {
                background-color: #f5f7fa;
                color: #606266;
                font-weight: 600;
                padding: 8px 0;
            }

            .el-table__row {
                transition: background-color 0.2s;

                &:hover {
                    background-color: #f0f9ff !important;
                }
            }

            .rank-worse {
                background-color: #fef0f0;
                color: #f56c6c;
                padding: 4px 8px;
                border-radius: 4px;
                font-weight: 500;
            }
        }

        .promoted-product-cell {
            display: flex;
            align-items: center;

            .promoted-image {
                width: 50px;
                height: 50px;
                flex-shrink: 0;
                margin-right: 10px;
                border-radius: 4px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .promoted-title-container {
                display: flex;
                flex-direction: column;

                .promoted-title {
                    font-size: 13px;
                    font-weight: 600;
                    color: #333;
                    margin-bottom: 4px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .promoted-brief {
                    font-size: 12px;
                    color: #666;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        .promoted-price {
            font-size: 14px;
            font-weight: 600;
            color: #ff6600;
        }

        .no-expiry {
            font-size: 12px;
            color: #999;
        }

        .promoted-score {
            font-size: 14px;
            font-weight: 600;
            color: #409eff;
        }
    }

    .config-form {
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin: 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
        }

        .weight-section {
            margin-bottom: 20px;

            .weight-list {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 15px;
            }
        }

        .time-weight-section {
            margin-top: 30px;

            .el-table .el-table__cell {
                padding: 8px 0; // Reduce padding in table cells
            }

            /deep/ .el-input-number {
                width: 100%; // Make the input number adaptive to cell width

                .el-input__inner {
                    padding-left: 5px; // Reduce left padding of the input
                    padding-right: 25px; // Make space for controls
                }

                .el-input-number__decrease,
                .el-input-number__increase {
                    width: 20px; // Make control buttons smaller
                }
            }
        }

        .config-summary {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .el-progress {
                margin-bottom: 15px;
            }
        }
    }

    .goods-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .goods-item {
        position: relative;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        width: 100%;
        max-width: 355px; /* 适合容器的宽度 */
        margin: 0 auto 0px; /* 减少商品之间的间距 */
        transition: transform 0.3s, box-shadow 0.3s;

        &:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
        }

        .goods-image {
            width: 100%;
            height: 160px; /* 减小图片高度，使页面能够显示更多商品 */
            overflow: hidden;
            position: relative;

            &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 30px;
                background: linear-gradient(
                    to top,
                    rgba(0, 0, 0, 0.15),
                    transparent
                );
                pointer-events: none;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.5s;
                border-radius: 4px;

                &:hover {
                    transform: scale(1.05);
                }
            }

            /* 商品曝光标识 - 左上角 */
            .item-exposure-badge {
                position: absolute;
                top: 8px;
                left: 8px;
                min-width: 22px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                background-color: rgba(255, 72, 0, 0.9);
                color: #fff;
                font-size: 12px;
                font-weight: bold;
                padding: 0 6px;
                border-radius: 11px;
                z-index: 100;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            /* 商品期数标识 - 右上角 */
            .item-period-badge {
                position: absolute;
                top: 8px;
                right: 8px;
                min-width: 22px;
                height: 22px;
                line-height: 22px;
                text-align: center;
                background-color: rgba(64, 158, 255, 0.9);
                color: #fff;
                font-size: 12px;
                font-weight: bold;
                padding: 0 6px;
                border-radius: 11px;
                z-index: 100;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                cursor: pointer;
            }
        }

        .goods-info {
            padding: 12px; /* 减少内边距 */
            display: flex;
            flex-direction: column;

            .goods-header {
                margin-bottom: 6px; /* 减少间距 */

                .goods-tags {
                    margin-bottom: 6px;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;

                    .tag-item {
                        display: inline-block;
                        padding: 2px 8px;
                        background-color: #ff9900;
                        color: #fff;
                        font-size: 12px;
                        border-radius: 20px;
                        letter-spacing: 0.5px;
                    }
                }

                .goods-title {
                    margin: 0;
                    font-size: 15px; /* 稍微减小字体 */
                    font-weight: 600;
                    color: #333;
                    line-height: 1.3;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                }
            }

            .goods-desc {
                margin-bottom: 6px;
                font-size: 13px; /* 减小字体 */
                color: #666;
                line-height: 1.4;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2; /* 减少显示行数 */
                line-clamp: 2; /* 标准属性 */
                -webkit-box-orient: vertical;
                overflow: hidden;
                p {
                    margin-bottom: 0;
                }
            }

            .goods-features {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                margin-bottom: 8px;

                .feature-tag {
                    padding: 2px 8px;
                    background-color: #f5f5f5;
                    color: #666;
                    font-size: 11px; /* 减小字体 */
                    border-radius: 20px;
                }
            }

            .goods-labels {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                margin-bottom: 8px;

                .goods-label-tag {
                    font-size: 10px;

                    border-radius: 12px;
                    background-color: #e8f4fd;
                    border-color: #b3d8ff;
                    color: #409eff;
                }
            }

            .goods-footer {
                margin-top: 6px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 6px;
                border-top: 1px solid #f5f5f5;

                .goods-price {
                    color: #ff6600;

                    .price-symbol {
                        font-size: 13px;
                    }

                    .price-value {
                        font-size: 20px; /* 减小字体 */
                        font-weight: 700;
                    }
                }

                .goods-sales {
                    font-size: 11px;
                    color: #999;
                    background-color: #f9f9f9;
                    padding: 2px 6px;
                    border-radius: 20px;
                }
            }
        }
    }

    .loading-indicator {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 14px;
    }

    .no-more-data {
        text-align: center;
        padding: 15px 0;
        color: #999;
        font-size: 14px;
    }

    /* 评分详情弹窗样式 */
    .score-details-container {
        padding: 10px;

        .score-details-header {
            text-align: center;
            margin-bottom: 20px;

            .total-score {
                display: inline-block;

                .score-value {
                    font-size: 36px;
                    font-weight: bold;
                    color: #409eff;
                }

                .score-label {
                    font-size: 14px;
                    color: #606266;
                    margin-top: 5px;
                }
            }
        }

        .score-details-list {
            max-height: 400px;
            overflow-y: auto;

            &.two-columns {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 10px 20px;
            }

            .score-detail-item {
                display: flex;
                justify-content: space-between;
                padding: 6px 15px;
                border-bottom: 1px solid #ebeef5;

                &:last-child {
                    border-bottom: none;
                }

                .score-detail-label {
                    flex: 3;
                    font-size: 14px;
                    color: #606266;
                    margin-right: 10px;
                }

                .score-detail-value {
                    flex: 1;
                    text-align: right;
                    font-size: 14px;
                    font-weight: 600;
                    color: #303133;
                }
            }
        }
    }

    /* 添加鼠标指针样式，表示可点击 */
    .item-exposure-badge,
    .item-period-badge,
    .promoted-score {
        cursor: pointer;

        &:hover {
            opacity: 0.8;
        }
    }
}
</style>
