<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form
                ref="form"
                label-width="80px"
                :inline="true"
                @submit.native.prevent
                size="mini"
            >
                <el-form-item>
                    <el-input
                        v-model="queryNewcomersData.period"
                        placeholder="请输入期数"
                        @keyup.enter.native="queryNewcomers"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-input
                        v-model="queryNewcomersData.title"
                        @keyup.enter.native="queryNewcomers"
                        placeholder="请输入标题"
                        clearable
                    ></el-input>
                </el-form-item>

                <el-form-item>
                    <el-select
                        v-model="queryNewcomersData.module"
                        placeholder="请选择所属模块"
                        clearable
                        @change="changeModule"
                    >
                        <el-option
                            v-for="item in moduleOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryNewcomersData.onsale_status"
                        placeholder="请选择商品状态"
                        clearable
                    >
                        <el-option
                            v-for="item in onsaleStatusList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                    <el-form-item>
                        <el-select
                            v-model="queryNewcomersData.periods_type"
                            placeholder="请选择频道"
                            class="m-l-10"
                        >
                            <el-option
                                v-for="item in periodsTypeOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="queryNewcomers"
                        >查询</el-button
                    >
                    <el-button type="success" @click="updateNewcomers"
                        >新增</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="NewcomersList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="期数" prop="period" width="120">
                </el-table-column>
                <el-table-column label="商品名称" prop="title">
                </el-table-column>
                <el-table-column label="商品状态" width="100">
                    <template slot-scope="scope">
                        {{ onsaleStatus[scope.row.onsale_status] }}
                    </template>
                </el-table-column>
                <el-table-column label="原售价" prop="price" width="150">
                </el-table-column>
                <el-table-column label="新人专享价" width="150">
                    <template slot-scope="scope">
                        <span>{{
                            JSON.parse(scope.row.newcomer_price)[0].price
                        }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" fixed="right" width="150">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="editNewcomers(scope)"
                            size="mini"
                            >编辑</el-button
                        >
                        <el-button
                            size="mini"
                            type="text"
                            @click="deleteNewcomers(scope.row)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryNewcomersData.limit"
                :current-page="queryNewcomersData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            :close-on-click-modal="false"
            title="新人专享设置"
            :visible.sync="addNewcomersVisible"
            width="60%"
            @close="cloesNewcomers"
        >
            <UpdateNewcomers
                v-if="addNewcomersVisible"
                ref="addNewcomersDia"
                @successSubmit="addNewcomersVisible = false"
            ></UpdateNewcomers>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="addNewcomersVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdate"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import UpdateNewcomers from "./UpdateNewcomers.vue";
export default {
    components: { UpdateNewcomers },
    data() {
        return {
            NewcomersList: [],
            queryNewcomersData: {
                period: "",
                title: "",
                module: "",
                onsale_status: "",
                periods_type: 0,
                page: 1,
                limit: 10
            },
            addNewcomersVisible: false,
            periodsTypeOptions: [
                {
                    value: 0,
                    label: "闪购"
                },
                {
                    value: 1,
                    label: "秒发"
                }
            ],
            onsaleStatusList: [
                { value: 0, label: "待上架" },
                { value: 1, label: "待售中" },
                { value: 2, label: "在售中" },
                { value: 3, label: "已下架" },
                { value: 4, label: "已售罄" }
            ],
            onsaleStatus: {
                0: "待上架",
                1: "待售中",
                2: "在售中",
                3: "已下架",
                4: "已售罄"
            },
            total: 0,

            //所属模块（1：新人0元购2：新人专享特价3：福利专区）
            moduleOptions: [
                {
                    label: "新人0元购",
                    value: 1
                },
                {
                    label: "新人专享特价",
                    value: 2
                },
                {
                    label: "福利专区",
                    value: 3
                }
            ]
        };
    },
    computed: {},
    mounted() {
        this.queryNewcomersList();
    },

    methods: {
        changeModule(params) {
            if (!params) {
                this.queryNewcomersData.module = "";
            }
            this.queryNewcomers();
        },
        queryNewcomers() {
            this.queryNewcomersData.page = 1;
            this.queryNewcomersList();
        },
        groupPrice(group_price) {
            let arr = JSON.parse(group_price)
                .map(item => {
                    return item.price;
                })
                .join(" , ");
            return arr;
        },
        queryNewcomersList() {
            this.$request.newcomers
                .getNewcomersList(this.queryNewcomersData)
                .then(result => {
                    this.NewcomersList = result.data.data.list;
                    this.total = result.data.data.total;
                });
        },
        updateNewcomers() {
            this.addNewcomersVisible = true;
        },
        editNewcomers(scope) {
            this.addNewcomersVisible = true;
            this.$nextTick(() => {
                this.$refs.addNewcomersDia.dataEcho(scope.row);
            });
        },
        cloesNewcomers() {
            this.queryNewcomersList();
        },
        comfirmUpdate() {
            this.$nextTick(() => {
                this.$refs.addNewcomersDia.submitNewcomers();
            });
        },
        handleSizeChange(limit) {
            this.queryNewcomersData.limit = limit;
            this.queryNewcomersData.page = 1;
            this.queryNewcomersList();
        },
        handleCurrentChange(page) {
            this.queryNewcomersData.page = page;
            this.queryNewcomersList();
        },
        deleteNewcomers(params) {
            this.$confirm("确定删除该商品吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.$request.newcomers
                    .deleteNewcomers({
                        id: params.id,
                        periods_type: params.periods_type
                    })
                    .then(result => {
                        this.$message({
                            type: "success",
                            message: "删除成功"
                        });
                        this.queryNewcomersList();
                    });
            });
        }
    }
};
</script>

<style lang="scss" scoped></style>
