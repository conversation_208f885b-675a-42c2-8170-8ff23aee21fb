<template>
    <div>
        <el-form
            ref="newcomersRef"
            :inline="false"
            :rules="rules"
            :model="addNewcomersData"
            label-width="120px"
        >
            <el-form-item label="频道" prop="periods_type">
                <el-radio-group
                    v-model="addNewcomersData.periods_type"
                    :disabled="isEdit"
                >
                    <el-radio :label="0">闪购</el-radio>
                    <el-radio :label="1">秒发</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="商品期数" prop="period">
                <el-select
                    v-model="addNewcomersData.period"
                    placeholder="请输入商品期数"
                    clearable
                    remote
                    filterable
                    :remote-method="searchGoodsByPeriod"
                    @change="seachGoodPackage"
                >
                    <el-option
                        v-for="item in GoodsOptions"
                        :key="item.id"
                        :label="item.title"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="所属模块" prop="module">
                <el-select
                    v-model="addNewcomersData.module"
                    placeholder="请输入商品所属模块"
                    clearable
                >
                    <el-option
                        v-for="item in moduleOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-card
                shadow="always"
                v-show="addNewcomersData.period"
                style="margin-bottom: 20px"
            >
                <el-descriptions :title="selectGood.title"> </el-descriptions>
                <!-- <el-descriptions
                    v-for="i in packageProductInfo"
                    :key="i.id"
                    :colon="false"
                >
                    <el-descriptions-item :label="i.short_code"
                        >库存:{{ i.inventory }}</el-descriptions-item
                    >
                </el-descriptions> -->
                <div class="product_package">
                    <div>
                        <div
                            v-for="i in packageProductInfo"
                            :key="
                                i.id +
                                    parseInt(
                                        Math.random() * (99999 - 10000) + 1,
                                        10
                                    )
                            "
                            class="product_package_content"
                        >
                            <span class="package_first">
                                {{ i.short_code }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <div
                            v-for="i in packageProductInfo"
                            :key="
                                i.id +
                                    parseInt(
                                        Math.random() * (99999 - 10000) + 1,
                                        10
                                    )
                            "
                            class="product_package_content"
                        >
                            <span>库存:{{ i.inventory }}</span>
                        </div>
                    </div>
                </div>
                <div class="product_package">
                    <div>
                        <div
                            v-for="i in packageInfo"
                            :key="i.id"
                            class="product_package_content"
                        >
                            <span class="package_first">
                                {{ i.package_name }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <div
                            v-for="i in packageInfo"
                            :key="i.id"
                            class="product_package_content"
                        >
                            <span>{{ i.packageNum }}</span>
                            <span
                                v-for="item in i.product"
                                :key="item.id"
                                class="package_second"
                            >
                            </span>
                        </div>
                    </div>
                    <div>
                        <div
                            v-for="i in packageInfo"
                            :key="i.id"
                            class="product_package_content"
                        >
                            <span class="package_third">¥{{ i.price }}</span>
                        </div>
                    </div>
                </div>
            </el-card>
            <el-form-item label="新人价格" prop="newcomer_price_checkbox">
                <el-checkbox-group
                    v-model="newcomers_priceList"
                    @change="changeNewcomersPrice"
                >
                    <el-checkbox
                        border
                        v-for="item in packageInfo"
                        :label="item.id"
                        :key="item.id"
                        >{{ item.package_name }}</el-checkbox
                    >
                </el-checkbox-group>

                <!-- :prop="'newcomer_price.'+k+''" -->
                <el-form-item
                    style="margin-top: 20px"
                    label-width="100px"
                    :label="i.package_name"
                    v-for="i in addNewcomersData.newcomer_price"
                    :key="i.package_id"
                    prop="newcomer_price"
                >
                    <el-input-number
                        :precision="2"
                        :min="0"
                        v-model="i.price"
                        style="max-width: 350px"
                        placeholder="请输入拼团价格"
                        clearable
                    ></el-input-number>
                </el-form-item>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    data() {
        // const newcomersPricevali = ()
        const newcomersPricevali = (rules, value, callback) => {
            if (this.addNewcomersData.newcomer_price.length > 0) {
                callback();
            } else {
                callback(new Error("请输入新人价格"));
            }
        };
        const newcomersPriceCheckoutvali = (rules, value, callback) => {
            // console.warn(this.addNewcomersData.newcomer_price);
            callback();
        };

        return {
            addNewcomersDataRules: {},
            packageInfo: [],
            packageProductInfo: [],
            addNewcomersData: {
                period: "",
                module: "",
                periods_type: 0,
                newcomer_price: []
            },
            moduleOptions: [
                {
                    label: "新人0元购",
                    value: 1
                },
                {
                    label: "新人专享特价",
                    value: 2
                },
                {
                    label: "福利专区",
                    value: 3
                }
            ],
            newcomers_priceList: [],
            GoodsOptions: [],
            selectGood: {
                title: ""
            },
            isEdit: false,
            rules: {
                period: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请输入期数"
                    }
                ],
                periods_type: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择频道"
                    }
                ],
                newcomer_price: [
                    {
                        required: true,
                        trigger: "change",
                        validator: newcomersPricevali
                    }
                ],
                newcomer_price_checkbox: [
                    {
                        required: true,
                        trigger: "change",
                        validator: newcomersPriceCheckoutvali
                    }
                ],
                module: [
                    {
                        required: false
                    }
                ]
            }
        };
    },

    mounted() {},

    methods: {
        changeNewcomersPrice(val, detaEcho) {
            let tempArr = [];
            val.map(item => {
                this.packageInfo.map(item2 => {
                    if (!detaEcho) {
                        if (item2.id == item) {
                            tempArr.push({
                                package_id: item,
                                package_name: item2.package_name,
                                price: 0
                            });
                        }
                    } else {
                        if (item2.id == item.id) {
                            tempArr.push({
                                package_id: Number(item.id),
                                package_name: item2.package_name,
                                price: item.price
                            });
                        }
                    }
                });
            });
            this.addNewcomersData.newcomer_price = tempArr;
        },
        searchGoodsByPeriod(periods) {
            return new Promise((resolve, reject) => {
                this.$request.grouping
                    .getGoodsByPeriod({
                        periods: periods,
                        periods_type: this.addNewcomersData.periods_type
                    })
                    .then(res => {
                        this.GoodsOptions = res.data.data.list;
                        resolve();
                    });
            });
        },
        seachGoodPackage(id) {
            return new Promise((resolve, reject) => {
                if (id) {
                    this.GoodsOptions.map(item => {
                        if (item.id === id) {
                            this.selectGood.title = item.title;
                        }
                    });
                    this.$request.grouping
                        .getGoodsPackage({
                            period: id,
                            periods_type: this.addNewcomersData.periods_type
                        })
                        .then(res => {
                            this.packageInfo = res.data.data;
                            let packageProductInfo = res.data.data.map(
                                (item, key) => {
                                    this.packageInfo[
                                        key
                                    ].packageNum = item.product
                                        .map(item2 => {
                                            return (
                                                item2.short_code +
                                                "*" +
                                                item2.nums
                                            );
                                        })
                                        .join("+");
                                    return item.product;
                                }
                            );
                            this.packageProductInfo = packageProductInfo.flat();
                            resolve();
                        });
                } else {
                    this.selectGood = {};
                    this.packageInfo = [];
                    this.packageProductInfo = [];
                    this.GoodsOptions = [];
                    this.addNewcomersData.newcomer_price = [];
                    reject();
                }
            });
        },
        dataEcho(row) {
            if (row) {
                this.isEdit = true;
                this.addNewcomersData.id = row.id;
                this.addNewcomersData.period = row.period;
                this.addNewcomersData.newcomers_sum = row.newcomers_sum;
                this.addNewcomersData.newcomers_people = row.newcomers_people;
                this.addNewcomersData.module = row.module;
                this.addNewcomersData.periods_type = row.periods_type;
                this.newcomers_priceList = JSON.parse(row.newcomer_price).map(
                    item => {
                        return item.package_id;
                    }
                );
                let newcomers_priceArr = JSON.parse(row.newcomer_price).map(
                    item => {
                        return {
                            id: item.package_id,
                            price: item.price
                        };
                    }
                );
                this.addNewcomersData.is_invite = row.is_invite;
                this.searchGoodsByPeriod(row.period).then(() => {
                    this.seachGoodPackage(row.period).then(() => {
                        this.changeNewcomersPrice(newcomers_priceArr, true);
                    });
                });
            }
        },
        submitNewcomers() {
            this.$refs.newcomersRef.validate(valid => {
                if (valid) {
                    if (this.addNewcomersData.newcomer_price.length == 0) {
                        this.$message.warning("请选择套餐");
                        return;
                    }

                    let addNewcomersData = JSON.parse(
                        JSON.stringify(this.addNewcomersData)
                    );

                    addNewcomersData.newcomer_price.map(item => {
                        delete item.package_name;
                    });
                    addNewcomersData.newcomer_price = addNewcomersData.newcomer_price.map(
                        item => {
                            return {
                                package_id: item.package_id,
                                price: item.price.toFixed(2)
                            };
                        }
                    );
                    addNewcomersData.newcomer_price = JSON.stringify(
                        addNewcomersData.newcomer_price
                    );
                    let methods = this.isEdit
                        ? "updateNewcomers"
                        : "addNewcomers";
                    console.warn(addNewcomersData);
                    console.warn(methods);
                    this.$request.newcomers[methods](addNewcomersData)
                        .then(res => {
                            if (res.data.error_code == 0) {
                                this.$emit("successSubmit");
                            } else {
                                this.addNewcomersData = JSON.parse(
                                    JSON.stringify(this.addNewcomersData)
                                );
                            }
                        })
                        .catch(e => {
                            this.addNewcomersData = JSON.parse(
                                JSON.stringify(addNewcomersData)
                            );
                        });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.product_package {
    display: flex;
    // flex-direction: column;
    .product_package_content {
        margin-bottom: 10px;
        .package_first {
            margin-right: 20px;
        }
        .package_second {
            // width: 300px;
            margin-right: 20px;
        }
        .package_third {
            margin-right: 20px;
        }
    }
}
</style>
