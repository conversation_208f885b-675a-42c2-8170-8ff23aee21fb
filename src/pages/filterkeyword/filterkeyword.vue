<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form
                ref="form"
                label-width="80px"
                :inline="true"
                @submit.native.prevent
                size="mini"
            >
                <el-form-item>
                    <el-input
                        v-model="queryKeywordData.name"
                        placeholder="请输入关键词"
                        @keyup.enter.native="queryKeyword"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryKeywordData.type"
                        placeholder="请选择分类"
                        clearable
                    >
                        <el-option
                            v-for="item in typeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryKeywordData.channel"
                        placeholder="频道"
                        clearable
                    >
                        <el-option
                            v-for="item in channelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryKeywordData.status"
                        placeholder="状态"
                        clearable
                    >
                        <el-option
                            v-for="item in [
                                { value: 1, label: '启用' },
                                { value: 0, label: '禁用' }
                            ]"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryKeyword"
                        >查询</el-button
                    >
                    <el-button type="success" @click="updateKeyword"
                        >新增</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="KeywordList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="筛选项名称" prop="name" width="250">
                </el-table-column>
                <el-table-column label="分类" prop="type">
                    <template slot-scope="scope">
                        {{ scope.row.type | toText(typeOptions) }}
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort"> </el-table-column>
                <el-table-column label="频道" prop="channel">
                    <template slot-scope="scope">
                        <!-- {{ scope.row.scope}} -->
                        {{ scope.row.channel | toText(channelOptions) }}
                    </template>
                </el-table-column>
                <el-table-column label="状态" prop="status">
                    <template slot-scope="scope">
                        <span
                            v-if="scope.row.status == 1 ? true : false"
                            style="color: #3fbf54"
                            >启用</span
                        >
                        <span v-else style="color: #fb0401">禁用</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="editScreen(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table></el-card
        >
        <el-dialog
            :close-on-click-modal="false"
            title="关键词设置"
            :visible.sync="keywprdVisible"
            width="60%"
            @close="closeKeyword"
        >
            <el-form
                :model="screenData"
                ref="screenData"
                :rules="rules"
                label-width="80px"
                :inline="false"
                size="normal"
                v-if="keywprdVisible"
            >
                <el-form-item label="分类" prop="type">
                    <el-select
                        v-model="screenData.type"
                        placeholder="请选择分类"
                        filterable
                        @change="changeType(false)"
                    >
                        <el-option
                            v-for="item in typeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="名称" prop="source_id">
                    <el-select
                        v-if="screenData.type == 1"
                        v-model="screenData.source_id"
                        placeholder="请选择国家"
                        clearable
                        filterable
                    >
                        <el-option
                            v-for="(i, k) in nameList"
                            :key="k"
                            :label="i.country_name_cn"
                            :value="i.id"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-if="screenData.type == 2"
                        v-model="screenData.source_id"
                        placeholder="请选择分类"
                        clearable
                        filterable
                    >
                        <el-option
                            v-for="(i, k) in nameList"
                            :key="k"
                            :label="i.name"
                            :value="i.id"
                        >
                        </el-option>
                    </el-select>
                    <el-select
                        v-if="screenData.type == 3"
                        v-model="screenData.source_id"
                        placeholder="请选择分类"
                        clearable
                        filterable
                    >
                        <el-option
                            v-for="(i, k) in nameList"
                            :key="k"
                            :label="i.name"
                            :value="i.id"
                        >
                        </el-option>
                    </el-select>
                    <div v-if="screenData.type == 4">
                        <el-input-number
                            v-model="min_price"
                            :precision="2"
                            :min="0"
                            clearable
                        ></el-input-number>
                        -
                        <el-input-number
                            v-model="max_price"
                            :min="min_price"
                            :precision="2"
                            clearable
                        ></el-input-number>
                    </div>
                    <el-select
                        v-if="screenData.type == 5"
                        v-model="screenData.source_id"
                        placeholder="请选择产区"
                        clearable
                        filterable
                        remote
                        :remote-method="regionsRemoteMethod"
                        :loading="regionsRemoteMethodLoading"
                    >
                        <el-option
                            v-for="item in regionsOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="频道" prop="channel">
                    <el-select
                        v-model="screenData.channel"
                        placeholder="请选择频道"
                        clearable
                    >
                        <el-option
                            v-for="item in channelOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number
                        v-model="screenData.sort"
                    ></el-input-number>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="screenData.status">
                        <el-radio key="1" :label="1"> 启用 </el-radio>
                        <el-radio key="0" :label="0"> 禁用 </el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="keywprdVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmUpdate"
                    >确定</el-button
                >
            </span>
        </el-dialog>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryKeywordData.page"
                :page-size="queryKeywordData.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
    </div>
</template>

<script>
export default {
    name: "Vue2ProductLibraryKeyword",

    data() {
        return {
            channelOptions: [
                { value: 1, label: "闪购" },
                { value: 2, label: "秒发" },
                { value: 3, label: "跨境" },
                // { value: 4, label: "尾货" },
                { value: 5, label: "闪购烈酒" }
            ],
            typeOptions: [
                { value: 1, label: "国家" },
                { value: 2, label: "类型" },
                { value: 3, label: "关键词" },
                { value: 4, label: "价格" },
                { value: 5, label: "产区" }
            ],
            queryKeywordData: {
                name: "",
                type: "",
                channel: "",
                status: "",
                page: 1,
                limit: 10
            },
            total: 0,
            KeywordList: [],
            keywprdVisible: false,
            screenData: {
                type: 3,
                name: "",
                source_id: "",
                status: "",
                sort: 0,
                channel: ""
            },
            min_price: 0,
            max_price: 0,
            screenDataClone: {
                type: 3,
                name: "",
                source_id: "",
                status: "",
                sort: 0,
                channel: ""
            },
            nameList: [],
            rules: {
                type: [
                    {
                        required: "true",
                        message: "请输入分类",
                        trigger: "change"
                    }
                ],
                source_id: [
                    {
                        required: "true",
                        message: "请输入名称",
                        trigger: "change"
                    }
                ],
                status: [
                    {
                        required: "true",
                        message: "请选择状态",
                        trigger: "change"
                    }
                ],
                sort: [
                    {
                        required: "true",
                        message: "请输入排序",
                        trigger: "change"
                    }
                ],
                channel: [
                    {
                        required: "true",
                        message: "请选择频道",
                        trigger: "change"
                    }
                ]
            },
            isEdit: false,
            regionsOptions: [],
            regionsRemoteMethodLoading: false
        };
    },
    filters: {
        toText(input, options = []) {
            return options.find(item => item.value === input)?.label || "";
        },
        toTypeText(input) {}
    },

    mounted() {
        this.getKeyword();
    },
    watch: {
        "screenData.source_id"(n, o) {
            if (this.screenData.type === 5) {
                this.screenData.name =
                    this.regionsOptions.find(item => item.value === n)?.label ||
                    "";
                return;
            }
            this.nameList.map(item => {
                if (n == item.id) {
                    this.screenData.name =
                        this.screenData.type == "1"
                            ? item.country_name_cn
                            : item.name;
                }
            });
        }
    },
    methods: {
        queryKeyword() {
            this.queryKeywordData.page = 1;
            this.getKeyword();
        },
        editScreen(item) {
            this.keywprdVisible = true;
            if (item.type == 4) {
                this.min_price = Number(item.name.split("-")[0]);
                this.max_price = Number(item.name.split("-")[1]);
            }
            this.screenData = {
                type: item.type,
                name: item.name,
                source_id: item.source_id,
                status: item.status,
                sort: item.sort,
                channel: item.channel,
                id: item.id
            };
            this.isEdit = true;
            this.changeType(true);
        },
        getKeyword() {
            this.$request.filterkeyword
                .getFilterList(this.queryKeywordData)
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.KeywordList = result.data.data.list;
                        this.total = result.data.data.total;
                    }
                });
        },
        comfirmUpdate() {
            if (this.screenData.type == 4) {
                if (this.max_price > this.min_price) {
                    this.screenData.name =
                        this.min_price + "-" + this.max_price;
                } else {
                    this.$message.error("最大价格不能小于或等于最小价格");
                    return false;
                }
            }
            this.$refs["screenData"].validate(valid => {
                if (valid) {
                    let method = this.isEdit ? "updateScreen" : "addSreen";
                    this.$request.filterkeyword[method](this.screenData).then(
                        res => {
                            if (res.data.error_code === 0) {
                                this.$message.success("操作成功");
                                this.keywprdVisible = false;
                                this.getKeyword();
                            }
                        }
                    );
                }
            });
        },
        closeKeyword() {
            this.screenData = JSON.parse(JSON.stringify(this.screenDataClone));
            this.isEdit = false;
            this.nameList = [];
            this.max_price = 0;
            this.min_price = 0;
        },
        updateKeyword() {
            this.keywprdVisible = true;
            this.changeType(false);
        },
        changeType(isEdit) {
            let type = this.screenData.type;
            this.screenData.source_id =
                this.screenData.source_id === 0
                    ? ""
                    : this.screenData.source_id;
            if (!isEdit) {
                this.screenData.source_id = "";
            }
            if (type == 1) {
                this.$request.filterkeyword
                    .countryList({
                        page: 1,
                        limit: 999
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.nameList = res.data.data.list;
                        }
                    });
            } else if (type == 2) {
                this.$request.filterkeyword
                    .typeList({
                        page: 1,
                        limit: 999
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.nameList = res.data.data.list;
                        }
                    });
            } else if (type == 3) {
                this.$request.filterkeyword
                    .keywordList({
                        page: 1,
                        limit: 999
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.nameList = res.data.data.list;
                        }
                    });
            } else if (type == 4) {
                this.$nextTick(() => {
                    this.screenData.source_id = 0;
                });
            } else if (type === 5) {
                if (isEdit) {
                    this.regionsOptions = [
                        {
                            value: this.screenData.source_id,
                            label: this.screenData.name
                        }
                    ];
                }
            }
            console.warn(this.nameList);
            this.isEdit ? "" : (this.screenData.source_id = "");
        },
        handleSizeChange(val) {
            this.queryKeywordData.page = 1;
            this.queryKeywordData.limit = val;
            this.getKeyword();
        },
        handleCurrentChange(val) {
            this.queryKeywordData.page = val;
            this.getKeyword();
        },
        changeStatus(row) {
            this.$request.filterkeyword.switchScreen({
                id: row.id,
                status: row.status == 1 ? 1 : 0
            });
        },
        regionsRemoteMethod(query) {
            if (query !== "") {
                this.regionsRemoteMethodLoading = true;
                this.$request.filterkeyword
                    .getRegionsList({
                        keyword: query,
                        page: 1,
                        limit: 20
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.regionsOptions = res.data.data.list.map(
                                item => ({
                                    value: item.id,
                                    label: item.regions_name_cn
                                })
                            );
                            this.regionsRemoteMethodLoading = false;
                        }
                    });
            } else {
                this.regionsOptions = [];
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 20px;
}
</style>
