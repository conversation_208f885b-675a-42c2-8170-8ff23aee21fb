<template>
    <el-form
        ref="addGoodsRef"
        :inline="false"
        :rules="addGoodsDataRules"
        :model="addGoodsData"
        label-width="100px"
        label-position="left"
    >
        <el-form-item label="期数" prop="periods">
            <el-input
                v-model.number="addGoodsData.periods"
                placeholder="请输入期数"
                clearable
                style="width: 180px; margin-right: 20px"
            ></el-input>
            <el-button
                type="primary"
                @click="importGoodsData"
                :disabled="!addGoodsData.periods"
                >查询</el-button
            >
            <el-button
                type="success"
                @click="queryCustomPackages"
                :disabled="!addGoodsData.periods"
                style="margin-left: 10px"
                >查询自选套餐</el-button
            >
        </el-form-item>
        <el-form-item label="商品简称">
            <el-input
                style="width: 180px"
                v-model="addGoodsData.goods_short_name"
                placeholder="请输入商品简称"
                clearable
                maxlength="7"
                show-word-limit
            />
        </el-form-item>
        <el-form-item label="产品简介">
            <el-input
                style="width: 300px"
                v-model="addGoodsData.product_introduction"
                placeholder="请输入产品简介"
                clearable
                maxlength="15"
                show-word-limit
            />
        </el-form-item>
        <el-form-item label="商品名称" prop="goods_name">
            <el-input
                style="width: 180px"
                v-model="addGoodsData.goods_name"
                placeholder="请输入商品名称"
                clearable
                disabled
            ></el-input>
        </el-form-item>
        <el-form-item label="图片" prop="images">
            <vos-oss
                list-type="picture-card"
                :showFileList="true"
                :limit="1"
                :dir="dir"
                :fileList.sync="good_image"
                :disabled="true"
                ref="goodsImageRef"
            >
                <i slot="default" class="el-icon-plus"></i>
            </vos-oss>
            <span class="pic-tips">仅限正方形白底PNG 800*800</span>
        </el-form-item>
        <el-card shadow="hover" style="margin-bottom: 20px; max-width: 7500px">
            <el-row type="flex" align="middle">
                <el-col :span="9"
                    ><div><span>列表展示价格：</span></div>
                    <el-form-item
                        label="单价"
                        prop="unit_price"
                        label-width="60px"
                    >
                        <el-input-number
                            v-model="addGoodsData.unit_price"
                            :min="0"
                            :step="1"
                            size="small"
                            disabled
                        />
                        <span class="unit">元</span>
                    </el-form-item>
                    <el-form-item
                        label="市场价"
                        prop="market_price"
                        label-width="60px"
                    >
                        <el-input-number
                            v-model="addGoodsData.market_price"
                            :min="0"
                            :step="1"
                            size="small"
                            disabled
                        />
                        <span class="unit">元</span>
                    </el-form-item>
                </el-col>
                <el-col :span="15">
                    <div class="set_special_price">
                        <el-checkbox :true-label="1" :false-label="0" disabled
                            >设置专题价格</el-checkbox
                        >
                        <!-- v-model="addGoodsData.is_custom_package_price" -->
                        <!-- :disabled="isChannel === 1 ? true : false" -->
                        <div style="margin-bottom: 10px">
                            <span
                                v-for="(item, index) in packages"
                                :key="index"
                                style="margin-right: 25px"
                                >{{ item.package_name }}正常价格：{{
                                    item.price
                                }}元</span
                            >
                        </div>
                        <div>
                            <span
                                v-for="(item, index) in packages"
                                :key="item.id"
                                style="margin-right: 10px"
                                >{{ item.package_name }}专题价格：<el-input
                                    size="mini"
                                    class="abc"
                                    v-model="
                                        addGoodsData.package[index].custom_price
                                    "
                                    oninput="value=value.replace(/[^0-9.]/g,'')"
                                    disabled
                                ></el-input>
                            </span>
                        </div>
                        <div></div>
                    </div>
                </el-col>
            </el-row>
        </el-card>

        <el-form-item label="关联活动" prop="activity_id">
            <div style="display: flex">
                <div>
                    <el-select
                        v-model="addGoodsData.activity_id"
                        placeholder="请选择活动"
                        clearable
                        remote
                        filterable
                        :remote-method="queryActivitiesList"
                        @change="changeActivity"
                    >
                        <el-option
                            v-for="(i, k) in activityList"
                            :key="k"
                            :label="i.activity_name"
                            :value="i.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <div style="margin-left: 10px">
                    <el-form-item size="normal" prop="label_id">
                        <el-select
                            v-model="addGoodsData.label_id"
                            placeholder="请选择所属板块"
                            clearable
                        >
                            <el-option
                                v-for="(i, k) in activityLabelList"
                                :key="k"
                                :label="i.label_name"
                                :value="i.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
            <el-radio v-model="addGoodsData.status" :label="2">显示</el-radio>
            <el-radio v-model="addGoodsData.status" :label="1">隐藏</el-radio>
        </el-form-item>
        <el-form-item label="上架时间" prop="puton_time">
            <el-date-picker
                v-model="addGoodsData.puton_time"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
            >
            </el-date-picker>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
            <el-input-number v-model="addGoodsData.sort" :min="0" :step="1" />
        </el-form-item>
    </el-form>
</template>
<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    data() {
        const checkFormId = (rues, value, callback) => {
            if (value != "") {
                if (/^[0-9]*$/.test(value)) {
                    callback();
                } else {
                    callback(new Error("请输入正确的期数"));
                }
            } else {
                callback(new Error("请输入期数"));
            }
        };
        const checkFormImage = (rues, value, callback) => {
            if (this.good_image.length == 0) {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        return {
            dir: "vinehoo/vos/marketing/",
            addGoodsData: {
                periods: null,
                goods_short_name: "",
                product_introduction: "",
                goods_name: "",
                images: "",
                unit_price: 0,
                market_price: 0,
                activity_id: "",
                status: 2,
                puton_time: "",
                sort: "",
                label_id: "",
                is_custom_package_price: 0,
                periods_type: null,
                package: []
            },
            addGoodsDataRules: {
                periods: [
                    {
                        required: true,
                        validator: checkFormId
                    }
                ],
                goods_name: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入商品名称"
                    }
                ],
                images: [
                    {
                        validator: checkFormImage,
                        trigger: "change",
                        required: true
                    }
                ],
                unit_price: [
                    {
                        required: false
                    }
                ],
                market_price: [
                    {
                        required: false
                    }
                ],
                activity_id: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择关联活动"
                    }
                ],
                status: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择状态"
                    }
                ],
                puton_time: [
                    {
                        required: false,
                        trigger: "change",
                        message: "请选择上架时间"
                    }
                ],
                sort: [
                    {
                        required: false
                    }
                ],
                label_id: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择商品所属板块"
                    }
                ]
            },
            good_image: [],
            isEdit: false,
            activityList: [],
            originGoodsId: null,
            activityLabelList: [],
            packages: [],
            isChannel: 0
        };
    },

    mounted() {
        this.queryActivitiesList("");
    },

    methods: {
        changeActivity(val, isEdit) {
            console.log(val, this.addGoodsData);
            if (val) {
                this.$request.activities
                    .getActivityDetails({ id: val })
                    .then(res => {
                        this.activityLabelList = res.data.data.lables;
                    });
            } else {
                this.activityLabelList = [];
            }

            isEdit ? "" : (this.addGoodsData.label_id = "");
            for (var element of this.activityList) {
                if (val === element.id) {
                    if (
                        element.activity_type === 1 &&
                        !this.isEdit &&
                        this.isChannel != 1
                    ) {
                        this.addGoodsData.is_custom_package_price = 1;
                    } else {
                        this.addGoodsData.is_custom_package_price = 0;
                    }
                }
            }
        },
        queryActivitiesList(query) {
            this.$request.activities
                .getActivitiesList({
                    activity_name: query,
                    page: 1,
                    limit: 99999
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.activityList = result.data.data.list;
                    }
                });
        },
        dataEcho(row) {
            console.log(row);
            if (row != undefined) {
                this.originGoodsId = row.periods;
                this.isEdit = true;
                for (let i in this.addGoodsData) {
                    this.addGoodsData[i] = row[i];
                    if (i == "images") {
                        this.good_image = row.images.split(",");
                    }
                }
                this.isChannel = row.is_channel;

                this.packages = row.package;
                if (row.is_custom_package_price === 0) {
                    this.packages.forEach(function(element) {
                        element.custom_price = String(element.price);
                    });
                }
                this.addGoodsData.id = row.id;
                console.log(this.addGoodsData);
                this.changeActivity(row.activity_id, this.isEdit);
            }
        },
        importGoodsData() {
            this.$request.activities
                .getGoodsById({
                    period: this.addGoodsData.periods
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.originGoodsId = this.addGoodsData.periods;
                        this.addGoodsData.goods_name = result.data.data.title;
                        this.addGoodsData.unit_price = result.data.data.price;
                        this.addGoodsData.market_price =
                            result.data.data.market_price;
                        this.addGoodsData.puton_time =
                            result.data.data.onsale_time;
                        this.good_image = [
                            result.data.data.product_img.split(",")[0]
                        ];
                        this.packages = result.data.data.package;
                        this.isChannel = result.data.data.is_channel;
                        this.addGoodsData.is_custom_package_price = 0;

                        this.periods_type = result.data.data.periods_type;
                        console.log("addGoodsData", this.addGoodsData);
                        this.addGoodsData.package = result.data.data.package.map(
                            item => {
                                return {
                                    package_id: item.id,
                                    package_name: item.package_name,
                                    price: Number(item.price),
                                    market_price: Number(item.market_price),
                                    custom_price: String(item.price)
                                };
                            }
                        );

                        console.log(result.data.data);
                        this.$refs.goodsImageRef.handleviewFileList(
                            this.good_image
                        );
                    }
                });
        },
        comfirmAddGoods() {
            console.log(this.addGoodsData);
            this.$refs.addGoodsRef.validate(valid => {
                if (valid) {
                    if (this.originGoodsId != this.addGoodsData.periods) {
                        this.$message.error(
                            "您输入的期数与已导入的商品期数不一致，请重新导入商品或修改期数"
                        );
                        return;
                    }
                    let GoodsPath = this.isEdit ? "updateGoods" : "addGoods";
                    let editID = this.isEdit
                        ? { id: this.addGoodsData.id }
                        : {};
                    this.$delete(this.addGoodsData, "is_custom_package_price");

                    this.$request.activities[GoodsPath]({
                        ...this.addGoodsData
                        // periods: Number(this.addGoodsData.periods),
                        // goods_short_name: this.addGoodsData.goods_short_name,
                        // product_introduction: this.addGoodsData.product_introduction,
                        // activity_id: this.addGoodsData.activity_id,
                        // label_id: this.addGoodsData.label_id,
                        // status: this.addGoodsData.status,
                        // sort: this.addGoodsData.sort,
                    }).then(result => {
                        console.warn(result);
                        if (result.data.error_code == 0) {
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                            this.addGoodsVisible = false;
                            this.$emit("closeGoods");
                            this.originGoodsId = null;
                        }
                    });
                }
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.unit {
    margin-left: 10px;
}
.pic-tips {
    font-size: 12px;
    color: #6b6b6b;
    line-height: 12px;
}
// .set_special_price {
//     position: absolute;
//    top: 50%;
// }

.abc {
    width: 55px;

    /deep/ .el-input__inner {
        padding: 2px;
        height: 25px;
        text-align: center;
    }
}
</style>
