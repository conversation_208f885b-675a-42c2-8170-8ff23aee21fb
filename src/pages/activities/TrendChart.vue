<template>
    <el-dialog
        title="访问量趋势"
        :visible.sync="visible"
        width="70%"
        :close-on-click-modal="false"
        @closed="handleClose"
        :append-to-body="true"
    >
        <div class="filter-container">
            <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                value-format="yyyy-MM-dd"
                @change="handleDateChange"
                size="small"
            ></el-date-picker>
        </div>
        
        <div class="chart-container">
            <div 
                ref="chartContainer" 
                :style="{ height: '400px', width: '100%' }"
            ></div>
            <div v-if="loading" class="loading-mask">
                <el-loading-component></el-loading-component>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import * as echarts from 'echarts';
import moment from 'moment';

export default {
    name: 'TrendChart',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        activityId: {
            type: [Number, String],
            required: true
        }
    },
    data() {
        return {
            loading: false,
            chart: null,
            dateRange: [],
            pickerOptions: {
                shortcuts: [{
                    text: '最近一周',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick(picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            }
        };
    },
    watch: {
        visible(val) {
            if (val) {
                // 设置默认日期范围为最近30天
                const end = new Date();
                const start = new Date();
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                this.dateRange = [
                    moment(start).format('YYYY-MM-DD'),
                    moment(end).format('YYYY-MM-DD')
                ];
                this.$nextTick(() => {
                    this.initChart();
                    this.fetchData();
                });
            }
        }
    },
    methods: {
        initChart() {
            if (!this.chart) {
                this.chart = echarts.init(this.$refs.chartContainer);
                window.addEventListener('resize', this.handleResize);
            }
        },
        handleResize() {
            this.chart && this.chart.resize();
        },
        handleClose() {
            this.$emit('update:visible', false);
            if (this.chart) {
                window.removeEventListener('resize', this.handleResize);
                this.chart.dispose();
                this.chart = null;
            }
        },
        handleDateChange() {
            this.fetchData();
        },
        async fetchData() {
            if (!this.dateRange || !this.dateRange[0] || !this.dateRange[1]) {
                return;
            }

            this.loading = true;
            try {
                const [start_date, end_date] = this.dateRange;
                const response = await this.$request.activities.getPvUvTrend({
                    start_date,
                    end_date,
                    buttonId: this.activityId
                });

                if (response.data.error_code === 0) {
                    this.renderChart(response.data.data.list);
                } else {
                    this.$message.error(response.data.error_msg || '获取数据失败');
                }
            } catch (error) {
                console.error('Fetch trend data error:', error);
                this.$message.error('获取数据失败');
            } finally {
                this.loading = false;
            }
        },
        renderChart(data) {
            const dates = data.map(item => {
                // 判断是否需要显示年份
                const showYear = !data.every(d => 
                    moment(d.date).year() === moment(data[0].date).year()
                );
                return showYear 
                    ? moment(item.date).format('YY-MM-DD')
                    : moment(item.date).format('MM-DD');
            });
            const pvData = data.map(item => item.pv);
            const uvData = data.map(item => item.uv);

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'line'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                legend: {
                    data: ['PV', 'UV']
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: dates,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    splitLine: {
                        show: true
                    }
                },
                series: [
                    {
                        name: 'PV',
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 6,
                        data: pvData,
                        itemStyle: {
                            color: '#409EFF'
                        }
                    },
                    {
                        name: 'UV',
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 6,
                        data: uvData,
                        itemStyle: {
                            color: '#67C23A'
                        }
                    }
                ]
            };

            this.chart.setOption(option);
        }
    }
};
</script>

<style scoped>
.filter-container {
    margin-bottom: 20px;
}

.chart-container {
    position: relative;
}

.loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}
</style> 