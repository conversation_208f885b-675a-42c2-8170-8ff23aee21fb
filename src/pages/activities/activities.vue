<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form
                :model="activitiesQueryform"
                ref="form"
                :rules="queryActivitiesDataRules"
                label-width="80px"
                :inline="true"
                @submit.native.prevent
                size="mini"
            >
                <el-form-item>
                    <el-input
                        v-model="activitiesQueryform.activity_name"
                        placeholder="活动名称"
                        @keyup.enter.native="queryActivities"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="activitiesQueryform.status"
                        placeholder="活动状态"
                        clearable
                    >
                        <el-option label="隐藏" :value="1"> </el-option>
                        <el-option label="显示" :value="2"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryActivities"
                        >查询</el-button
                    >
                    <el-button type="success" @click="updateActivities()"
                        >新增活动</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            :shadow="never"
            style="margin-top: 10px; border: none"
            :body-style="{ padding: '0px' }"
        >
            <el-table
                :data="activitiesTableData"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="cellStyle"
                style="width: 100%"
            >
                <el-table-column label="ID" prop="id" width="80">
                </el-table-column>
                <el-table-column 
                    label="活动名称" 
                    prop="activity_name"
                >
                    <template slot-scope="{ row }">
                        <el-tag
                            size="mini"
                            :type="row.status === 1 ? 'info' : 'success'"
                            style="margin-right: 8px"
                        >
                            {{ row.status === 1 ? "隐藏" : "显示" }}
                        </el-tag>
                        {{ row.activity_name }}
                    </template>
                </el-table-column>
                <el-table-column label="排序" prop="sort" width="100">
                </el-table-column>
                <el-table-column label="商品数量" prop="goods_num" width="100">
                </el-table-column>
                <el-table-column label="创建人" prop="created_name" width="150">
                </el-table-column>
                <el-table-column label="创建时间" width="200" prop="created_at">
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="200">
                    <template slot-scope="scope">
                        <div style="display: flex; align-items: center; justify-content: flex-start; width: 180px;">
                            <el-tooltip content="查看活动访问量趋势图" placement="top">
                                <el-button
                                    type="text"
                                    size="mini"
                                    @click="showStatistics(scope.row)"
                                >
                                    <i class="el-icon-data-analysis"></i>
                                </el-button>
                            </el-tooltip>
                            <el-button
                                type="text"
                                size="mini"
                                @click="updateActivities(scope.row)"
                                >编辑</el-button
                            >
                            <el-button
                                v-if="scope.row.activity_type==1"
                                type="text"
                                size="mini"
                                @click="copyActivity(scope.row)"
                                >复制</el-button
                            >
                            <el-popover
                                placement="left-start"
                                width="200"
                                trigger="click"
                                @show="getMinipogramCode(scope.row)"
                            >
                                <img :src="minipogramCode" style="width: 180px" />
                                <el-button
                                 v-show="scope.row.activity_type==1"
                                    slot="reference"
                                    type="text"
                                    size="mini"
                                    style="margin-left: 8px"
                                   
                                    >小程序码</el-button
                                >
                            </el-popover>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="activitiesQueryform.limit"
                :current-page="activitiesQueryform.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <el-dialog
            :close-on-click-modal="false"
            :title="isEdit ? '编辑活动' : '新增活动'"
            :visible.sync="addActivitiesVisible"
            width="60%"
            @close="cloesActivities"
        >
            <add-activities
                v-if="addActivitiesVisible"
                ref="addActivitiesDia"
                @closeDia="addActivitiesVisible = false"
            ></add-activities>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="addActivitiesVisible = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="comfirmAddActivities"
                    >确定</el-button
                >
            </span>
        </el-dialog>

        <trend-chart
            :visible.sync="trendChartVisible"
            :activity-id="selectedActivityId"
        />
    </div>
</template>
<script>
import moment from "moment";
import AddActivities from "./Add.vue";
import TrendChart from "./TrendChart.vue";

export default {
    components: {
        AddActivities,
        TrendChart
    },
    data() {
        return {
            image: [],
            total: 0,
            activitiesQueryform: {
                page: 1,
                limit: 10,
                activity_name: ""
            },
            activitiesTableData: [],
            addActivitiesData: {
                activity_name: "",
                status: "",
                image: "",
                id: null
            },
            addActivitiesVisible: false,
            isEdit: false,
            addActivitiesDataRules: {
                activity_name: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur"
                    }
                ]
            },
            queryActivitiesDataRules: {},
            winePartyInfo: {},
            minipogramCode:"",
            trendChartVisible: false,
            selectedActivityId: null
        };
    },

    mounted() {
        this.getActivitiesList();
    },

    methods: {
        cellStyle({ column }) {
            if (column.property === 'activity_name') {
                return { 'text-align': 'left' };
            }
            return { 'text-align': 'center' };
        },
        formatDate(date) {
            return moment(date).format("yyyy-MM-DD HH:mm:ss");
        },
        //获取活动列表'

        getActivitiesList() {
            this.$request.activities
                .getActivitiesList(this.activitiesQueryform)
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.activitiesTableData = result.data.data.list;
                        this.total = result.data.data.total;
                    }
                });
        },
        // 查询活动
        queryActivities() {
            this.activitiesQueryform.page = 1;
            this.getActivitiesList();
        },
        //打开更新活动对话框
        updateActivities(row) {
            this.addActivitiesVisible = true;
            if (row != undefined) {
                this.isEdit = true;
                this.$nextTick(() => {
                    this.$refs.addActivitiesDia.dataEcho(row);
                });
            }
        },
        copyActivity(row){
            this.$confirm(
                "确定复制？",
                "提示",
                {
                    confirmButtonText: "确定",
                    type: "warning"
                }
            )
                .then(() => {
                    this.$request.activities
                        .activityCopy({id:row.id})
                        .then(result => {
                            if (result.data.error_code == 0) {
                                this.$message.success("复制成功");
                                this.getActivitiesList();
                            }
                        });
                        })
                .catch(() => {
                    console.log("取消");
                });
          
        },
        getMinipogramCode(params){
            console.log(params.id, this.winePartyInfo.id);
            
            if (params.id != this.winePartyInfo.id) {
                let  data = {
                        scene: String(params.id),
                        page:
                            "packageG/pages/loginTemplate/login"
                    };
                console.log(data);
                this.minipogramCode = "";
                this.$request.activities.getMinappQrcode(data).then(res => {
                    if (res.data.error_code === 0) {
                        console.warn(res.data.data);
                        this.minipogramCode =
                            "data:image/jpg;base64," + res.data.qrcode;
                        console.warn(this.minipogramCode);
                        this.winePartyInfo = params;
                    }
                });
            }
        },
        // 关闭活动对话框
        cloesActivities() {
            this.isEdit = false;
            this.getActivitiesList();
        },
        // 确认添加活动
        comfirmAddActivities() {
            this.$refs.addActivitiesDia.confirmAddActivities();
        },
        handleSizeChange(limit) {
            this.activitiesQueryform.limit = limit;
            this.activitiesQueryform.page = 1;
            this.getActivitiesList();
        },
        handleCurrentChange(page) {
            this.activitiesQueryform.page = page;
            this.getActivitiesList();
        },
        showStatistics(row) {
            this.selectedActivityId = row.id;
            this.trendChartVisible = true;
        }
    }
};
</script>
