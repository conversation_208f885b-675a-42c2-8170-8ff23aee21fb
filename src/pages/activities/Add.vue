<template>
    <el-form
        ref="addActivitiesRef"
        :inline="true"
        :rules="addActivitiesDataRules"
        :model="addActivitiesData"
        label-width="120px"
    >
        <el-form-item label="活动类型" prop="activity_type">
            <div>
                <el-radio
                    :disabled="addIsEdit"
                    v-model="addActivitiesData.activity_type"
                    :label="0"
                    >站内</el-radio
                >
                <el-radio
                    :disabled="addIsEdit"
                    v-model="addActivitiesData.activity_type"
                    :label="1"
                    >站外</el-radio
                >
                <span style="color: red"
                    >（注意：活动类型一旦确认，将不可修改）</span
                >
            </div>
        </el-form-item>
        <el-form-item label="活动名称" prop="activity_name">
            <el-input
                style="width: 380px"
                v-model="addActivitiesData.activity_name"
                size="normal"
                clearable
            ></el-input>
        </el-form-item>
        <el-form-item label="活动副标题" prop="sub_activity_name">
            <el-input
                style="width: 380px"
                v-model="addActivitiesData.sub_activity_name"
                size="normal"
                clearable
            ></el-input>
        </el-form-item>
        <div>
            <el-form-item label="题图边框" prop="title_map">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="title_map"
                    :limitWhList="[
                        [750, 464],
                        [800, 800]
                    ]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <el-form-item label="胶囊图片" prop="list_back">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="list_back"
                    :limitWhList="[1080, 314]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
        </div>
        <div>
            <el-form-item label="活动图片" prop="image">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="image"
                    :limitWhList="[750, 750]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
        </div>
        <div>
            <el-form-item label="活动链接" prop="activity_url">
                <el-input
                    style="width: 380px"
                    v-model="addActivitiesData.activity_url"
                    size="normal"
                    clearable
                ></el-input>
            </el-form-item>
        </div>
        <div>
            <el-form-item label="活动开始时间" prop="start_at">
                <el-date-picker
                    v-model="addActivitiesData.start_at"
                    type="datetime"
                    value-format="timestamp"
                    default-time="00:00:00"
                >
                </el-date-picker>
            </el-form-item>
        </div>
        <div>
            <!-- 活动结束时间 -->
            <el-form-item label="活动结束时间" prop="end_at">
                <el-date-picker
                    v-model="addActivitiesData.end_at"
                    type="datetime"
                    value-format="timestamp"
                    default-time="23:59:59"
                >
                </el-date-picker>
            </el-form-item>
        </div>
        <div>
            <!-- 活动排序 -->
            <el-form-item label="活动排序" prop="sort">
                <el-input-number v-model="addActivitiesData.sort" :step="1">
                </el-input-number>
            </el-form-item>
        </div>
        <div>
            <!-- 活动状态 -->
            <el-form-item label="活动状态" prop="status">
                <el-radio v-model="addActivitiesData.status" :label="2"
                    >显示</el-radio
                >
                <el-radio v-model="addActivitiesData.status" :label="1"
                    >隐藏</el-radio
                >
            </el-form-item>
        </div>
        <div>
            <el-form-item label="商品排序" prop="goods_sort">
                <el-radio v-model="addActivitiesData.goods_sort" :label="0"
                    >添加时间倒序</el-radio
                >
                <el-radio v-model="addActivitiesData.goods_sort" :label="1"
                    >上架时间倒序</el-radio
                >
                <el-radio v-model="addActivitiesData.goods_sort" :label="2"
                    >闪购排序值倒序</el-radio
                >
                <el-radio v-model="addActivitiesData.goods_sort" :label="3"
                    >随机</el-radio
                >
            </el-form-item>
        </div>
        <el-card shadow="hover">
            <!-- <div slot="header">
                <span>所属板块</span> 
            </div> -->
            <div style="margin-top: 20px">
                <draggable v-model="addActivitiesData.label">
                    <div
                        v-for="(item, index) in addActivitiesData.label"
                        :key="item.id"
                    >
                        <el-form-item
                            :key="item.id || item.key"
                            :label="index == 0 ? '所属板块 ' : '所属板块 '"
                            :prop="'label.' + index + '.label_name'"
                            :rules="addActivitiesDataRules.label_name"
                        >
                            <el-input
                                style="margin-right: 10px; width: 380px"
                                v-model="item.label_name"
                                size="normal"
                                clearable
                            ></el-input>
                            <el-button
                                type="primary"
                                size="defualt"
                                @click="addType"
                                v-if="index == 0"
                                >添加</el-button
                            >
                            <el-button
                                type="danger"
                                size="default"
                                @click="delType(index, item.id)"
                                v-if="addActivitiesData.label.length > 1"
                                >删除</el-button
                            >
                        </el-form-item>
                        <div>
                            <el-form-item label="板块题图边框">
                                <vos-oss
                                    list-type="picture-card"
                                    :showFileList="true"
                                    :limit="1"
                                    :dir="dir"
                                    :file-list="item.labelTitleMapFileList"
                                    :limitWhList="[
                                        [750, 464],
                                        [800, 800]
                                    ]"
                                >
                                    <i slot="default" class="el-icon-plus"></i>
                                </vos-oss>
                            </el-form-item>
                        </div>
                    </div>
                </draggable>
            </div>
        </el-card>

        <el-card shadow="hover" style="margin-top: 20px; max-width: 7500px">
            <div slot="header">
                <span> 分享配置 </span>
            </div>
            <!-- card body -->
            <div>
                <el-form-item label="分享图" prop="share_image">
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="share_image"
                        :limitWhList="[750, 750]"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="主标题" prop="main_title">
                    <el-input
                        style="width: 380px"
                        v-model="addActivitiesData.main_title"
                        size="normal"
                        clearable
                    ></el-input>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="副标题" prop="sub_title">
                    <el-input
                        style="width: 380px"
                        v-model="addActivitiesData.sub_title"
                        size="normal"
                        clearable
                    ></el-input>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="分享链接" prop="share_url">
                    <el-input
                        style="width: 380px"
                        v-model="addActivitiesData.share_url"
                        size="normal"
                        clearable
                    ></el-input>
                </el-form-item>
            </div>
        </el-card>
        <!-- <el-form-item label="分类">
                    <el-input
                        style="width: 380px"
                        v-model="test"
                        size="normal"
                        clearable
                    ></el-input>
                </el-form-item> -->
        <el-form
            ref="pageConfigRef"
            :inline="true"
            :rules="pageConfigRules"
            :model="addActivitiesData.page_config"
            label-width="110px"
        >
            <el-card
                v-if="addActivitiesData.activity_type == 1"
                shadow="hover"
                style="margin-top: 20px; max-width: 7500px"
            >
                <div slot="header">
                    <span> 自定义页面配置 </span>
                </div>
                <!-- card body -->

                <el-row>
                    <el-col :span="12">
                        <div>
                            <el-row>
                                <el-col :span="12">
                                    <el-form-item
                                        label="活动页头图"
                                        prop="head_img"
                                    >
                                        <span style="font-size: 11px;"
                                            >（建议宽度750，高度小于2000）</span
                                        >
                                        <vos-oss
                                            list-type="picture-card"
                                            :showFileList="true"
                                            :limit="1"
                                            :dir="dir"
                                            :file-list="head_img"
                                        >
                                            <i
                                                slot="default"
                                                class="el-icon-plus"
                                            ></i>
                                        </vos-oss>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item
                                        label="登录页面背景图"
                                        prop="login_bg_img"
                                        label-width="130px"
                                    >
                                        <span style="font-size: 11px;"
                                            >（建议尺寸750X1624）</span
                                        >
                                        <vos-oss
                                            list-type="picture-card"
                                            :showFileList="true"
                                            :limit="1"
                                            :dir="dir"
                                            :file-list="login_bg_img"
                                        >
                                            <i
                                                slot="default"
                                                class="el-icon-plus"
                                            ></i>
                                        </vos-oss>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                        <div>
                            <el-row>
                                <el-col :span="9">
                                    <el-form-item
                                        label="登录页面登录框背景图"
                                        prop="login_box_bg_img"
                                        label-width="170px"
                                    >
                                        <vos-oss
                                            list-type="picture-card"
                                            :showFileList="true"
                                            :limit="1"
                                            :dir="dir"
                                            :file-list="login_box_bg_img"
                                        >
                                            <i
                                                slot="default"
                                                class="el-icon-plus"
                                            ></i>
                                        </vos-oss>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="15">
                                    <div>
                                        <el-form-item
                                            label=" 登录按钮背景颜色"
                                            prop="login_button_bg_color"
                                            label-width="150px"
                                        >
                                            <el-color-picker
                                                v-model="
                                                    addActivitiesData
                                                        .page_config
                                                        .login_button_bg_color
                                                "
                                            ></el-color-picker>
                                        </el-form-item>
                                    </div>
                                    <div>
                                        <el-form-item
                                            label=" 登录按钮文字颜色"
                                            prop="login_button_text_color"
                                            label-width="150px"
                                        >
                                            <el-color-picker
                                                v-model="
                                                    addActivitiesData
                                                        .page_config
                                                        .login_button_text_color
                                                "
                                            ></el-color-picker>
                                        </el-form-item>
                                    </div>

                                    <!-- <el-form-item > -->
                                    <el-row>
                                        <el-col :span="12">
                                            <!-- <span>协议复选框未勾选图</span>
                    <vos-oss
                      list-type="picture-card"
                      :showFileList="true"
                      :limit="1"
                      :dir="dir"
                      :file-list="unchecked_protocol_img"
                      class="login_agree_box"
                    >
                      <i slot="default" class="el-icon-plus"></i>
                    </vos-oss> -->
                                            <el-form-item
                                                label="协议复选框未勾选图"
                                                prop="unchecked_protocol_img"
                                                label-width="160px"
                                            >
                                                <vos-oss
                                                    list-type="picture-card"
                                                    :showFileList="true"
                                                    :limit="1"
                                                    :dir="dir"
                                                    :file-list="
                                                        unchecked_protocol_img
                                                    "
                                                    class="login_agree_box"
                                                >
                                                    <i
                                                        slot="default"
                                                        class="el-icon-plus"
                                                    ></i>
                                                </vos-oss>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <!-- <span>协议复选框勾选图</span>
                    <vos-oss
                      list-type="picture-card"
                      :showFileList="true"
                      :limit="1"
                      :dir="dir"
                      :file-list="checked_protocol_img"
                      class="login_agree_box"
                    >
                      <i slot="default" class="el-icon-plus"></i>
                    </vos-oss> -->
                                            <el-form-item
                                                label="协议复选框勾选图"
                                                prop="checked_protocol_img"
                                                label-width="160px"
                                            >
                                                <vos-oss
                                                    list-type="picture-card"
                                                    :showFileList="true"
                                                    :limit="1"
                                                    :dir="dir"
                                                    :file-list="
                                                        checked_protocol_img
                                                    "
                                                    class="login_agree_box"
                                                >
                                                    <i
                                                        slot="default"
                                                        class="el-icon-plus"
                                                    ></i>
                                                </vos-oss>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <!-- </el-form-item> -->
                                </el-col>
                            </el-row>
                        </div>
                        <div>
                            <el-form-item label="价格前缀" prop="price_prefix">
                                <el-input
                                    style="width: 150px"
                                    v-model="
                                        addActivitiesData.page_config
                                            .price_prefix
                                    "
                                    size="normal"
                                    placeholder="请输入"
                                    clearable
                                ></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item
                                label="埋点标识"
                                prop="source_platform"
                            >
                                <el-input
                                    style="width: 160px"
                                    placeholder="source_platform"
                                    v-model="
                                        addActivitiesData.page_config
                                            .source_platform
                                    "
                                    size="normal"
                                    disabled
                                    clearable
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="" prop="source_event">
                                <el-input
                                    style="width: 135px"
                                    v-model="
                                        addActivitiesData.page_config
                                            .source_event
                                    "
                                    size="normal"
                                    placeholder="source_event"
                                    clearable
                                ></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item
                            label="小程序页面标题"
                            prop="applet_page_title"
                            label-width="130px"
                        >
                            <el-input
                                style="width: 260px"
                                v-model="
                                    addActivitiesData.page_config
                                        .applet_page_title
                                "
                                size="normal"
                                placeholder="请输入"
                                clearable
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-row type="flex" align="middle">
                            <el-col :span="14">
                                <el-form-item
                                    label="主页背景颜色"
                                    prop="class_index_color"
                                >
                                    <el-color-picker
                                        v-model="
                                            addActivitiesData.page_config
                                                .class_index_color
                                        "
                                    ></el-color-picker>
                                </el-form-item>
                                <!-- <el-form-item label="分类标题大小" prop="class_title_size">
                <el-input
                  style="width: 100px"
                  v-model="addActivitiesData.page_config.class_title_size"
                  placeholder="请输入"
                  size="normal"
                  clearable
                ></el-input>
              </el-form-item> -->

                                <el-form-item
                                    label="分类标题颜色"
                                    prop="class_title_color"
                                >
                                    <el-color-picker
                                        v-model="
                                            addActivitiesData.page_config
                                                .class_title_color
                                        "
                                    ></el-color-picker>
                                </el-form-item>
                            </el-col>
                            <el-col :span="10">
                                <div>
                                    <img
                                        src="https://images.vinehoo.com/vinehoo/activities/maersk/mearsk_title_one2.png"
                                        alt=""
                                        style="width: 100%;"
                                    />
                                </div>
                            </el-col>
                        </el-row>
                        <el-row type="flex" align="middle">
                            <el-col :span="14">
                                <div>
                                    <el-form-item
                                        label="降价标签背景颜色"
                                        prop="reduced_price_label_bg_color"
                                        label-width="140px"
                                    >
                                        <el-color-picker
                                            v-model="
                                                addActivitiesData.page_config
                                                    .reduced_price_label_bg_color
                                            "
                                        ></el-color-picker>
                                    </el-form-item>
                                </div>
                                <div>
                                    <el-form-item
                                        label="降价标签文字颜色"
                                        prop="reduced_price_label_text_color"
                                        label-width="140px"
                                    >
                                        <el-color-picker
                                            v-model="
                                                addActivitiesData.page_config
                                                    .reduced_price_label_text_color
                                            "
                                        ></el-color-picker>
                                    </el-form-item>
                                </div>

                                <!-- <el-form-item label="降价标签文字大小" prop="reduced_price_label_text_size" label-width="140px">
                <el-input
                  style="width: 100px"
                  v-model="addActivitiesData.page_config.reduced_price_label_text_size"
                  placeholder="请输入"
                  size="normal"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item label="降价标签圆角大小" prop="reduced_price_label_corner_radius" label-width="140px">
                <el-input
                  style="width: 100px"
                  v-model="addActivitiesData.page_config.reduced_price_label_corner_radius"
                  placeholder="请输入"
                  size="normal"
                  clearable
                ></el-input>
              </el-form-item> -->
                            </el-col>
                            <el-col :span="10">
                                <div>
                                    <img
                                        :src="
                                            require('@/assets/css/default/images/show_image_btn.png')
                                        "
                                        alt=""
                                        style="width: 100%;"
                                    />
                                </div>
                            </el-col>
                        </el-row>
                        <el-row type="flex" align="middle">
                            <el-col :span="14">
                                <div>
                                    <el-form-item
                                        label="分类背景颜色"
                                        prop="category_button_bg_color"
                                        label-width="140px"
                                    >
                                        <el-color-picker
                                            v-model="
                                                addActivitiesData.page_config
                                                    .category_button_bg_color
                                            "
                                        ></el-color-picker>
                                    </el-form-item>
                                </div>
                                <div>
                                    <el-form-item
                                        label="分类按钮选中颜色"
                                        prop="category_button_text_color"
                                        label-width="140px"
                                    >
                                        <el-color-picker
                                            v-model="
                                                addActivitiesData.page_config
                                                    .category_button_text_color
                                            "
                                        ></el-color-picker>
                                    </el-form-item>
                                </div>
                                <!-- <div>
                <el-form-item label="分类按钮文字大小" prop="category_button_text_size" label-width="140px">
                  <el-input
                    style="width: 100px"
                    v-model="addActivitiesData.page_config.category_button_text_size"
                    placeholder="请输入"
                    size="normal"
                    clearable
                  ></el-input>
                </el-form-item>
              </div> -->
                            </el-col>
                            <el-col :span="10">
                                <div>
                                    <img
                                        :src="
                                            require('@/assets/css/default/images/show_image_tab.png')
                                        "
                                        alt=""
                                        style="width: 100%;"
                                    />
                                </div>
                            </el-col>
                        </el-row>
                        <el-form-item
                            label="查看更多酒款图"
                            prop="view_more_wine_img"
                            label-width="150px"
                        >
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="view_more_wine_img"
                                class="login_agree_box"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                        <el-form-item
                            label="分享按钮图"
                            prop="share_button_img"
                        >
                            <vos-oss
                                list-type="picture-card"
                                :showFileList="true"
                                :limit="1"
                                :dir="dir"
                                :file-list="share_button_img"
                                class="login_agree_box"
                            >
                                <i slot="default" class="el-icon-plus"></i>
                            </vos-oss>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- <el-row>
        <el-col :span="8">
          <el-form-item label="页面底部图" prop="page_bottom_img">
            <vos-oss
              list-type="picture-card"
              :showFileList="true"
              :limit="1"
              :dir="dir"
              :file-list="page_bottom_img"
              class="login_agree_box"
            >
              <i slot="default" class="el-icon-plus"></i>
            </vos-oss>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="查看更多酒款图" prop="view_more_wine_img" label-width="150px">
            <vos-oss
              list-type="picture-card"
              :showFileList="true"
              :limit="1"
              :dir="dir"
              :file-list="view_more_wine_img"
              class="login_agree_box"
            >
              <i slot="default" class="el-icon-plus"></i>
            </vos-oss>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分享按钮图" prop="share_button_img">
            <vos-oss
              list-type="picture-card"
              :showFileList="true"
              :limit="1"
              :dir="dir"
              :file-list="share_button_img"
              class="login_agree_box"
            >
              <i slot="default" class="el-icon-plus"></i>
            </vos-oss>
          </el-form-item>
        </el-col>
      </el-row> -->
            </el-card>
        </el-form>
    </el-form>
</template>
<script>
import VosOss from "vos-oss";
import draggable from "vuedraggable";
export default {
    name: "Vue2MarketingAdd",
    components: {
        VosOss,
        draggable
    },
    data() {
        const checkTitileMap = (rules, value, callback) => {
            if (this.title_map.length == 0) {
                callback(new Error("请上传题图边框"));
            } else {
                callback();
            }
        };
        const checkImage = (rules, value, callback) => {
            if (this.image.length == 0) {
                callback(new Error("请上传活动图片"));
            } else {
                callback();
            }
        };
        const checkShareImg = (rules, value, callback) => {
            if (this.share_image.length == 0) {
                callback(new Error("请上传分享图"));
            } else {
                callback();
            }
        };
        const checkHeadImg = (rules, value, callback) => {
            if (this.head_img.length == 0) {
                callback(new Error("请上传活动页头图"));
            } else {
                callback();
            }
        };
        const checkLoginImg = (rules, value, callback) => {
            if (this.login_bg_img.length == 0) {
                callback(new Error("请上传登录页背景图"));
            } else {
                callback();
            }
        };
        const checkLoginBoxImg = (rules, value, callback) => {
            if (this.login_box_bg_img.length == 0) {
                callback(new Error("请上传登录页登录框背景图"));
            } else {
                callback();
            }
        };
        const checkUncheckedImg = (rules, value, callback) => {
            if (this.unchecked_protocol_img.length == 0) {
                callback(new Error("请上传协议复选框未勾选图"));
            } else {
                callback();
            }
        };
        const checkCheckedImg = (rules, value, callback) => {
            if (this.checkCheckedImg.length == 0) {
                callback(new Error("请上传协议复选框已勾选图"));
            } else {
                callback();
            }
        };
        const checkBottomImg = (rules, value, callback) => {
            if (this.page_bottom_img.length == 0) {
                callback(new Error("请上传页面底部图"));
            } else {
                callback();
            }
        };
        const checkMoreImg = (rules, value, callback) => {
            if (this.view_more_wine_img.length == 0) {
                callback(new Error("请上传查看更多酒款图"));
            } else {
                callback();
            }
        };
        const checkShareBtnImg = (rules, value, callback) => {
            if (this.share_button_img.length == 0) {
                callback(new Error("请上传 分享按钮图"));
            } else {
                callback();
            }
        };
        const checkShareConfig = (rules, value, callback) => {
            console.warn(this.addActivitiesData.main_title);
            if (
                this.share_image.length == 0 ||
                this.addActivitiesData.main_title == "" ||
                this.addActivitiesData.sub_title == "" ||
                this.addActivitiesData.share_url == ""
            ) {
                callback(new Error("请完善分享配置"));
            } else {
                callback();
            }
        };

        return {
            test: "",
            addActivitisLabel: [
                {
                    label: "",
                    key: new Date().getTime()
                }
            ],

            image: [],
            title_map: [],
            list_back: [],
            share_image: [],
            head_img: [],
            login_bg_img: [],
            login_box_bg_img: [],
            unchecked_protocol_img: [],
            checked_protocol_img: [],
            page_bottom_img: [],
            view_more_wine_img: [],
            share_button_img: [],
            dir: "vinehoo/vos/marketing/",
            addActivitiesData: {
                activity_type: 0,
                activity_name: "",
                sub_activity_name: "",
                start_at: "",
                end_at: "",
                image: "",
                title_map: "",
                list_back: "",
                goods_sort: 0,
                activity_url: "",
                share_image: "",
                main_title: "",
                sub_title: "",
                share_url: "",
                id: null,
                sort: 0,
                status: 1,
                label: [
                    {
                        label_name: "",
                        key: new Date().getTime(),
                        labelTitleMapFileList: []
                    }
                ],
                page_config: {
                    head_img: "",
                    login_bg_img: "",
                    login_box_bg_img: "",
                    login_button_bg_color: "",
                    login_button_text_color: "",
                    unchecked_protocol_img: "",
                    checked_protocol_img: "",
                    price_prefix: "",
                    source_platform: "vinehoo",
                    source_event: "",
                    applet_page_title: "",
                    class_title_size: "",
                    class_title_color: "",
                    reduced_price_label_bg_color: "",
                    reduced_price_label_text_color: "",
                    reduced_price_label_text_size: "",
                    reduced_price_label_corner_radius: "",
                    category_button_bg_color: "",
                    category_button_text_color: "",
                    category_button_text_size: "",
                    page_bottom_img: "",
                    view_more_wine_img: "",
                    share_button_img: "",
                    class_index_color: ""
                }
            },
            addIsEdit: false,
            pageConfigRules: {
                // head_img: [
                //   {
                //     required: true,
                //     validator: checkHeadImg,
                //   },
                // ],
                // login_bg_img: [
                //   {
                //     required: true,
                //     validator: checkLoginImg,
                //   },
                // ],
                // login_box_bg_img: [
                //   {
                //     required: true,
                //     validator: checkLoginBoxImg,
                //   },
                // ],
                // login_button_bg_color: [
                //   {
                //     required: true,
                //     message: "请选择登录按钮背景颜色",
                //     trigger: "change",
                //   },
                // ],
                // login_button_text_color: [
                //   {
                //     required: true,
                //     message: "请选择登录按钮字体颜色",
                //     trigger: "change",
                //   },
                // ],
                // unchecked_protocol_img: [
                //   {
                //     required: true,
                //     validator: checkUncheckedImg,
                //   },
                // ],
                // checked_protocol_img: [
                //   {
                //     required: true,
                //     validator: checkCheckedImg,
                //   },
                // ],
                // price_prefix: [
                //   {
                //     required: true,
                //     message: "请输入价格前缀",
                //     trigger: "blur",
                //   },
                // ],
                // source_platform: [
                //   {
                //     required: true,
                //     message: "请输入埋点标签-来源平台",
                //     trigger: "blur",
                //   },
                // ],
                // source_event: [
                //   {
                //     required: true,
                //     message: "请输入埋点标签-来源事件",
                //     trigger: "blur",
                //   },
                // ],
                // applet_page_title: [
                //   {
                //     required: true,
                //     message: "请输入小程序页面标题",
                //     trigger: "blur",
                //   },
                // ],
                // class_title_size: [
                //   {
                //     required: true,
                //     message: "请输入分类标题大小",
                //     trigger: "blur",
                //   },
                // ],
                // class_title_color: [
                //   {
                //     required: true,
                //     message: "请选择分类标题颜色",
                //     trigger: "blur",
                //   },
                // ],
                // reduced_price_label_bg_color: [
                //   {
                //     required: true,
                //     message: "请选择降价标签背景颜色",
                //     trigger: "blur",
                //   },
                // ],
                // reduced_price_label_text_color: [
                //   {
                //     required: true,
                //     message: "请选择降价标签文字颜色",
                //     trigger: "blur",
                //   },
                // ],
                // reduced_price_label_text_size: [
                //   {
                //     required: true,
                //     message: "请输入降价标签文字大小",
                //     trigger: "blur",
                //   },
                // ],
                // reduced_price_label_corner_radius: [
                //   {
                //     required: true,
                //     message: "请输入降价标签圆角大小",
                //     trigger: "blur",
                //   },
                // ],
                // category_button_bg_color: [
                //   {
                //     required: true,
                //     message: "请选择分类按钮背景颜色",
                //     trigger: "blur",
                //   },
                // ],
                // category_button_text_color: [
                //   {
                //     required: true,
                //     message: "请选择分类按钮选中颜色",
                //     trigger: "blur",
                //   },
                // ],
                // category_button_text_size: [
                //   {
                //     required: true,
                //     message: "请输入分类按钮文字大小",
                //     trigger: "blur",
                //   },
                // ],
                // page_bottom_img: [
                //   {
                //     required: true,
                //     validator: checkBottomImg,
                //   },
                // ],
                // view_more_wine_img: [
                //   {
                //     required: true,
                //     validator: checkMoreImg,
                //   },
                // ],
                // share_button_img: [
                //   {
                //     required: true,
                //     validator: checkShareBtnImg,
                //   },
                // ],
            },
            addActivitiesDataRules: {
                activity_type: [
                    {
                        required: true,
                        message: "请选择活动类型",
                        trigger: "blur"
                    }
                ],
                activity_name: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur"
                    }
                ],
                sub_activity_name: [
                    {
                        required: true,
                        message: "请输入活动副标题",
                        trigger: "blur"
                    }
                ],
                image: [
                    {
                        required: true,
                        validator: checkImage
                    }
                ],
                title_map: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ],
                // list_back: [
                //     {
                //         required: false,
                //         validator: checkListBack,
                //     },
                // ],
                activity_url: [
                    {
                        required: true,
                        message: "请输入活动链接",
                        trigger: "blur"
                    }
                ],
                share_config: [
                    {
                        required: true,
                        validator: checkShareConfig
                    }
                ],
                share_image: [
                    {
                        required: true,
                        validator: checkShareImg
                    }
                ],
                main_title: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请填写主标题"
                    }
                ],
                sub_title: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请填写副标题"
                    }
                ],
                share_url: [
                    {
                        required: true,
                        message: "请填写分享链接",
                        trigger: "blur"
                    }
                ],
                label_name: [
                    {
                        required: true,
                        message: "请填写所属板块",
                        trigger: "blur"
                    }
                ],
                start_at: [
                    {
                        required: true,
                        message: "请选择开始时间",
                        trigger: "blur"
                    }
                ],
                end_at: [
                    {
                        required: true,
                        message: "请选择结束时间",
                        trigger: "blur"
                    }
                ],
                sort: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "change"
                    }
                ],
                status: [
                    {
                        required: true,
                        message: "请选择活动状态",
                        trigger: "change"
                    }
                ],
                labelTitleMap: [
                    { required: true, message: "请上传板块题图边框" }
                ]
            }
        };
    },

    mounted() {},

    methods: {
        delType(key, id) {
            let tips = id
                ? "此操作将删除此板块下的所有商品, 是否继续?"
                : "此操作将删除此板块, 是否继续";
            this.$confirm(tips, "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    if (id) {
                        this.$request.activities
                            .deleteActivitiesLabel({
                                id: id
                            })
                            .then(res => {
                                if (res.data.error_code == 0) {
                                    this.addActivitiesData.label.splice(key, 1);
                                    this.$message({
                                        type: "success",
                                        message: "删除成功!"
                                    });
                                }
                            });
                    } else {
                        this.addActivitiesData.label.splice(key, 1);
                        this.$message({
                            type: "success",
                            message: "删除成功!"
                        });
                    }
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除"
                    });
                });
        },
        addType() {
            this.addActivitiesData.label.push({
                label_name: "",
                key: new Date().getTime(),
                labelTitleMapFileList: []
            });
        },
        dataEcho(row) {
            if (row.id != undefined) {
                this.$request.activities
                    .getActivityDetails({ id: row.id })
                    .then(res => {
                        for (const iterator in res.data.data) {
                            for (const iterator1 in this.addActivitiesData) {
                                if (iterator === iterator1) {
                                    this.addActivitiesData[iterator1] =
                                        res.data.data[iterator];
                                }
                            }
                        }
                        this.addActivitiesData.page_config.source_platform =
                            "vinehoo";
                        if (this.addActivitiesData.image != "") {
                            this.image = res.data.data.image.split(",");
                        }
                        if (this.addActivitiesData.title_map != "") {
                            this.title_map = res.data.data.title_map.split(",");
                        }
                        if (this.addActivitiesData.list_back != "") {
                            this.list_back = res.data.data.list_back.split(",");
                        }
                        if (this.addActivitiesData.share_image != "") {
                            this.share_image = res.data.data.share_image.split(
                                ","
                            );
                        }
                        if (this.addActivitiesData.page_config.head_img != "") {
                            this.head_img = res.data.data.page_config.head_img.split(
                                ","
                            );
                        }
                        if (
                            this.addActivitiesData.page_config.login_bg_img !=
                            ""
                        ) {
                            this.login_bg_img = res.data.data.page_config.login_bg_img.split(
                                ","
                            );
                        }
                        if (
                            this.addActivitiesData.page_config
                                .login_box_bg_img != ""
                        ) {
                            this.login_box_bg_img = res.data.data.page_config.login_box_bg_img.split(
                                ","
                            );
                        }
                        if (
                            this.addActivitiesData.page_config
                                .unchecked_protocol_img != ""
                        ) {
                            this.unchecked_protocol_img = res.data.data.page_config.unchecked_protocol_img.split(
                                ","
                            );
                        }
                        if (
                            this.addActivitiesData.page_config
                                .checked_protocol_img != ""
                        ) {
                            this.checked_protocol_img = res.data.data.page_config.checked_protocol_img.split(
                                ","
                            );
                        }
                        if (
                            this.addActivitiesData.page_config
                                .page_bottom_img != ""
                        ) {
                            this.page_bottom_img = res.data.data.page_config.page_bottom_img.split(
                                ","
                            );
                        }
                        if (
                            this.addActivitiesData.page_config
                                .view_more_wine_img != ""
                        ) {
                            this.view_more_wine_img = res.data.data.page_config.view_more_wine_img.split(
                                ","
                            );
                        }
                        if (
                            this.addActivitiesData.page_config
                                .share_button_img != ""
                        ) {
                            this.share_button_img = res.data.data.page_config.share_button_img.split(
                                ","
                            );
                        }
                        if (res.data.data.lables.length > 0) {
                            this.addActivitiesData.label = res.data.data.lables.map(
                                item => {
                                    return {
                                        id: item.id,
                                        label_name: item.label_name,
                                        key: new Date().getTime(),
                                        labelTitleMapFileList: item.title_map
                                            ? [item.title_map]
                                            : []
                                    };
                                }
                            );
                        }
                        this.addActivitiesData.start_at =
                            res.data.data.start_at * 1000;
                        this.addActivitiesData.end_at =
                            res.data.data.end_at * 1000;
                    });

                this.addIsEdit = true;
            }
        },
        confirmAddActivities() {
            this.$refs.addActivitiesRef.validate(valid => {
                if (valid) {
                    let originData = JSON.parse(
                        JSON.stringify(this.addActivitiesData)
                    );
                    originData.title_map = this.title_map.join(",");
                    originData.image = this.image.join(",");
                    originData.list_back = this.list_back.join(",");
                    originData.share_image = this.share_image.join(",");
                    originData.page_config.head_img = this.head_img.join(",");

                    originData.page_config.login_bg_img = this.login_bg_img.join(
                        ","
                    );
                    originData.page_config.login_box_bg_img = this.login_box_bg_img.join(
                        ","
                    );
                    originData.page_config.unchecked_protocol_img = this.unchecked_protocol_img.join(
                        ","
                    );
                    originData.page_config.checked_protocol_img = this.checked_protocol_img.join(
                        ","
                    );
                    originData.page_config.page_bottom_img = this.page_bottom_img.join(
                        ","
                    );
                    originData.page_config.view_more_wine_img = this.view_more_wine_img.join(
                        ","
                    );
                    originData.page_config.share_button_img = this.share_button_img.join(
                        ","
                    );

                    originData.label = originData.label.map(item => {
                        if (item.id) {
                            return {
                                label_name: item.label_name,
                                id: item.id,
                                title_map: item.labelTitleMapFileList.join(",")
                            };
                        } else {
                            return {
                                label_name: item.label_name,
                                title_map: item.labelTitleMapFileList.join(",")
                            };
                        }
                    });
                    originData.start_at = originData.start_at / 1000;
                    originData.end_at = originData.end_at / 1000;
                    let submitPath = this.addIsEdit
                        ? "editActivities"
                        : "addActivities";
                    this.$request.activities[submitPath](originData).then(
                        result => {
                            if (result.data.error_code == 0) {
                                this.$message({
                                    type: "success",
                                    message: "操作成功"
                                });
                                this.addIsEdit = false;
                                this.$emit("closeDia");
                            }
                        }
                    );
                }
            });
        },
        onLabelTitleMapSuccess(index) {
            console.log(this.addActivitiesData.label);
            this.$refs.addActivitiesRef.validateField(
                `label[${index}].labelTitleMapFileList`
            );
        }
    }
};
</script>
<style lang="scss">
.share_config {
    padding-top: 20px;
    width: 70%;
    border: 1px solid rgba(0, 0, 0, 0.4);
}
.el-form-item__error {
    min-width: 100px;
}
</style>
