<template>
  <div>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="活动列表" name="first">
        <activities v-if="activeName == 'first'"></activities>
      </el-tab-pane>
      <el-tab-pane label="商品列表" name="second">
        <goods v-if="activeName == 'second'"></goods>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import activities from "./activities.vue";
import goods from "./goods.vue";
export default {
  name: "MarketingIndex",
  components: { activities, goods },
  data() {
    return {
      activeName: "first",
    };
  },

  mounted() {},

  methods: {},
};
</script>