<template>
  <div>
    <el-card shadow="hover" :body-style="{ padding: '20px' }">
      <el-form
        :model="goodsQueryform"
        ref="goodsQueryformRef"
        :rules="queryGoodsDataRules"
        label-width="80px"
        :inline="true"
        size="mini"
      >
        <el-form-item prop="periods">
          <el-input
            v-model.number="goodsQueryform.periods"
            placeholder="请输入期数"
            @keyup.enter.native="queryGoodsWithParam"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="goodsQueryform.is_onsale"
            placeholder="是否在售"
            clearable
          >
            <el-option label="待上架" value="0"> </el-option>
            <el-option label="待售中" value="1"> </el-option>
            <el-option label="在售中" value="2"> </el-option>
            <el-option label="已下架" value="3"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="goodsQueryform.status"
            placeholder="是否隐藏"
            clearable
          >
            <el-option label="隐藏" value="1"> </el-option>
            <el-option label="显示" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="goodsQueryform.sort"
            placeholder="排序值顺序"
            clearable
          >
            <el-option label="排序值正序" value="1"> </el-option>
            <el-option label="排序值倒序" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="goodsQueryform.activity_id"
            placeholder="请选择活动"
            clearable
            remote
            filterable
            :remote-method="queryActivitiesList"
            @change="changeActivity"
          >
            <el-option
              v-for="(i, k) in activityList"
              :key="k"
              :label="i.activity_name"
              :value="i.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="goodsQueryform.label_id"
            placeholder="请选择活动板块"
            clearable
            @change="queryGoodsWithParam"
          >
            <el-option
              v-for="(i, k) in labelOptions"
              :key="k"
              :label="i.label_name"
              :value="i.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="queryGoodsWithParam"
            >查询</el-button
          >
          <el-button type="success" @click="updateGoods()">新增商品</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card
      shadow="hover"
      :body-style="{ padding: '20px' }"
      style="margin-top: 10px"
    >
      <el-table
        :data="goodsData"
        border
        size="small"
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column label="期数" prop="periods" width="80">
        </el-table-column>
        <el-table-column label="商品名称" prop="goods_name"> </el-table-column>
        <el-table-column label="图片" prop="images" width="80">
          <template slot-scope="scope">
            <el-popover placement="top-start" trigger="hover" size="mini">
              <!--trigger属性值：hover、click、focus 和 manual-->
              <a
                :href="scope.row.images"
                target="_blank"
                title="查看最大化图片"
              >
                <img
                  :src="scope.row.images"
                  style="width: 200px; height: 200px"
                />
              </a>
              <img
                slot="reference"
                :src="scope.row.images"
                style="width: 50px; height: 50px; cursor: pointer"
              />
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="单价" prop="unit_price" width="90">
        </el-table-column>
        <el-table-column label="市场价" prop="market_price" width="90">
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template slot-scope="scope">
            {{ GoodsStatus[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="100">
        </el-table-column>
        <el-table-column label="创建人" prop="created_name" width="100">
        </el-table-column>
        <el-table-column label="开售时间" prop="puton_time" width="180">
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="updateGoods(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" size="mini" @click="copyGoods(scope.row)"
              >复制</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <div style="text-align: center">
      <el-pagination
        background
        style="margin-top: 10px"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        :page-size="goodsQueryform.limit"
        :current-page="goodsQueryform.page"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :title="isEdit ? '编辑商品' : '新增商品'"
      :visible.sync="addGoodsVisible"
      width="70%"
      @close="closeGoods"
    >
      <add-good
        @closeGoods="closeGoods"
        v-if="addGoodsVisible"
        ref="addGoodRef"
      ></add-good>
      <span slot="footer" style="display: flex; justify-content: center">
        <el-button @click="addGoodsVisible = false">取消</el-button>
        <el-button type="primary" @click="comfirmAddGoods">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="复制商品"
      :visible.sync="copyVisible"
      width="30%"
      @close="closeCopyGoods"
     
    >
      <div style="display: flex"  v-if="copyVisible">
        <div>
          <el-select
            v-model="copyparams.activity_id"
            placeholder="请选择活动"
            clearable
            remote
            filterable
            @change="changeCopyActivity"
          >
            <el-option
              v-for="(i, k) in copyActivitiesList"
              :key="k"
              :label="i.activity_name"
              :value="i.id"
            >
            </el-option>
          </el-select>
        </div>
        <div style="margin-left: 10px">
          <el-select
            v-model="copyparams.label_id"
            placeholder="请选择所属板块"
            clearable
          >
            <el-option
              v-for="(i, k) in copyLabelOptions"
              :key="k"
              :label="i.label_name"
              :value="i.id"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeCopyGoods">取 消</el-button>
        <el-button type="primary" @click="submitCopyClick"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import AddGood from "./AddGood.vue";
import moment from "moment";
export default {
  components: {
    AddGood,
  },
  data() {
    const checkid = (rues, value, callback) => {
      if (value != "") {
        if (/^[0-9]*$/.test(value)) {
          callback();
        } else {
          callback(new Error("请输入正确的期数"));
        }
      } else {
        callback();
      }
    };

    return {
      isEdit: false,
      copyparams: {
        id: "",
        activity_id: "",
        label_id: "",
      },
      goodsQueryform: {
        periods: "",
        status: "",
        activity_id: "",
        is_onsale: "",
        sort: "",
        label_id: "",
        limit: 10,
        page: 1,
      },
      activityList: [],
      copyActivityList: [],
      queryGoodsDataRules: {
        periods: [
          {
            required: true,
            trigger: "change",
            validator: checkid,
          },
        ],
      },
      goodsData: [],
      GoodsStatus: {
        1: "隐藏",
        2: "显示",
      },
      addGoodsVisible: false,
      copyVisible: false,
      total: 0,
      labelOptions: [],
      copyLabelOptions: [],
    };
  },
  filters: {
    timeFormat(val) {
      if (val) {
        return moment(val).format("YYYY-MM-DD HH:mm:ss");
      }
    },
  },
  mounted() {
    this.queryActivitiesList("");
    this.queryCopyActivitiesList("");
    this.queryGoods();
  },

  methods: {
    queryGoodsWithParam() {
      this.goodsQueryform.page = 1;
      this.queryGoods();
    },
    handleSizeChange(limit) {
      this.goodsQueryform.limit = limit;
      this.goodsQueryform.page = 1;
      this.queryGoods();
    },
    handleCurrentChange(page) {
      this.goodsQueryform.page = page;
      this.queryGoods();
    },
    queryActivitiesList(query) {
      let queryData = {
        name: query,
        page: 1,
        limit: 9999,
      };
      this.$request.activities.getActivitiesList(queryData).then((result) => {
        if (result.data.error_code == 0) {
          this.activityList = result.data.data.list;
        }
      });
    },
    queryCopyActivitiesList(query) {
      let queryData = {
        name: query,
        page: 1,
        limit: 9999,
        activity_type: 2,
      };
      this.$request.activities.getActivitiesList(queryData).then((result) => {
        if (result.data.error_code == 0) {
          this.copyActivitiesList = result.data.data.list;
          console.log(this.copyActivitiesList);
        }
      });
    },
    submitCopyClick() {
      console.log(this.copyparams);
      const data = {
        id:Number(this.copyparams.id),
        activity_id:Number(this.copyparams.activity_id),
        label_id:Number(this.copyparams.label_id),
      };
      this.$request.activities.copyGoodsForActivity(data).then((result) => {
        if (result.data.error_code == 0) {
          this.$message({
                type: "success",
                message: "操作成功",
              });
          this.closeCopyGoods();
          this.queryGoods();
        }
      });
      
    },
   
    changeActivity(id) {
      this.goodsQueryform.label_id = "";
      this.labelOptions = [];
      if (id) {
        this.$request.activities.getActivityDetails({ id: id }).then((res) => {
          this.labelOptions = res.data.data.lables;
        });
      }
    },
    queryGoods() {
      this.$refs.goodsQueryformRef.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.goodsQueryform));
          this.$request.activities.getGoodsList(data).then((result) => {
            this.goodsData = result.data.data.list;
            this.total = result.data.data.total;
          });
        }
      });
    },
    updateGoods(row) {
      if (row != undefined) {
        this.isEdit = true;
      }
      this.addGoodsVisible = true;
      this.$nextTick(() => {
        this.$refs["addGoodRef"].dataEcho(row);
      });
    },
    copyGoods(row) {
      this.copyVisible = true;
      this.copyparams.id = row.id;
    },
    changeCopyActivity(id) {
      
      this.copyLabelOptions = [];
      for (const index in this.copyActivitiesList) {
        if(this.copyActivitiesList[index].id === id) {
          this.copyLabelOptions = this.copyActivitiesList[index].lables;
          this.copyparams.label_id =this.copyLabelOptions[0].id;
          break;
        }
      }
    },
    closeCopyGoods() {
        this.copyVisible = false;
        this.copyparams.activity_id = "";
        this.copyparams.label_id = "";
    },
    closeGoods() {
      this.addGoodsVisible = false;
      this.isEdit = false;
      this.queryGoods();
    },
    comfirmAddGoods() {
      this.$refs.addGoodRef.comfirmAddGoods();
    },
  },
};
</script>
<style lang="scss" scoped></style>
