<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item
                label="标题"
                :label-width="formLabelWidth"
                prop="title"
            >
                <el-input
                    size="mini"
                    v-model="form.title"
                    placeholder="请输入标题"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="频道"
                :label-width="formLabelWidth"
                prop="channel"
            >
                <el-checkbox-group v-model="form.channel">
                    <!-- <el-checkbox label="0">首页</el-checkbox> -->
                    <el-checkbox label="1">闪购</el-checkbox>
                    <el-checkbox label="2">秒发</el-checkbox>
                    <el-checkbox label="3">社区</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item
                label="广告类型"
                :label-width="formLabelWidth"
                prop="modul"
            >
                <el-radio-group :value="form.modul" @input="adTypeChange">
                    <el-radio label="1">酒会</el-radio>
                    <el-radio label="2">直播</el-radio>
                    <el-radio label="3">专题活动</el-radio>
                    <el-radio label="4">商品</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                label="客户端"
                :label-width="formLabelWidth"
                prop="client"
            >
                <el-checkbox-group v-model="form.client">
                    <el-checkbox ref="client" label="0">ios</el-checkbox>
                    <el-checkbox ref="client" label="1">安卓</el-checkbox>
                    <el-checkbox ref="client" label="2">小程序</el-checkbox>
                    <el-checkbox ref="client" label="3">h5</el-checkbox>
                    <el-checkbox ref="client" label="4">PC</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item
                label="数据ID"
                :label-width="formLabelWidth"
                prop="modul_id"
            >
                <el-input
                    size="mini"
                    v-model="form.modul_id"
                    placeholder="请输入数据ID"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="图片"
                :label-width="formLabelWidth"
                prop="image"
                v-if="form.modul"
            >
                <!-- v-if="showImg" -->
                <vos-oss
                    v-if="form.modul == 1 || form.modul == 2"
                    key="1"
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                    :limitWhList="MAdTypeOssImgWhLimit[form.modul]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
                <vos-oss
                    v-else-if="form.modul == 4"
                    key="2"
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                    :limitWhList="MAdTypeOssImgWhLimit[form.modul]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
                <vos-oss
                    v-else
                    key="3"
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                    :limitWhList="MAdTypeOssImgWhLimit[form.modul]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>

            <el-form-item
                label="排序"
                :label-width="formLabelWidth"
                prop="sort"
            >
                <el-input-number
                    size="mini"
                    v-model="form.sort"
                    controls-position="right"
                    @change="handleChange"
                    :min="0"
                ></el-input-number>
            </el-form-item>
            <!-- <PathConfig :path_id="form.path_id" ref="pathConfig"></PathConfig> -->
            <el-form-item
                label="上架时间"
                :label-width="formLabelWidth"
                prop="start_time"
            >
                <el-date-picker
                    v-model="form.start_time"
                    type="datetime"
                    placeholder="请选择上架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="下架时间"
                :label-width="formLabelWidth"
                prop="end_time"
            >
                <el-date-picker
                    v-model="form.end_time"
                    type="datetime"
                    placeholder="请选择下架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import PathConfig from "../../components/pathConfig/pathConfig.vue";
import { MAdTypeOssImgWhLimit } from "@/mapper/advertising/mapper";
export default {
    components: {
        VosOss,
        PathConfig
    },
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的数字"));
            } else {
                callback();
            }
        };
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        return {
            MAdTypeOssImgWhLimit,
            dialogFormVisible: false,
            // showImg: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            form: {
                channel: [],
                client: [],
                modul: "",
                modul_id: "",
                title: "",
                path_id: " ",
                image: "",
                sort: 1,
                start_time: "",
                end_time: ""
            },
            pathOptions: [],
            formRules: {
                start_time: [
                    {
                        required: true,
                        message: "请选择上架时间",
                        trigger: "blur"
                    }
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择下架时间",
                        trigger: "blur"
                    }
                ],
                modul_id: [
                    {
                        required: true,
                        message: "请输入数据ID",
                        trigger: "blur"
                    }
                ],
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur"
                    }
                ],
                modul: [
                    {
                        required: true,
                        message: "请选择广告位",
                        trigger: "blur"
                    }
                ],
                client: [
                    {
                        required: true,
                        message: "请选择客户端",
                        trigger: "blur"
                    }
                ],
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                path_id: [
                    {
                        required: true,
                        message: "请选择路径",
                        trigger: "blur"
                    }
                ],
                image: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ],
                sort: [
                    {
                        required: true,
                        validator: weightValidator,
                        trigger: "blur"
                    }
                ]
            },
            formLabelWidth: "150px",
            pageAttr: {
                page: 1,
                limit: 10
            }
        };
    },
    mounted() {},
    methods: {
        closeDiog() {
            this.$emit("close");
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        },
        adTypeChange(value) {
            if (!([1, 2].includes(this.form.modul) && [1, 2].includes(value))) {
                this.icon_map = [];
            }
            this.form.modul = value;
        },
        //表单提交，在父组件调用
        submitForm(ruleForm) {
            this.$refs[ruleForm].validate(valid => {
                if (valid) {
                    let data = {
                        title: this.form.title,
                        client: this.form.client,
                        channel: this.form.channel,
                        modul: this.form.modul,
                        modul_id: this.form.modul_id,
                        image: this.icon_map.join(","),
                        sort: this.form.sort,
                        path: 0,
                        params: [],
                        type: 5,
                        start_time: this.form.start_time,
                        end_time: this.form.end_time
                    };
                    console.log("表单", data);

                    this.$request.adBanner.adBanner(data).then(res => {
                        console.log("返回的值", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("添加成功");
                            this.$emit("close");
                            this.$emit("getAdvertising");
                        }
                    });
                } else {
                    // this.$Message.error(new Error());
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
