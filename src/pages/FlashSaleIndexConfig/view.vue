<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item
                label="标签类型"
                :label-width="formLabelWidth"
                prop="type"
            >
            <el-radio-group
                        :value="form.type"
                        @input="changeChannel"
                    >
                    <el-radio v-model="form.type" :label="0">频道</el-radio>
                    <el-radio v-model="form.type" :label="1">活动</el-radio>
                    <el-radio v-model="form.type" :label="2">栏目</el-radio>
                    </el-radio-group>
              
            </el-form-item>
            <div v-show="form.type == 0">
                <el-form-item
                    label="频道"
                    :label-width="formLabelWidth"
                    prop="channel"
                >
                    <el-radio v-model="form.channel" :label="1">闪购</el-radio>
                    <el-radio v-model="form.channel" :label="2">秒发</el-radio>
                    <el-radio v-model="form.channel" :label="3">跨境</el-radio>
                    <el-radio v-model="form.channel" :label="4">尾货</el-radio>
                </el-form-item>
            </div>
            <div v-show="form.type == 1">
                <el-form-item
                    label="活动"
                    :label-width="formLabelWidth"
                    prop="type"
                >
                    <el-select
                        v-model="form.activity_name"
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入活动关键词"
                        :remote-method="remoteMethod"
                        :loading="loading"
                    >
                        <el-option
                            v-for="item in options"
                            :key="item.id"
                            :label="item.activity_name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="标签icon"
                    :label-width="formLabelWidth"
                    prop="icon"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                        v-if="form.type == 1"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
            </div>

            <div v-show="form.type == 2">
                <!-- <el-form-item
                    label="栏目"
                    :label-width="formLabelWidth"
                    prop="type"
                >
                    <el-select
                        v-model="chooseColumn"
                        filterable
                        placeholder="请选择栏目"
                        multiple
                    >
                        <el-option
                            v-for="item in cloumOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item
                    label="标签icon"
                    :label-width="formLabelWidth"
                    prop="icon"
                >
                    <vos-oss
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="icon_map"
                        v-if="form.type == 2"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>
            </div>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss
    },
    props: ["rowData"],
    data() {
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if(this.form.type==1) {
                if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
            } else {
                if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
            }
           
        };
        return {
            dialogFormVisible: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            form: {
                icon: "",
                type: "0",
                channel: "",
                activity_id: "",
                name: "",
                activity_name: ""
            },

            formRules: {
                type: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "blur"
                    }
                ],
                channel: [
                    {
                        required: true,
                        message: "请选择频道",
                        trigger: "blur"
                    }
                ],
                activity_name: [
                    {
                        required: true,
                        message: "请输入活动关键词",
                        trigger: "blur"
                    }
                ],
                icon: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ]
            },
            formLabelWidth: "150px",
            options: [],
            cloumOptions:[],
            chooseColumn:[],
            list: [],
            loading: false,
            activityList: []
        };
    },
    mounted() {
        this.getColumnManage();
        console.log("编辑数据", this.rowData);
        this.form = { ...this.form, ...this.rowData };
        console.log(this.form);
        if (this.form.type == 1) {
            this.form.activity_name = this.form.name;
            if (this.form.icon != "") {
            this.icon_map = this.form.icon.split(",");
        }
        } else if(this.form.type == 2){
            this.chooseColumn = this.form.activity_id.split(",").map(Number);
            if (this.form.icon != "") {
            this.icon_map = this.form.icon.split(",");
        }
        }
        
    },
    methods: {
        //搜索活动列表
        remoteMethod(value) {
            console.log(value);
            if (value !== "") {
                this.loading = true;
                let data = {
                    activity_name: value,
                    page: 1,
                    limit: 10
                };
                console.log("活动列表参数", data);
                this.$request.FlashSaleIndexConfig.getActivitiesList(data).then(
                    res => {
                        console.log("活动列表", res);
                        if (res.data.error_code == 0) {
                            this.loading = false;
                            this.options = res.data.data.list.filter(item => {
                                return (
                                    item.activity_name
                                        .toLowerCase()
                                        .indexOf(value.toLowerCase()) > -1
                                );
                            });
                        }
                    }
                );
            } else {
                this.options = [];
            }
        },
        //活动列表
        async getActivitiesList() {
            let data = {
                page: 1,
                limit: 10
            };
            let res = await this.$request.FlashSaleIndexConfig.getActivitiesList(
                data
            );
            console.log("活动列表", res);
            if (res.data.error_code == 0) {
                this.activityList = res.data.data.list;
            }
        },
          //栏目管理列表
          async getColumnManage() {
            let res = await this.$request.KingArea.getColumnManagerList({
                channel: 20,
                // page_area: this.page_area,
                page: 1,
                status: 1,
                limit: 9999
            });
            console.log("列表", res);
            if (res.data.error_code == 0) {
                this.cloumOptions = res.data.data.list;
            }
        },
        changeChannel(value) {
            this.icon_map = [];
            this.$nextTick(() => {
              this.icon_map = [];
              this.form.activity_id = "";
              this.form.activity_name = "";
              this.form.icon = "";
            });
            this.form.type = value;
        },
        closeDiog() {
            this.$emit("closeViewDialogStatus");
            this.$emit("getFlashSaleIndexList");
        },
        //表单提交，在父组件调用
        submitForm() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    let data = {};
                    switch (this.form.type) {
                        case 0:
                            let channelName = "";
                            if (this.form.channel == 1) {
                                channelName = "闪购";
                            } else if (this.form.channel == 2) {
                                channelName = "秒发";
                            } else if (this.form.channel == 3) {
                                channelName = "跨境";
                            } else if (this.form.channel == 4) {
                                channelName = "尾货";
                            }
                            data = {
                                id: this.form.id,
                                type: this.form.type,
                                channel: this.form.channel,
                                // activity_id: this.form.activity_id,
                                // icon: this.icon_map.join(","),
                                name: channelName
                            };
                            break;
                        case 1:
                            if (this.options.length != 0) {
                                data = {
                                    id: this.form.id,
                                    type: this.form.type,
                                    // channel: this.form.channel,
                                    activity_id: this.form.activity_name,
                                    icon: this.icon_map.join(","),
                                    name: this.options[0].activity_name
                                };
                            } else {
                                data = {
                                    id: this.form.id,
                                    type: this.form.type,
                                    // channel: this.form.channel,
                                    activity_id: this.form.activity_id,
                                    icon: this.icon_map.join(","),
                                    name: this.form.activity_name
                                };
                            }
                            break;
                            case 2:
                            data = {
                                    id: this.form.id,
                                    type: this.form.type,
                                    //activity_id:this.chooseColumn.join(","),
                                    icon: this.icon_map.join(","),
                                };
                            break;
                    }

                    console.log("表单", data);

                    this.$request.FlashSaleIndexConfig.updateFlashSale(
                        data
                    ).then(res => {
                        console.log("返回的值", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("编辑成功");
                            this.closeDiog();
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
