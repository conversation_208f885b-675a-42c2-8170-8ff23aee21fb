<template>
    <div class="order-layout">
        <!-- <div class="order-form">
            <el-card>
                <el-input
                    v-model="title"
                    placeholder="请输入标题"
                    size="mini"
                    style="width:200px;margin-right:20px"
                ></el-input>
                <el-select
                    class="m-r-10 w-mini"
                    v-model="status"
                    filterable
                    size="mini"
                    placeholder="状态"
                    clearable
                >
                    <el-option
                        v-for="(item, index) in objectOptions"
                        :key="index"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-button type="primary" size="mini" @click="search()"
                    >查询</el-button
                >
                <el-button
                    type="success"
                    size="mini"
                    @click="dialogStatus = true"
                    >添加banner广告</el-button
                >
            </el-card>
        </div> -->
        <div class="table" v-if="tableData.length">
            <el-card class="card" shadow="hover">
                <el-table
                    border
                    size="mini"
                    :data="tableData"
                    style="width: 100%"
                >
                    <el-table-column
                        align="center"
                        label="标签类型"
                        prop="type"
                        width="100"
                    >
                        <template #default="type">
                            <span>{{
                                `${type.row.type == 0 ? "频道" : "栏目"}`
                            }}</span>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column
                        align="center"
                        label="图标"
                        prop="icon"
                        min-width="110"
                    >
                        <template #default="icon">
                            <el-image
                                style="width: 100px; height: 100px"
                                :src="icon.row.icon"
                                fit="cover"
                            ></el-image>
                        </template>
                    </el-table-column> -->
                    <el-table-column
                        align="center"
                        label="频道/活动名称"
                        prop="name"
                        min-width="150"
                        show-overflow-tooltip
                    >
                        <!-- <template #default="channel">
                            <span
                                v-for="item in channel.row.channel"
                                :key="item.id"
                                >{{ item.name }}、</span
                            >
                        </template> -->
                    </el-table-column>

                    <el-table-column
                        align="center"
                        label="排序"
                        width="100"
                        prop="sort"
                    >
                        <template slot-scope="{ row }">
                            <!-- <el-input
                                class="sort_input"
                                size="mini"
                                v-model="row.sort"
                                oninput="value=value.replace(/[^0-9]/g,'')"
                                @change="updateFlashSaleSort(row)"
                            /> -->
                            <!-- oninput="value=value.replace(/[^0-9]/g,'')" -->
                            <input
                                class="sort_input"
                                type="text"
                                v-model="row.sort"
                                oninput="value=value.replace(/[^0-9]/g,'')"
                                @change="updateFlashSaleSort(row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="operator"
                        align="center"
                        label="最新编辑人"
                        width="120"
                    >
                    </el-table-column>

                    <el-table-column
                        align="center"
                        label="最新编辑时间"
                        min-width="160"
                        prop="update_time"
                    >
                        <!-- <template #default="row">
                            <div>创建 : {{ row.row.created_at }}</div>
                            <div>上架 : {{ row.row.start_time }}</div>
                            <div>下架 : {{ row.row.end_time }}</div>
                        </template> -->
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        label="操作"
                        fixed="right"
                        width="120"
                        align="center"
                    >
                        <template slot-scope="row">
                            <el-button
                                @click="view(row.row)"
                                type="text"
                                size="mini"
                                >编辑</el-button
                            >
                            <el-popconfirm
                                confirm-button-text="确定"
                                cancel-button-text="取消"
                                title="确定改变状态吗？"
                                @confirm="
                                    updateEnable(
                                        row.row,
                                        row.row.status == 0 ? '1' : '0'
                                    )
                                "
                            >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    v-if="row.row.status == 0"
                                    type="text"
                                    style="margin-left:10px"
                                    >启用</el-button
                                >
                                <el-button
                                    slot="reference"
                                    size="mini"
                                    type="text"
                                    v-if="row.row.status == 1"
                                    style="margin-left:10px"
                                    >禁用</el-button
                                >
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>
        <el-empty v-else></el-empty>

        <!-- 编辑 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="编辑"
                :visible.sync="viewDialogStatus"
                width="40%"
                :before-close="closeViewDialogStatus"
            >
                <div style="max-height: 570px;overflow-y: auto;">
                    <Views
                        v-if="viewDialogStatus"
                        :rowData="rowData"
                        @closeViewDialogStatus="closeViewDialogStatus"
                        @getFlashSaleIndexList="getFlashSaleIndexList"
                    ></Views>
                </div>
            </el-dialog>
        </div>
        <!-- <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div> -->
    </div>
</template>
<script>
// import Add from "./add.vue";
import Views from "./view.vue";
export default {
    components: { Views },

    data() {
        return {
            rowData: {},
            tableData: [],
            objectOptions: [
                {
                    id: 1,
                    name: "开启"
                },
                {
                    id: 0,
                    name: "禁用"
                }
            ],
            title: "",
            status: "",
            dialogStatus: false,
            viewDialogStatus: false,
            pageAttr: {
                page: 1,
                limit: 10
            },
            total: 0,
            isEdit: false
        };
    },
    mounted() {
        this.getFlashSaleIndexList();
        // this.addFlashSaleIndex();
    },
    methods: {
        //闪购首页配置列表
        async getFlashSaleIndexList() {
            let res = await this.$request.FlashSaleIndexConfig.getFlashSaleIndexList();
            console.log("闪购首页配置列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
            }
        },
        //添加闪购首页配置
        // async addFlashSaleIndex() {
        //     let data = {
        //         type: 0,
        //         channel: 1,
        //         activity_id: 0,
        //         icon: "",
        //         name: "闪购"
        //     };
        //     let res = await this.$request.FlashSaleIndexConfig.addFlashSaleIndex(
        //         data
        //     );
        //     console.log(res);
        // },

        //更新排序
        async updateFlashSaleSort(row) {
            let data = {
                id: row.id,
                sort: row.sort
            };
            let res = await this.$request.FlashSaleIndexConfig.updateFlashSaleSort(
                data
            );
            console.log("更新排序", res);
            if (res.data.error_code == 0) {
                this.$Message.success("更新成功");
                this.getFlashSaleIndexList();
            }
        },
        //更改状态
        async updateEnable(row, status) {
            let data = {
                id: row.id,
                status: status
            };
            this.$request.FlashSaleIndexConfig.updateFlashSaleStatus(data).then(
                res => {
                    console.log(res);
                    if (res.data.error_code == 0) {
                        this.getFlashSaleIndexList();
                    }
                }
            );
        },
        //关闭编辑弹框
        closeViewDialogStatus() {
            this.viewDialogStatus = false;
            // this.getAdBannerList();
        },
        view(row) {
            this.viewDialogStatus = true;
            this.rowData = row;
            // let data = {
            //     id: row.id
            // };
            // this.$request.clientPath.EditPathDetail(data).then(res => {
            //     if (res.data.error_code == 0) {
            //         console.log("编辑res", res);
            //         this.rowData = res.data.data;
            //         console.log("88888888", this.rowData);
            //     }
            // });
            // this.isEdit = true;
        },
        close() {
            this.dialogStatus = false;
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            // this.getFlashSaleIndexList();
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            // this.getFlashSaleIndexList();
        }
    },

    filters: {}
};
</script>
<style scoped lang="scss">
.sort_input {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 30px;
    line-height: 30px;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    text-align: center;
}
// /deep/ .el-input__inner {
//     -webkit-appearance: none;
//     background-color: #fff;
//     background-image: none;
//     border-radius: 4px;
//     border: 1px solid #dcdfe6;
//     -webkit-box-sizing: border-box;
//     box-sizing: border-box;
//     color: #606266;
//     display: inline-block;
//     font-size: inherit;
//     height: 40px;
//     line-height: 40px;
//     outline: 0;
//     padding: 0 15px;
//     -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
//     transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
//     width: 100%;
//     text-align: center;
// }
/deep/ .el-dialog {
    position: relative;
    margin: 0 auto 50px;
    border-radius: 2px;
    box-shadow: 0 1px 3pxrgba (0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 50%;
    margin-top: 1% !important;
}
.order-layout {
    .pagination-block {
        text-align: center;
        display: flex;
        justify-content: center;
    }
    .level-list {
        display: flex;
        align-items: center;
        padding: 10px 0;
        /deep/ .radio {
            margin-bottom: 0;
        }
        .name {
            margin-right: 20px;
        }
    }
    .table {
        margin-top: 10px;
        .f-12 {
            font-size: 12px;
        }
        .card {
            margin-bottom: 8px;
            .card-title {
                display: flex;
                align-items: center;

                .m-l-8 {
                    margin-left: 10px;
                }
            }
        }

        .order-main {
            display: flex;
            & > div {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -moz-box;
                -moz-line-clamp: 1;
                -moz-box-orient: vertical;
                word-wrap: break-word;
                word-break: break-all;
                white-space: nowrap;
                min-width: 200px;
                margin-right: 10px;

                color: #333;

                & > div {
                    display: flex;
                }
                b {
                    line-height: 2;
                    opacity: 1;
                    display: inline-block;
                    font-weight: bold;
                }

                // width: 30;
            }
        }
    }
}
</style>
