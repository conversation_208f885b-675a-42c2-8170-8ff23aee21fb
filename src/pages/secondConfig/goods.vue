<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form label-width="80px" :inline="true" size="mini">
                <el-form-item prop="periods">
                    <el-input
                        v-model.number="goodsQueryform.periods"
                        placeholder="请输入期数"
                        @keyup.enter.native="queryGoodsWithParam"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="goodsQueryform.status"
                        placeholder="请选择状态"
                        clearable
                    >
                        <el-option label="隐藏" value="1"> </el-option>
                        <el-option label="显示" value="2"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="goodsQueryform.second_id"
                        placeholder="请选择分类"
                        filterable
                        clearable
                    >
                        <el-option
                            v-for="(i, k) in secondList"
                            :key="k"
                            :label="i.second_name"
                            :value="i.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryGoodsWithParam"
                        >查询</el-button
                    >
                    <el-button type="success" @click="updateGoods()"
                        >新增商品</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            shadow="hover"
            :body-style="{ padding: '20px' }"
            style="margin-top: 10px"
        >
            <el-table
                :data="goodsData"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="期数" prop="periods" width="80">
                </el-table-column>
                <el-table-column
                    label="商品名称"
                    prop="goods_name"
                    min-width="300"
                >
                </el-table-column>
                <el-table-column label="图片" prop="images" width="80">
                    <template slot-scope="scope">
                        <el-popover
                            placement="top-start"
                            trigger="hover"
                            size="mini"
                        >
                            <!--trigger属性值：hover、click、focus 和 manual-->
                            <a
                                :href="scope.row.images"
                                target="_blank"
                                title="查看最大化图片"
                            >
                                <img
                                    :src="scope.row.images"
                                    style="width: 200px; height: 200px"
                                />
                            </a>
                            <img
                                slot="reference"
                                :src="scope.row.images"
                                style="
                                    width: 50px;
                                    height: 50px;
                                    cursor: pointer;
                                "
                            />
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="单价" prop="unit_price" width="90">
                </el-table-column>
                <el-table-column label="市场价" prop="market_price" width="90">
                </el-table-column>
                <el-table-column label="状态" prop="status" width="100">
                    <template slot-scope="scope">
                        {{ GoodsStatus[scope.row.status] }}
                    </template>
                </el-table-column>
                <!-- <el-table-column label="排序" prop="sort" width="100">
                </el-table-column> -->
                <el-table-column label="创建人" prop="created_name" width="100">
                </el-table-column>
                <el-table-column label="开售时间" prop="sale_time" width="180">
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="200">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="updateGoods(scope.row)"
                            >编辑</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="goodsQueryform.limit"
                :current-page="goodsQueryform.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <el-dialog
            :close-on-click-modal="false"
            :title="isEdit ? '编辑商品' : '新增商品'"
            :visible.sync="addGoodsVisible"
            width="60%"
            @close="closeGoods"
        >
            <add-good
                @closeGoods="closeGoods"
                v-if="addGoodsVisible"
                ref="addGoodRef"
            ></add-good>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="addGoodsVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirmAddGoods"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import AddGood from "./AddGood.vue";
import moment from "moment";
export default {
    components: {
        AddGood
    },
    data() {
        return {
            isEdit: false,
            goodsQueryform: {
                periods: "",
                status: "",
                second_id: "",
                is_sale: "",
                limit: 10,
                page: 1
            },
            secondList: [],
            goodsData: [],
            GoodsStatus: {
                1: "隐藏",
                2: "显示"
            },
            addGoodsVisible: false,

            total: 0
        };
    },
    filters: {
        timeFormat(val) {
            if (val) {
                return moment(val).format("YYYY-MM-DD HH:mm:ss");
            }
        }
    },
    mounted() {
        this.querySecondsList("");
        this.queryGoods();
    },

    methods: {
        queryGoodsWithParam() {
            this.goodsQueryform.page = 1;
            this.queryGoods();
        },
        handleSizeChange(limit) {
            this.goodsQueryform.limit = limit;
            this.goodsQueryform.page = 1;
            this.queryGoods();
        },
        handleCurrentChange(page) {
            this.goodsQueryform.page = page;
            this.queryGoods();
        },
        querySecondsList(query) {
            let queryData = {
                name: query,
                page: 1,
                limit: 9999
            };
            this.$request.second.getSecondtypeList(queryData).then(result => {
                if (result.data.error_code == 0) {
                    this.secondList = result.data.data.list;
                }
            });
        },
        queryGoods() {
            this.$request.second
                .getSecondGoodsList(this.goodsQueryform)
                .then(result => {
                    this.goodsData = result.data.data.list;
                    this.total = result.data.data.total;
                });
        },
        updateGoods(row) {
            if (row != undefined) {
                this.isEdit = true;
            }
            this.addGoodsVisible = true;
            this.$nextTick(() => {
                this.$refs["addGoodRef"].dataEcho(row);
            });
        },
        closeGoods() {
            this.addGoodsVisible = false;
            this.isEdit = false;
            this.queryGoods();
        },
        comfirmAddGoods() {
            this.$refs.addGoodRef.comfirmAddGoods();
        }
    }
};
</script>
<style lang="scss" scoped></style>
