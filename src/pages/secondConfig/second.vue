<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form
                :model="secondtypeQueryform"
                ref="form"
                :rules="querySecondtypeDataRules"
                label-width="80px"
                :inline="true"
                @submit.native.prevent
                size="mini"
            >
                <el-form-item>
                    <el-input
                        v-model="secondtypeQueryform.second_name"
                        placeholder="分类名称"
                        @keyup.enter.native="querySecondtype"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="querySecondtype"
                        >查询</el-button
                    >
                    <!-- <el-button type="success" @click="updateSecondtype()"
            >新增分类</el-button
          > -->
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="secondtypeTableData"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="ID" prop="id" width="80">
                </el-table-column>
                <el-table-column label="分类名称" prop="second_name">
                </el-table-column>
                <el-table-column width="80">
                    <template slot-scope="scope">
                        <el-image :src="scope.row.image"></el-image>
                    </template>
                </el-table-column>
                <el-table-column label="商品数量" prop="goods_num" width="120">
                </el-table-column>
                <el-table-column label="排序" prop="sort" width="120">
                </el-table-column>
                <el-table-column
                    label="最近编辑人"
                    prop="created_name"
                    width="150"
                >
                </el-table-column>
                <el-table-column label="最新编辑时间" width="150">
                    <template slot-scope="scope">
                        {{ scope.row.update_at }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="180">
                    <!--   -->
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="updateSecondtype(scope.row)"
                            >编辑</el-button
                        >
                        <!-- 禁用/启用 -->
                        <el-button
                            type="text"
                            size="mini"
                            @click="changeStatus(scope.row)"
                            >{{
                                scope.row.status == 1 ? "禁用" : "启用"
                            }}</el-button
                        >
                        <!-- 删除 -->
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="secondtypeQueryform.limit"
                :current-page="secondtypeQueryform.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <el-dialog
            :close-on-click-modal="false"
            :title="isEdit ? '编辑分类' : '新增分类'"
            :visible.sync="addSecondtypeVisible"
            width="60%"
            @close="cloesSecondtype"
        >
            <add-secondtype
                v-if="addSecondtypeVisible"
                ref="addSecondtypeDia"
                @closeDia="addSecondtypeVisible = false"
            ></add-secondtype>
            <span slot="footer" style="display: flex; justify-content: center">
                <el-button @click="addSecondtypeVisible = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="comfirmAddSecondtype"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>
<script>
import AddSecondtype from "./Add.vue";
export default {
    components: {
        AddSecondtype
    },
    data() {
        return {
            image: [],
            total: 0,
            secondtypeQueryform: {
                page: 1,
                limit: 10,
                second_name: ""
            },
            secondtypeTableData: [],
            addSecondtypeData: {
                second_name: "",
                image: "",
                id: null
            },
            addSecondtypeVisible: false,
            isEdit: false,
            addSecondtypeDataRules: {
                second_name: [
                    {
                        required: true,
                        message: "请输入分类名称",
                        trigger: "blur"
                    }
                ]
            },
            querySecondtypeDataRules: {}
        };
    },

    mounted() {
        this.getSecondtypeList();
    },

    methods: {
        //获取分类列表'
        getSecondtypeList() {
            this.$request.second
                .getSecondtypeList(this.secondtypeQueryform)
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.secondtypeTableData = result.data.data.list;
                        this.total = result.data.data.total;
                    }
                });
        },
        changeStatus(params) {
            this.$request.second
                .updateSecondtypeStatus({
                    id: params.id,
                    status: params.status == 1 ? 0 : 1
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.$message.success("操作成功");
                        this.getSecondtypeList();
                    }
                });
        },
        // 查询分类
        querySecondtype() {
            this.secondtypeQueryform.page = 1;
            this.getSecondtypeList();
        },
        //打开更新分类对话框
        updateSecondtype(row) {
            this.addSecondtypeVisible = true;
            if (row != undefined) {
                this.isEdit = true;
                this.$nextTick(() => {
                    this.$refs.addSecondtypeDia.editSecond(row);
                });
            }
        },

        // 关闭分类对话框
        cloesSecondtype() {
            this.isEdit = false;
            this.getSecondtypeList();
        },
        // 确认添加分类
        comfirmAddSecondtype() {
            this.$refs.addSecondtypeDia.confirmAddSecondtype();
        },
        handleSizeChange(limit) {
            this.secondtypeQueryform.limit = limit;
            this.secondtypeQueryform.page = 1;
            this.getSecondtypeList();
        },
        handleCurrentChange(page) {
            this.secondtypeQueryform.page = page;
            this.getSecondtypeList();
        }
    }
};
</script>
