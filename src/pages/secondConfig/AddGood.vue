<template>
    <el-form
        ref="addGoodsRef"
        :inline="false"
        :rules="addGoodsDataRules"
        :model="addGoodsData"
        label-width="100px"
    >
        <el-form-item label="期数" prop="periods">
            <el-input
                v-model.number="addGoodsData.periods"
                placeholder="请输入期数"
                clearable
                style="width: 180px; margin-right: 20px"
            ></el-input>
            <el-button
                type="primary"
                @click="importGoodsData"
                :disabled="!addGoodsData.periods"
                >确定</el-button
            >
        </el-form-item>
        <el-form-item label="商品名称" prop="goods_name">
            <el-input
                style="width: 300px"
                v-model="addGoodsData.goods_name"
                placeholder="请输入商品名称"
                clearable
                disabled
            ></el-input>
        </el-form-item>
        <el-form-item label="图片" prop="images">
            <vos-oss
                list-type="picture-card"
                :showFileList="true"
                :limit="1"
                :dir="dir"
                :file-list="good_image"
                ref="goodsImageRef"
                :disabled="true"
            >
                <i slot="default" class="el-icon-plus"></i>
            </vos-oss>
            <span class="pic-tips">仅限正方形白底PNG 800*800</span>
        </el-form-item>
        <el-form-item label="单价" prop="unit_price">
            <el-input-number
                v-model="addGoodsData.unit_price"
                :min="0"
                :step="1"
                disabled
            />
            <span class="unit">元</span>
        </el-form-item>
        <el-form-item label="市场价" prop="market_price">
            <el-input-number
                v-model="addGoodsData.market_price"
                :min="0"
                :step="1"
                disabled
            />
            <span class="unit">元</span>
        </el-form-item>
        <el-form-item label="上架时间" prop="puton_time">
            <el-date-picker
                v-model="addGoodsData.puton_time"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
            >
            </el-date-picker>
        </el-form-item>
        <el-form-item label="开售时间" prop="sale_time">
            <el-date-picker
                v-model="addGoodsData.sale_time"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
            >
            </el-date-picker>
        </el-form-item>
        <el-form-item label="下架时间" prop="down_time">
            <el-date-picker
                v-model="addGoodsData.down_time"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
            >
            </el-date-picker>
        </el-form-item>
        <el-form-item label="关联分类" prop="second_id">
            <div style="display: flex">
                <div>
                    <el-select
                        v-model="addGoodsData.second_id"
                        placeholder="请选择分类"
                        clearable
                        remote
                        filterable
                    >
                        <el-option
                            v-for="(i, k) in secondList"
                            :key="k"
                            :label="i.second_name"
                            :value="i.id"
                        >
                        </el-option>
                    </el-select>
                </div>
                <!-- <div style="margin-left: 10px">
          <el-form-item size="normal" prop="label_id">
            <el-select
              v-model="addGoodsData.label_id"
              placeholder="请选择所属板块"
              clearable
            >
              <el-option
                v-for="(i, k) in secondLabelList"
                :key="k"
                :label="i.label_name"
                :value="i.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div> -->
            </div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
            <el-radio v-model="addGoodsData.status" :label="2">显示</el-radio>
            <el-radio v-model="addGoodsData.status" :label="1">隐藏</el-radio>
        </el-form-item>
        <!-- <el-form-item label="排序" prop="sort">
      <el-input-number v-model="addGoodsData.sort" :min="0" :step="1" />
    </el-form-item> -->
    </el-form>
</template>
<script>
import VosOss from "vos-oss";
export default {
    components: {
        VosOss,
    },
    data() {
        const checkFormId = (rues, value, callback) => {
            if (value != "") {
                if (/^[0-9]*$/.test(value)) {
                    callback();
                } else {
                    callback(new Error("请输入正确的期数"));
                }
            } else {
                callback(new Error("请输入期数"));
            }
        };
        const checkFormImage = (rues, value, callback) => {
            if (this.good_image.length == 0) {
                callback(new Error("请上传图片"));
            } else {
                callback();
            }
        };
        return {
            dir: "vinehoo/vos/marketing/",
            addGoodsData: {
                periods: null,
                goods_name: "",
                images: "",
                unit_price: 0,
                market_price: 0,
                second_id: "",
                status: 2,
                puton_time: "",
                sale_time: "",
                sort: 0,
                down_time: "",
            },
            addGoodsDataRules: {
                periods: [
                    {
                        required: true,
                        validator: checkFormId,
                    },
                ],
                goods_name: [
                    {
                        required: true,
                        trigger: "blur",
                        message: "请输入商品名称",
                    },
                ],
                images: [
                    {
                        validator: checkFormImage,
                        trigger: "change",
                        required: true,
                    },
                ],
                unit_price: [
                    {
                        required: false,
                    },
                ],
                market_price: [
                    {
                        required: false,
                    },
                ],
                second_id: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择关联分类",
                    },
                ],
                status: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择状态",
                    },
                ],
                puton_time: [
                    {
                        required: false,
                        trigger: "change",
                        message: "请选择上架时间",
                    },
                ],
                sort: [
                    {
                        required: false,
                    },
                ],
                label_id: [
                    {
                        required: true,
                        trigger: "change",
                        message: "请选择商品所属板块",
                    },
                ],
            },
            good_image: [],
            isEdit: false,
            secondList: [],
            originGoodsId: null,
            secondLabelList: [],
        };
    },

    mounted() {
        this.querySecondList();
    },

    methods: {
        querySecondList() {
            this.$request.second
                .getSecondtypeList({
                    second_name: "",
                    page: 1,
                    limit: 99999,
                })
                .then((result) => {
                    if (result.data.error_code == 0) {
                        this.secondList = result.data.data.list;
                    }
                });
        },
        dataEcho(row) {
            if (row != undefined) {
                this.originGoodsId = row.periods;
                this.isEdit = true;
                for (let i in this.addGoodsData) {
                    this.addGoodsData[i] = row[i];
                    if (i == "images") {
                        this.good_image = row.images.split(",");
                    }
                }
                this.addGoodsData.id = row.id;
            }
        },
        importGoodsData() {
            this.$request.second
                .getGoodsById({
                    id: this.addGoodsData.periods,
                })
                .then((result) => {
                    if (result.data.error_code == 0) {
                        this.originGoodsId = this.addGoodsData.periods;
                        let good_info = Object.assign({}, result.data.data);
                        this.addGoodsData.goods_name = good_info.title;
                        this.addGoodsData.unit_price = good_info.price;
                        this.addGoodsData.market_price = good_info.market_price;
                        this.addGoodsData.puton_time = good_info.onsale_time;
                        this.addGoodsData.sale_time = good_info.sell_time;
                        this.addGoodsData.down_time = good_info.sold_out_time;
                        this.good_image = good_info.product_img;
                        this.$refs.goodsImageRef.handleviewFileList(
                            good_info.product_img
                        );
                    }
                });
        },
        comfirmAddGoods() {
            this.$refs.addGoodsRef.validate((valid) => {
                if (valid) {
                    if (this.originGoodsId != this.addGoodsData.periods) {
                        this.$message.error(
                            "您输入的期数与已导入的商品期数不一致，请重新导入商品或修改期数"
                        );
                        return;
                    }
                    let queryData = {
                        periods: Number(this.addGoodsData.periods),
                        second_id: this.addGoodsData.second_id,
                        status: this.addGoodsData.status,
                        sort: this.addGoodsData.sort,
                    };
                    if (this.isEdit) {
                        queryData.id = this.addGoodsData.id;
                    }
                    let GoodsPath = this.isEdit ? "updateGoods" : "addGoods";
                    this.$request.second[GoodsPath](queryData).then(
                        (result) => {
                            if (result.data.error_code == 0) {
                                this.$message({
                                    type: "success",
                                    message: "操作成功",
                                });
                                this.addGoodsVisible = false;
                                this.$emit("closeGoods");
                                this.originGoodsId = null;
                            }
                        }
                    );
                }
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.unit {
    margin-left: 10px;
}
.pic-tips {
    font-size: 12px;
    color: #6b6b6b;
    line-height: 12px;
}
</style>