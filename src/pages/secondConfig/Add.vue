<template>
  <div>
    <el-form
      :model="queryData"
      ref="queryDataRef"
      :rules="rules"
      label-width="100px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="分类名称" prop="second_name">
        <el-input
          class="w-220"
          v-model="queryData.second_name"
          placeholder="最多四个中文"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="分类图片" prop="image">
        <vos-oss
          list-type="picture-card"
          :showFileList="true"
          :limit="1"
          :dir="dir"
          :file-list="file_list"
        >
          <i slot="default" class="el-icon-plus"></i>
        </vos-oss>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          :min="0"
          v-model="queryData.sort"
          clearable
        ></el-input-number>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import VosOss from "vos-oss";
export default {
  components: {
    VosOss,
  },
  data() {
    const check_pic = (rule, value, callback) => {
      if (this.file_list.length == 0) {
        callback(new Error("请上传图片"));
      } else {
        callback();
      }
    };
    return {
      queryData: {
        second_name: "",
        image: "",
        sort: 0,
      },
      file_list: [],
      dir: "vinehoo/vos/marketing/second",
      rules: {
        second_name: [
          { required: true, message: "请输入分类名称", trigger: "blur" },
          { min: 1, max: 4, message: "最多四个中文", trigger: "blur" },
        ],
        image: [
          {
            required: true,
            validator: check_pic,
            trigger: "change",
          },
        ],
        sort: [{ required: true, message: "请输入排序", trigger: "blur" }],
      },
    };
  },

  mounted() {},

  methods: {
    editSecond(params) {
      this.isEdit = true;
      this.queryData.second_name = params.second_name;
      this.queryData.id = params.id;
      this.file_list = params.image.split(",");
      this.queryData.sort = params.sort;
    },
    confirmAddSecondtype() {
      this.$refs["queryDataRef"].validate((valid) => {
        if (valid) {
          this.queryData.image = this.file_list.join(",");
          this.$request.second.updateSecondtype(this.queryData).then((res) => {
            if (res.data.error_code == 0) {
              this.$message.success("修改成功");
              this.$emit("closeDia");
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.w-220 {
  width: 220px;
}
</style>