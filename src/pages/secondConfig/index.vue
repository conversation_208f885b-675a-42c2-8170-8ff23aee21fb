<template>
  <div>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane label="秒发分类" name="first">
        <secondtype v-if="activeName == 'first'"></secondtype>
      </el-tab-pane>
      <el-tab-pane label="商品列表" name="second">
        <goods v-if="activeName == 'second'"></goods>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import secondtype from "./second.vue";
import goods from "./goods.vue";
export default {
  name: "MarketingIndex",
  components: { secondtype, goods },
  data() {
    return {
      activeName: "first",
    };
  },

  mounted() {},

  methods: {},
};
</script>