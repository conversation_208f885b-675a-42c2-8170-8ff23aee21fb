<template>
  <div>
    <el-form
      :model="updateHotkeywordForm"
      ref="updateHotkeywordForm"
      :rules="updateHotkeywordRules"
      label-width="120px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="频道" prop="type">
        <el-radio-group v-model="updateHotkeywordForm.type">
          <el-radio
            v-for="item in HotKeywordsTypeOptions"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="updateHotkeywordForm.keyword"
          style="width: 80%"
        ></el-input>
      </el-form-item>
      <el-form-item label="是否热门" prop="is_hot">
        <el-radio v-model="updateHotkeywordForm.is_hot" label="1">是</el-radio>
        <el-radio v-model="updateHotkeywordForm.is_hot" label="0">否</el-radio>
      </el-form-item>
      <el-form-item label="权重值" prop="sort">
        <el-input-number
          v-model="updateHotkeywordForm.sort"
          controls-position="right"
          :min="0"
          :step="1"
          :max="9999999"
        ></el-input-number>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: "Vue2MarketingUpdatebullein",

  data() {
    return {
      updateHotkeywordForm: {
        type: null,
        keyword: "",
        is_hot: "",
        sort: 0,
      },
      updateHotkeywordRules: {
        type: [
          {
            required: true,
            message: "请选择频道",
            trigger: "change",
          },
        ],
        keyword: [
          {
            required: true,
            message: "请输入关键词",
            trigger: "blur",
          },
        ],
        is_hot: [
          {
            required: true,
            message: "请选中是否热门",
            trigger: "blur",
          },
        ],
        sort: [
          {
            required: true,
            message: "请输入正确的数字",
            trigger: "blur",
          },
        ],
      },
      isEdit: false,
      HotKeywordsTypeOptions: [
        { label: "首页", value: 0 },
        { label: "闪购", value: 1 },
        { label: "秒发", value: 2 },
        { label: "社区", value: 3 },
      ],
    };
  },

  mounted() {},

  methods: {
    editRow(row) {
      console.log("是否热门", row);
      this.updateHotkeywordForm.type = row.type;
      this.updateHotkeywordForm.keyword = row.keyword;
      this.updateHotkeywordForm.sort = row.sort;
      this.updateHotkeywordForm.id = row.id;
      this.updateHotkeywordForm.is_hot = String(row.is_hot);
      console.info(this.updateHotkeywordForm);
      this.isEdit = true;
    },
    updatehotkeyword() {
      this.$refs.updateHotkeywordForm.validate((valid) => {
        if (valid) {
          let isEditName = "";
          if (this.isEdit) {
            isEditName = "updatehotkeyword";
          } else {
            isEditName = "createhotkeyword";
          }
          this.$request.hotkeyword[isEditName](this.updateHotkeywordForm).then(
            (result) => {
              if (result.data.error_code == 0) {
                this.$emit("closeHotkeywordDialog");
              }
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  beforeDestroy() {
    this.isEdit = false;
  },
};
</script>
