<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form :inline="true" size="mini">
                <el-form-item>
                    <el-input
                        v-model="queryHotKeywordsData.keyword"
                        placeholder="请输入热门关键词"
                        @keyup.enter.native="queryHotKeywords"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-select
                        v-model="queryHotKeywordsData.type"
                        placeholder="请选择频道"
                        clearable
                        filterable
                    >
                        <el-option
                            v-for="item in HotKeywordsTypeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryHotKeywords"
                        >查询</el-button
                    >

                    <el-button type="success" size="mini" @click="addBullein"
                        >添加关键词</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>
        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="hotkeywordsListData"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="频道" prop="type" width="100">
                    <template slot-scope="scope">
                        {{ HotKeywordsTypeValue[scope.row.type] }}
                    </template>
                </el-table-column>
                <el-table-column label="关键词" prop="keyword">
                </el-table-column>
                <el-table-column label="是否热门" prop="is_hot" width="100">
                    <template #default="is_hot">
                        <span>{{
                            `${is_hot.row.is_hot == 0 ? "否" : ""}
                            ${is_hot.row.is_hot == 1 ? "是" : ""}`
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="是否显示" prop="is_show" width="100">
                    <template slot-scope="scope">
                        {{ scope.row.is_show == 0 ? "否" : "是" }}
                    </template>
                </el-table-column>
                <el-table-column label="权重" prop="sort" width="100">
                </el-table-column>
                <el-table-column label="修改时间" prop="updated_at" width="200">
                </el-table-column>
                <el-table-column label="创建时间" prop="created_at" width="200">
                </el-table-column>
                <el-table-column
                    label="操作人"
                    prop="operator_name"
                    width="100"
                >
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="180">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="editHotKeyword(scope.row)"
                            >编辑
                        </el-button>
                        <el-button
                            type="text"
                            size="mini"
                            @click="updateHotkeywordStatus(scope.row)"
                            >{{ scope.row.is_show == 1 ? "隐藏" : "显示" }}
                        </el-button>
                        <el-popconfirm
                            title="你确定要删除吗?"
                            style="margin-left: 10px"
                            @confirm="deleteHotKeyword(scope.row)"
                        >
                            <el-button slot="reference" type="text" size="mini"
                                >删除
                            </el-button>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryHotKeywordsData.limit"
                :current-page="queryHotKeywordsData.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>
        <div>
            <el-dialog
                title="热门搜索词配置"
                :visible.sync="updateHotkeywordVisible"
                width="40%"
                :close-on-click-modal="false"
            >
                <updateHotkeywordVue
                    @closeHotkeywordDialog="closeHotkeywordDialog"
                    v-if="updateHotkeywordVisible"
                    ref="updateHotkeyword"
                ></updateHotkeywordVue>
                <span
                    slot="footer"
                    style="display: flex; justify-content: center"
                >
                    <el-button @click="updateHotkeywordVisible = false"
                        >取消</el-button
                    >
                    <el-button type="primary" @click="comfirmUpdateBullein"
                        >确定</el-button
                    >
                </span>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import updateHotkeywordVue from "./updateHotkeyword.vue";

export default {
    name: "Vue2MarketingBullein",
    components: { updateHotkeywordVue },
    data() {
        return {
            queryHotKeywordsData: {
                keyword: "",
                type: "",
                limit: 10,
                page: 1
            },
            hotkeywordsListData: [],
            updateHotkeywordVisible: false,
            total: 0,
            HotKeywordsTypeOptions: [
                { label: "首页", value: 0 },
                { label: "闪购", value: 1 },
                { label: "秒发", value: 2 },
                { label: "社区", value: 3 }
            ],
            HotKeywordsTypeValue: {
                0: "首页",
                1: "闪购",
                2: "秒发",
                3: "社区"
            }
        };
    },

    mounted() {
        this.getHotKeywordList();
    },

    methods: {
        queryHotKeywords() {
            this.queryHotKeywordsData.page = 1;
            this.getHotKeywordList();
        },
        addBullein() {
            this.updateHotkeywordVisible = true;
        },
        editHotKeyword(row) {
            this.updateHotkeywordVisible = true;
            this.$nextTick(() => {
                this.$refs.updateHotkeyword.editRow(row);
            });
        },
        closeHotkeywordDialog() {
            this.updateHotkeywordVisible = false;
            this.getHotKeywordList();
            this.$message({
                type: "success",
                message: "操作成功"
            });
        },
        getHotKeywordList() {
            this.$request.hotkeyword
                .gethotkeywordList(this.queryHotKeywordsData)
                .then(res => {
                    this.hotkeywordsListData = res.data.data.list;
                    this.total = res.data.data.total;
                });
        },
        updateHotkeywordStatus(row) {
            let upStatus = JSON.parse(JSON.stringify(row));
            let is_show = upStatus.is_show == 0 ? 1 : 0;
            this.$request.hotkeyword
                .updateHotkeywordStatus({
                    id: upStatus.id,
                    is_show
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.getHotKeywordList();
                        this.$message({
                            type: "success",
                            message: "操作成功"
                        });
                    }
                });
        },
        comfirmUpdateBullein() {
            this.$nextTick(() => {
                this.$refs.updateHotkeyword.updatehotkeyword();
            });
        },
        handleSizeChange(limit) {
            this.queryHotKeywordsData.limit = limit;
            this.queryHotKeywordsData.page = 1;
            this.getHotKeywordList();
        },
        handleCurrentChange(page) {
            this.queryHotKeywordsData.page = page;
            this.getHotKeywordList();
        },
        deleteHotKeyword(row) {
            this.$request.hotkeyword
                .deleteHotkeyword({
                    id: row.id
                })
                .then(result => {
                    if (result.data.error_code == 0) {
                        this.$message({
                            type: "success",
                            message: "删除成功"
                        });
                        this.getHotKeywordList();
                    }
                });
        }
    }
};
</script>
