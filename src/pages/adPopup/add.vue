<template>
    <div>
        <el-form
            :model="form"
            :rules="formRules"
            ref="ruleForm"
            class="demo-ruleForm"
            size="mini"
        >
            <el-form-item
                label="标题"
                :label-width="formLabelWidth"
                prop="title"
            >
                <el-input
                    size="mini"
                    v-model="form.title"
                    placeholder="请输入标题"
                    style="width:400px"
                ></el-input>
            </el-form-item>
            <el-form-item
                label="模式"
                :label-width="formLabelWidth"
                prop="pattern"
            >
                <!-- <el-radio v-model="form.pattern" label="1">新用户</el-radio>
                <el-radio v-model="form.pattern" label="2">老用户</el-radio>
                <el-radio v-model="form.pattern" label="3">未登录</el-radio> -->
                <el-checkbox-group v-model="form.pattern">
                    <el-checkbox label="1">新用户</el-checkbox>
                    <el-checkbox label="2">老用户</el-checkbox>
                    <el-checkbox label="3">未登录</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item
                label="图片"
                :label-width="formLabelWidth"
                prop="image"
            >
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :limit="1"
                    :dir="dir"
                    :file-list="icon_map"
                    :limitWhList="[450, 620]"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>

            <el-form-item
                label="排序"
                :label-width="formLabelWidth"
                prop="sort"
            >
                <el-input-number
                    size="mini"
                    v-model="form.sort"
                    controls-position="right"
                    @change="handleChange"
                    :min="0"
                ></el-input-number>
            </el-form-item>
            <PathConfig :path_id="form.path_id" ref="pathConfig"></PathConfig>
            <el-form-item
                label="上架时间"
                :label-width="formLabelWidth"
                prop="start_time"
            >
                <el-date-picker
                    v-model="form.start_time"
                    type="datetime"
                    placeholder="请选择上架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item
                label="下架时间"
                :label-width="formLabelWidth"
                prop="end_time"
            >
                <el-date-picker
                    v-model="form.end_time"
                    type="datetime"
                    placeholder="请选择下架日期时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item style="margin-top:50px;margin-left:40%">
                <el-button @click="closeDiog">取 消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确 定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import PathConfig from "../../components/pathConfig/pathConfig.vue";
export default {
    components: {
        VosOss,
        PathConfig
    },
    data() {
        //验证排序
        let weightValidator = (rule, value, callback) => {
            const reg = /^[0-9]*[1-9][0-9]*$/;
            if (!value) {
                callback(new Error("请输入排序"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正确的排序"));
            } else {
                callback();
            }
        };
        //图标验证
        const checkTitileMap = (rules, value, callback) => {
            if (this.icon_map.length == 0) {
                callback(new Error("请上传图标"));
            } else {
                callback();
            }
        };
        return {
            dialogFormVisible: false,
            icon_map: [],
            dir: "vinehoo/vos/marketing/",
            form: {
                pattern: [],
                client: ["0", "1", "2", "3"],
                title: "",
                path_id: " ",
                image: "",
                sort: 1,
                start_time: "",
                end_time: ""
            },
            pathOptions: [],
            formRules: {
                start_time: [
                    {
                        required: true,
                        message: "请选择上架时间",
                        trigger: "blur"
                    }
                ],
                end_time: [
                    {
                        required: true,
                        message: "请选择下架时间",
                        trigger: "blur"
                    }
                ],
                pattern: [
                    {
                        required: true,
                        message: "请选择模式",
                        trigger: "blur"
                    }
                ],
                client: [
                    {
                        required: true,
                        message: "请选择客户端",
                        trigger: "blur"
                    }
                ],
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                path_id: [
                    {
                        required: true,
                        message: "请选择路径",
                        trigger: "blur"
                    }
                ],
                image: [
                    {
                        required: true,
                        validator: checkTitileMap
                    }
                ],
                sort: [
                    {
                        required: true,
                        validator: weightValidator,
                        trigger: "blur"
                    }
                ]
            },
            formLabelWidth: "150px"
        };
    },
    mounted() {},
    methods: {
        closeDiog() {
            this.$emit("close");
        },
        //改变排序
        handleChange(value) {
            console.log("改变后的排序", value);
        },
        //表单提交，在父组件调用
        submitForm() {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    let {
                        client,
                        arr,
                        pathNum1
                    } = this.$refs.pathConfig.getAddData(); //接受路径配置传过来的参数
                    this.form.path_id = pathNum1;
                    console.log("路径id", this.form.path_id);
                    let data = {
                        client: client,
                        params: arr,
                        path: pathNum1,
                        image: this.icon_map.join(","),
                        title: this.form.title,
                        pattern: this.form.pattern.join(","),
                        // channel: this.form.channel,
                        sort: this.form.sort,
                        type: 3,
                        start_time: this.form.start_time,
                        end_time: this.form.end_time
                    };
                    console.log("表单", data);

                    this.$request.adBanner.adBanner(data).then(res => {
                        console.log("返回的值", res);
                        if (res.data.error_code == 0) {
                            this.$Message.success("添加成功");
                            this.$emit("close");
                            this.$emit("getPopupList");
                        }
                    });
                } else {
                    console.log("失败");
                    return false;
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
    border-color: #409eff;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}
.avatar {
    width: 178px;
    height: 178px;
    display: block;
}
</style>
