<template>
    <el-form
        :model="templateData"
        ref="templateDataForm"
        :rules="rules"
        label-width="100px"
        :inline="false"
        size="normal"
    >
        <el-form-item label="标题" prop="title">
            <el-input
                v-model="templateData.title"
                placeholder="请输入标题"
               style="width: 340px;"
                maxlength="15"
                show-word-limit
            ></el-input>
        </el-form-item>
        <el-form-item label="数据类型" prop="data_type">
            <!-- 数据类型 数据类型 0:快报详情 1:商品详情 2:帖子详情 3:活动 4:酒闻详情 -->
            <el-select
                v-model="templateData.data_type"
                placeholder="请输入数据类型"
                clearable
            >
                <el-option
                    v-for="item in dataTypeOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item
            v-if="templateData.data_type !== ''"
            :label="`${data_type_text[templateData.data_type]}ID`"
            prop="param_id"
        >
            <el-input
                v-model="templateData.param_id"
                class="w-220"
                :placeholder="`请输入${data_type_text[templateData.data_type]}`"
            ></el-input>
        </el-form-item>

        <el-form-item label="推送类型" prop="push_type">
            <el-radio-group v-model="templateData.push_type">
                <el-radio style="margin-bottom: 0" :label="0"
                    >全部推送</el-radio
                >
                <el-radio style="margin-bottom: 0" :label="3"
                    >导入推送</el-radio
                >
            </el-radio-group>
            <el-row v-if="templateData.push_type === 3">
                <el-col :span="12">
                    <vos-oss
                        filesType="/"
                        listType="text"
                        showFileList
                        dir="vinehoo/marketing/apppush/"
                        :file-list="fileList"
                        :limit="1"
                        is_download
                        @on-success="onSuccess"
                    >
                        <el-button size="mini" type="success">上传</el-button>
                        <el-button
                            type="warning"
                            size="mini"
                            class="m-l-10"
                            @click.stop="down"
                            >下载导入模版</el-button
                        >
                    </vos-oss>
                </el-col>
            </el-row>
        </el-form-item>
        <el-form-item label="消息内容" prop="content">
            <el-input
                class="w-520"
                type="textarea"
                v-model="templateData.content"
                placeholder="请输入消息内容"
                size="normal"
                maxlength="38"
                show-word-limit
                :rows="5"
            ></el-input>
        </el-form-item>
        <el-form-item label="推送时间" prop="timing_push_at">
            <el-radio v-model="pushtimemethod" label="0">立即</el-radio>
            <el-radio v-model="pushtimemethod" label="1">定时</el-radio>
            <el-date-picker
                v-if="pushtimemethod == 1"
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="templateData.timing_push_at"
                type="datetime"
                placeholder="选择日期时间"
            >
            </el-date-picker>
        </el-form-item>
    </el-form>
</template>

<script>
import vosOss from "vos-oss";

export default {
    components: {
        vosOss
    },
    data() {
        const checkTime = (rule, value, callback) => {
            if (this.pushtimemethod !== "") {
                if (
                    (value === "" || value === null) &&
                    this.pushtimemethod == "1"
                ) {
                    callback(new Error("请选择时间"));
                } else {
                    callback();
                }
            } else {
                callback(new Error("请选择推送时间"));
            }
        };

        const checkPushType = (rule, value, callback) => {
            console.log("this", this);
            if (this.templateData.push_type === "") {
                callback(new Error("请选择推送类型"));
            }
            if (this.templateData.push_type === 3 && !this.fileList.length) {
                callback(new Error("请上传文件"));
            }
            callback();
        };
        return {
            templateData: {
                title: "",
                content: "",
                data_type: "",
                param_id: "",
                push_type: 0,
                timing_push_at: "",
                file_url: ""
            },
            rules: {
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                content: [
                    {
                        required: true,
                        message: "请输入消息内容",
                        trigger: "blur"
                    }
                ],
                data_type: [
                    {
                        required: true,
                        message: "请选择数据类型",
                        trigger: "change"
                    }
                ],
                push_type: [
                    {
                        required: true,
                        validator: checkPushType
                    }
                ],
                param_id: [
                    {
                        required: true,
                        message: "请输入数据类型ID",
                        trigger: "blur"
                    }
                ],
                timing_push_at: [
                    {
                        required: true,
                        validator: checkTime
                    }
                ]
            },
            pushtimemethod: "",
            dataTypeOption: [
                {
                    value: 0,
                    label: "快报详情"
                },
                {
                    value: 1,
                    label: "商品详情"
                },
                {
                    value: 2,
                    label: "帖子详情"
                },
                {
                    value: 3,
                    label: "活动"
                },
                {
                    value: 4,
                    label: "酒闻详情"
                }
            ],
            data_type_text: {
                0: "快报",
                1: "商品",
                2: "帖子",
                3: "活动",
                4: "酒闻"
            },
            fileList: []
        };
    },

    methods: {
        submit() {
            this.$refs["templateDataForm"].validate(valid => {
                if (valid) {
                    if (this.templateData.push_type === 3) {
                        this.templateData.file_url = this.fileList.join();
                    } else {
                        this.templateData.file_url = "";
                    }
                    let data = JSON.parse(JSON.stringify(this.templateData));
                    console.log("data", data);
                    if (this.pushtimemethod == 0) {
                        data.timing_push_at = 0;
                    }
                    this.$request.apppush.addTemplate(data).then(result => {
                        if (result.data.error_code == 0) {
                            this.$message.success("添加成功");
                            this.$emit("updateSuccess");
                        }
                    });
                }
            });
        },
        onSuccess() {
            this.$refs.templateDataForm.validateField("push_type");
        },
        down() {
            window.location.href =
                "https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/download/template/app%E6%B6%88%E6%81%AF%E5%AF%BC%E5%85%A5%E6%8E%A8%E9%80%81%E6%A8%A1%E6%9D%BF.xls";
        }
    }
};
</script>

<style lang="scss" scoped>
.w-220 {
    width: 220px;
}
.w-520 {
    width: 520px;
}
</style>
