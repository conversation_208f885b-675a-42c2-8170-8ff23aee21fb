<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-03-18 10:46:06
 * @LastEditors: zhangyu <EMAIL>
 * @LastEditTime: 2022-05-25 13:43:18
 * @FilePath: /vue2-marketing/src/pages/appPush/Home.vue
 * @Description: app推送
-->
<template>
    <div>
        <el-card shadow="hover">
            <el-input
                v-model="queryData.keyword"
                size="mini"
                class="m-r-10 w-normal"
                placeholder="请输入关键词"
                @keyup.enter.native="search"
            ></el-input>
            <el-button type="primary" size="mini" @click="search"
                >查询</el-button
            >
            <el-button type="warning" size="mini" @click="addTemplate"
                >新增</el-button
            >
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
            <el-table
                :data="templateList"
                border
                size="mini"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column label="ID" prop="id" width="80">
                </el-table-column>
                <el-table-column label="标题" prop="title" min-width="150">
                </el-table-column>
                <el-table-column
                    label="消息内容"
                    prop="content"
                    min-width="200"
                >
                </el-table-column>
                <!-- <el-table-column label="数据ID" prop="label"> </el-table-column> -->
                <el-table-column label="数据类型" prop="data_type" width="100">
                    <template slot-scope="scope">
                        <span>{{ dataType[scope.row.data_type] }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    label="通知类型"
                    prop="notice_type"
                    width="120"
                >
                    <template slot-scope="scope">
                        <span>{{ noticeType[scope.row.notice_type] }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="接收端" prop="push_type" width="120">
                    <template slot-scope="scope">
                        <span>{{ pushType[scope.row.push_type] }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="用户ID" width="100" prop="created_id">
                </el-table-column>
                <el-table-column label="跳转路由" prop="label" width="220">
                </el-table-column>
                <el-table-column label="地址名称" prop="title" width="220">
                </el-table-column>
                <el-table-column label="创建时间" prop="created_at" width="180">
                </el-table-column>
                <el-table-column label="操作者" prop="created_name" width="120">
                </el-table-column>
            </el-table>
        </el-card>
        <div style="display: flex; justify-content: center; margin-top: 20px">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryData.page"
                :page-size="queryData.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <el-dialog
            title="模板设置"
            :visible.sync="templateVisible"
            width="80%"
            :close-on-click-modal="false"
        >
            <Add
                ref="templateRef"
                @updateSuccess="updateSuccess"
                v-if="templateVisible"
            ></Add>
            <span slot="footer" style="display:flex;justify-content:center">
                <el-button @click="templateVisible = false">取消</el-button>
                <el-button type="primary" @click="comfirm">确认</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import Add from "./Add.vue";
export default {
    components: { Add },
    data() {
        return {
            queryData: {
                keyword: "",
                page: 1,
                limit: 10
            },
            templateList: [],
            templateVisible: false,
            total: 0,
            //数据类型  0:快报详情 1:商品详情 2:帖子详情 3:活动 4:酒闻详情 5:h5外链 10:订单通知 11:粉丝关注 12:评论 13:回复 14:点赞 15:优惠券到期 16:驳回 17:直播预约 18:违反公约 20:物流通知
            dataType: {
                0: "快报详情",
                1: "商品详情",
                2: "帖子详情",
                3: "活动",
                4: "酒闻详情",
                5: "h5外链",
                10: "订单通知",
                11: "粉丝关注",
                12: "评论",
                13: "回复",
                14: "点赞",
                15: "优惠券到期",
                16: "驳回",
                17: "直播预约",
                18: "违反公约",
                20: "物流通知"
            },
            //推送类型 0:全部推送 1:单个推送 2:部分推送
            pushType: {
                0: "全部推送",
                1: "单个推送",
                2: "部分推送"
            },
            //通知类型 0:系统通知(data_id等于0,1,2,3,4) 1:用户通知(data_id等于5,6,7,8,9,10) 2:物流通知(data_id等于11)
            noticeType: {
                0: "系统通知",
                1: "用户通知",
                2: "物流通知"
            }
        };
    },

    mounted() {
        this.queryAppPushList();
    },

    methods: {
        search() {
            this.queryData.page = 1;
            this.queryAppPushList();
        },
        handleCurrentChange(page) {
            this.queryData.page = page;
            this.queryAppPushList();
        },
        handleSizeChange(limit) {
            this.queryData.limit = limit;
            this.queryData.page = page;
            this.queryAppPushList();
        },
        updateSuccess() {
            this.templateVisible = false;
            this.queryAppPushList();
        },
        queryAppPushList() {
            this.$request.apppush.getTemplateList(this.queryData).then(res => {
                if (res.data.error_code == 0) {
                    this.templateList = res.data.data.list;
                    this.total = res.data.data.total;
                }
            });
        },
        addTemplate() {
            this.templateVisible = true;
        },
        comfirm() {
            this.$refs.templateRef.submit();
        }
    }
};
</script>

<style lang="scss" scoped></style>
