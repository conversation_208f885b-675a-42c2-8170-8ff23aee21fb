<template>
    <div>
        <el-button
            type="primary"
            size="mini"
            style="margin-bottom:15px"
            @click="dialogStatus = true"
            >添加商品</el-button
        >
        <el-card shadow="never">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column
                    align="center"
                    prop="goods_id"
                    label="期数"
                    width="100"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="title"
                    label="商品名称"
                    min-width="150"
                >
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="banner_img"
                    label="图片"
                    min-width="80"
                >
                    <template slot-scope="row">
                        <el-image
                            style="width: 70px; height: 70px"
                            :src="row.row.banner_img"
                            fit="cover"
                        ></el-image>
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="weigh"
                    label="排序"
                    width="100"
                >
                    <template slot-scope="row">
                        <!-- <el-input></el-input> -->
                        <input
                            class="weigh"
                            placeholder="请输入内容"
                            @change="changeWeigh(row.row)"
                            v-model="row.row.weigh"
                            type="text"
                        />
                    </template>
                </el-table-column>
                <el-table-column
                    align="center"
                    prop="onsale_status"
                    label="是否在售"
                >
                    <template slot-scope="row">
                        <span>{{
                            onsale_status_text[row.row.onsale_status]
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="address" align="center" label="操作">
                    <template slot-scope="row">
                        <el-popconfirm
                            title="这是一段内容确定删除吗？"
                            @confirm="del(row.row)"
                        >
                            <el-button slot="reference" type="text" size="mini"
                                >删除</el-button
                            >
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <div class="pagination-block">
            <el-pagination
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pageAttr.page"
                :page-size="pageAttr.limit"
                :page-sizes="[10, 30, 50, 100, 200]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <!-- 添加商品 -->
        <div>
            <el-dialog
                :close-on-click-modal="false"
                title="添加商品"
                :visible.sync="dialogStatus"
                width="30%"
                append-to-body
            >
                <div>
                    <AddGoods
                        v-if="dialogStatus"
                        :type="type"
                        :rowData="rowData"
                        @close="close"
                    ></AddGoods>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import AddGoods from "./addGoods.vue";
export default {
    components: {
        AddGoods
    },
    props: ["rowData", "type"],
    data() {
        return {
            dialogStatus: false,
            tableData: [],
            pageAttr: {
                page: 1,
                limit: 10
            },
            total: 0,
            onsale_status_text: {
                0: "待上架",
                1: "待售中",
                2: "在售中",
                3: "已下架",
                4: "已售罄"
            }
        };
    },
    mounted() {
        console.log(this.rowData);
        switch (this.type) {
            case 1:
                this.winepartyActivityGoods();
                break;
            case 2:
                this.getPullNewGoodsList();
                break;
            default:
                break;
        }
    },
    methods: {
        //获取酒会推广商品列表
        async winepartyActivityGoods() {
            let data = {
                invite_activity_id: this.rowData.id,
                page: this.pageAttr.page,
                limit: this.pageAttr.limit
            };
            let res = await this.$request.winePartyPromote.winepartyActivityGoods(
                data
            );
            console.log("活动商品列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        //获取拉新活动商品列表
        async getPullNewGoodsList() {
            let data = {
                invite_activity_id: this.rowData.id,
                page: this.pageAttr.page,
                limit: this.pageAttr.limit
            };
            let res = await this.$request.PullNew.getPullNewGoodsList(data);
            console.log("拉新活动商品列表", res);
            if (res.data.error_code == 0) {
                this.tableData = res.data.data.list;
                this.total = res.data.data.total;
            }
        },
        //改变排序值
        async changeWeigh(row) {
            switch (this.type) {
                case 1:
                    let data1 = {
                        id: row.id,
                        invite_activity_id: row.invite_activity_id,
                        goods_id: row.goods_id,
                        weigh: row.weigh
                    };
                    let res1 = await this.$request.winePartyPromote.editWinePartyActiveGoods(
                        data1
                    );
                    console.log("编辑", res1);
                    if (res1.data.error_code == 0) {
                        this.$Message.success("更新成功");
                        this.winepartyActivityGoods();
                    }
                    break;
                case 2:
                    let data2 = {
                        id: row.id,
                        invite_activity_id: row.invite_activity_id,
                        goods_id: row.goods_id,
                        weigh: row.weigh
                    };
                    let res2 = await this.$request.PullNew.editPullNewGoods(
                        data2
                    );
                    console.log("编辑", res2);
                    if (res2.data.error_code == 0) {
                        this.$Message.success("更新成功");
                        this.getPullNewGoodsList();
                    }
                    break;
                default:
                    break;
            }
        },

        //删除
        async del(row) {
            console.log(row);
            let res = {};
            switch (this.type) {
                case 1:
                    res = await this.$request.winePartyPromote.delGoods({
                        id: row.id
                    });
                    console.log("删除结果", res);
                    if (res.data.error_code == 0) {
                        this.$Message.success("删除成功");
                        this.winepartyActivityGoods();
                    }
                    break;
                case 2:
                    res = await this.$request.PullNew.delPullNewGoods({
                        id: row.id
                    });
                    console.log("删除结果", res);
                    if (res.data.error_code == 0) {
                        this.$Message.success("删除成功");
                        this.getPullNewGoodsList();
                    }

                    break;
                default:
                    break;
            }
        },
        close() {
            this.dialogStatus = false;
            switch (this.type) {
                case 1:
                    this.winepartyActivityGoods();
                    break;
                case 2:
                    this.getPullNewGoodsList();
                    break;
                default:
                    break;
            }
        },
        handleSizeChange(val) {
            this.pageAttr.page = 1;
            this.pageAttr.limit = val;
            switch (this.type) {
                case 1:
                    this.winepartyActivityGoods();
                    break;
                case 2:
                    this.getPullNewGoodsList();
                    break;
                default:
                    break;
            }
            console.log(`每页 ${val} 条`);
        },
        handleCurrentChange(val) {
            console.log(`当前页: ${val}`);
            this.pageAttr.page = val;
            switch (this.type) {
                case 1:
                    this.winepartyActivityGoods();
                    break;
                case 2:
                    this.getPullNewGoodsList();
                    break;
                default:
                    break;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.pagination-block {
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 10px;
}
.weigh {
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #606266;
    display: inline-block;
    font-size: inherit;
    height: 40px;
    line-height: 40px;
    outline: 0;
    padding: 0 15px;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;
    text-align: center;
}
</style>
