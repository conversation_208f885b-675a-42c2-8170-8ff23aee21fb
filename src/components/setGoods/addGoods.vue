<template>
    <div>
        <el-form
            :model="form"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            size="mini"
            class="demo-ruleForm"
        >
            <el-form-item label="商品期数" prop="goods_id">
                <el-input
                    v-model="form.goods_id"
                    style="width:300px;margin-right:10px"
                ></el-input>
                <el-button type="primary" @click="isExistRelationid()"
                    >确定</el-button
                >
            </el-form-item>
            <el-form-item label="商品名称" prop="name" v-if="isGoodsName">
                <el-input
                    disabled
                    v-model="goodsName"
                    style="width:300px"
                ></el-input>
            </el-form-item>

            <el-form-item label="排序" prop="weigh">
                <el-input-number
                    v-model="form.weigh"
                    label="描述文字"
                ></el-input-number>
            </el-form-item>
            <el-form-item>
                <el-button @click="close()">取消</el-button>
                <el-button type="primary" @click="submitForm('ruleForm')"
                    >确定</el-button
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    props: ["type", "rowData"],
    data() {
        return {
            form: {
                goods_id: "",
                weigh: "1"
            },
            goodsName: "",
            isGoodsName: false,
            rules: {
                goods_id: [
                    {
                        required: true,
                        message: "请输入期数",
                        trigger: "blur"
                    }
                ],
                weigh: [
                    {
                        required: true,
                        message: "请输入排序值",
                        trigger: "blur"
                    }
                ]
            }
        };
    },
    methods: {
        close() {
            this.$emit("close");
        },
        //商品期数是否存在
        async isExistRelationid() {
            if (this.form.goods_id) {
                let data = {
                    periods: this.form.goods_id
                };
                console.log("商品期数", data);
                let res = await this.$request.cardManage.isExistRelationid(
                    data
                );
                console.log("商品期数是否存在", res);
                if (res.data.error_code == 0) {
                    if (res.data.data.list.length == 0) {
                        this.$message.error("商品期数不存在");
                        this.isGoodsName = false;
                    } else {
                        this.$message.success("查找成功");
                        this.goodsName = res.data.data.list[0].brief;
                        this.isGoodsName = true;
                    }
                }
            }
        },
        async addWinePartyActiveGoods() {
            let data = {
                invite_activity_id: this.rowData.id,
                goods_id: this.form.goods_id,
                weigh: this.form.weigh
            };
            console.log("参数", data);
            let res = await this.$request.winePartyPromote.addWinePartyActiveGoods(
                data
            );
            console.log("结果", res);
            if (res.data.error_code == 0) {
                this.$Message.success("添加成功");
                this.$emit("close");
            }
        },
        async addPullNewGoods() {
            let data = {
                invite_activity_id: this.rowData.id,
                goods_id: this.form.goods_id,
                weigh: this.form.weigh
            };
            console.log("参数", data);
            let res = await this.$request.PullNew.addPullNewGoods(data);
            console.log("结果", res);
            if (res.data.error_code == 0) {
                this.$Message.success("添加成功");
                this.$emit("close");
            }
        },
        submitForm(formName) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    switch (this.type) {
                        case 1:
                            this.addWinePartyActiveGoods();
                            break;
                        case 2:
                            this.addPullNewGoods();
                            break;
                        default:
                            break;
                    }
                } else {
                    console.log("error submit!!");
                    return false;
                }
            });
        }
    }
};
</script>

<style></style>
