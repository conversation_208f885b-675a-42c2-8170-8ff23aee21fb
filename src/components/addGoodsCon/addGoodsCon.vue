<template>
    <div>
      <!-- 是编辑的时候 -->
  
      <!-- 新增的时候 -->
      <div class="add">
        <el-form-item label="添加商品" prop="path_id" label-width="150px">
          <el-radio v-model="add_methodnew" :label="0">手动</el-radio>
          <el-radio v-model="add_methodnew" :label="1">自动</el-radio>
        </el-form-item>
        <div v-if="add_methodnew==1">  <el-form-item label="" prop="path_id" label-width="150px">
          <el-radio-group :value="auto_add_typeNew" @input="changeAutoAddType">
            <el-radio :label="1">国家</el-radio>
            <el-radio :label="2">产区</el-radio>
            <el-radio :label="3">酒庄</el-radio>
            <el-radio :label="4">标签</el-radio>
            <el-radio :label="5">类型</el-radio>
            <el-radio :label="6">标题</el-radio>
            <el-radio :label="7">葡萄品种</el-radio>
            <el-radio :label="8">库存</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="定义内容" prop="path_id" label-width="150px">
          <el-select
            v-if="auto_add_typeNew == 1"
            v-model="chooseOptions"
            filterable
            style="width: 280px"
            multiple
            remote
            clearable
            reserve-keyword
            placeholder="请输入国家"
            :remote-method="getCountryList"
            @change="countryChange"
          >
            <el-option
              v-for="item in CountryOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
          v-if="auto_add_typeNew == 2"
              v-model="chooseOptions"
              filterable
              remote
              multiple
              style="width: 280px"
              clearable
              reserve-keyword
              placeholder="请输入产区"
              :remote-method="getRegionList"
             
          >
              <el-option
                  v-for="item in RegionOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
          </el-select>
          <el-select
          v-if="auto_add_typeNew == 3"
              v-model="chooseOptions"
              filterable
              style="width: 280px"
              clearable
              remote
              reserve-keyword
              placeholder="请输入酒庄"
              :remote-method="getWineryList"
              multiple
          >
              <el-option
                  v-for="item in WineryOptions"
                  :key="item.id"
                  :label="
                     item.name
                  "
                  :value="item.id"
              >
              </el-option>
          </el-select>
          <el-select
          v-if="auto_add_typeNew == 4"
              v-model="chooseOptions"
              filterable
              style="width: 280px"
              clearable
              remote
              placeholder="请输入标签"
              multiple
             
          >
              <el-option
                  v-for="item in tagOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
          </el-select>
          <el-cascader
          v-if="auto_add_typeNew == 5"
              :options="typeList"
              v-model="chooseOptions"
              filterable
              style="width: 280px"
              :show-all-levels="false"
              :props="{
                  label: 'name',
                  value: 'id',
                  checkStrictly: true,
                  multiple: true,
                  emitPath:false
              }"
          >
          </el-cascader>
          <div v-if="auto_add_typeNew == 6"> 
           
              <el-input
                class="input-new-tag"
                v-if="inputVisible"
                v-model="inputValue"
                ref="saveTagInput"
                size="small"
                @keyup.enter.native="handleInputConfirm"
                @blur="handleInputConfirm"
              >
              </el-input>
              <el-button v-else style="width: 180px;" size="small" @click="showInput">请输入标题</el-button>
              <el-tag
                :key="tag"
                v-for="tag in chooseOptions"
                closable
                :disable-transitions="false"
                @close="handleClose(tag)">
                {{tag}}
              </el-tag>
          </div>
          <el-select
          v-if="auto_add_typeNew == 7"
              v-model="chooseOptions"
              filterable
              style="width: 280px"
              clearable
              remote
              reserve-keyword
              placeholder="请输入葡萄品种"
              :remote-method="getGrapeList"
              multiple
          >
              <el-option
                  v-for="item in GrapeOptions"
                  :key="item.id"
                  :label="
                     item.name
                  "
                  :value="item.id"
              >
              </el-option>
          </el-select>
          <div v-if="auto_add_typeNew == 8"> 
           
           <el-input-number
            size="large"
             v-model="count"
            :min="1"
            :step="1"
            :step-strictly="true"
             placeholder="请输入库存"
           >
           </el-input-number>
           
       </div>
        </el-form-item></div>
       
      </div>
    </div>
  </template>
  
  <script>
  export default {
    props: ["add_method", "auto_add_type", "auto_add_content", "isEdit"],
  
    data() {
      return {
        add_methodnew: 0,
        auto_add_typeNew: 1,
        CountryOptions: [], //搜索国家列表
        RegionOptions: [], //搜索产区列表
        WineryOptions: [], //搜索酒庄列表
        GrapeOptions:[],//葡萄品种
        tagOptions: [], //标签列表
        typeList: [], //类型列表
        chooseOptions: [], //选中的列表
        AllCountryOptions: [], //已搜索国家列表
        AllRegionOptions: [], //已搜索产区列表
        AllWineryOptions: [], //已搜索酒庄列表
        AllGrapeOptions: [], //搜索过的所有葡萄品种
        inputVisible: false,
        inputValue: '',
        originTypeList:[],
        count: "",
      };
    },
    watch: {},
   
    mounted() {
      setTimeout(() => {
        if(this.isEdit){
         this.add_methodnew = this.add_method;
         this.auto_add_typeNew = this.auto_add_type ? this.auto_add_type : 1;
          if(this.auto_add_typeNew == 6) {
            this.chooseOptions = this.auto_add_content.map(item => {
              return item.name;
          });
          } else {
            this.chooseOptions = this.auto_add_content.map(item => {
              return item.id;
          });
          }
  
         console.log('po------',this.auto_add_content);
         if(this.auto_add_typeNew == 1) {
          this.CountryOptions =  this.auto_add_content;
          this.AllCountryOptions =  this.auto_add_content;
         } else if(this.auto_add_typeNew == 2) {
          this.RegionOptions = this.auto_add_content;
          this.AllRegionOptions = this.auto_add_content;
         }else if(this.auto_add_typeNew == 3) {
          this.WineryOptions = this.auto_add_content;
          this.AllWineryOptions = this.auto_add_content;
         } else if(this.auto_add_typeNew == 7) {
          this.GrapeOptions = this.auto_add_content;
          this.AllGrapeOptions = this.auto_add_content;
         }  else if(this.auto_add_typeNew == 8) {
          this.count = this.auto_add_content[0].name;
         }
      }
              }, 300);
      
      this.getProductCategory();
      this.getRecommendLabel();
      // if (this.isEdit) {
      //     setTimeout(() => {
      //         console.log("传过来的rowData", this.rowData);
      //         if (this.rowData.path != undefined) {
      //             this.path = this.rowData.path;
      //         } else {
      //             this.path = this.rowData.path_id;
      //         }
      //         if (typeof this.rowData.client[0] == "string") {
      //             this.EditClient = this.rowData.client.split(",");
      //         } else {
      //             this.EditClient = this.rowData.client.map(item => {
      //                 return String(item.id);
      //             });
      //         }
      //         console.log("popip", this.pathDetail);
      //         this.isDisableClient = JSON.parse(
      //             JSON.stringify(
      //                 this.pathDetail.find(item => item.id == this.path)
      //             )
      //         ).client.join(",");
      //         // this.isDisableClient = JSON.stringify(this.EditClient);
      //         // this.isDisableClient = JSON.parse(this.isDisableClient);
      //         if (this.rowData.ad_path_value_param != undefined) {
      //             this.EditParam = this.rowData.ad_path_value_param;
      //         } else {
      //             this.EditParam = this.rowData.la_path_value_param;
      //         }
      //     }, 1000);
      // }
      // this.getClientPath();
      // this.pathNum = this.path_id; //路径id
    },
    methods: {
      changeAutoAddType(value) {
        console.log(value);
        this.auto_add_typeNew = value;
        this.chooseOptions = [];
      },
      //国家
      getCountryList(keyword) {
        console.log(keyword);
        if (keyword) {
          this.$request.KingArea.getCountryList({
            keyword,
            page: 1,
            limit: 10,
          }).then((res) => {
            if (res.data.error_code == 0) {
              this.CountryOptions = res.data.data.list.map((item) => {
                  return {
                      id: item.id,
                      name: item.country_name_cn + item.country_name_en
                  };
                });
                
              this.CountryOptions.forEach(item => {
              const existingItem = this.AllCountryOptions.find(bItem => bItem.id === item.id);
              if (!existingItem) {
                this.AllCountryOptions.push(item);
              }
            });
            }
          });
        }
      },
      //酒庄
      getWineryList(keyword) {
        if (keyword) {
          this.$request.KingArea
            .getWineryList({
              keyword,
              page: 1,
              limit: 10,
            })
            .then((res) => {
              if (res.data.error_code == 0) {
                this.WineryOptions = res.data.data.list.map((item) => {
                  return {
                      id: item.id,
                      name:  item.winery_name_cn + item.winery_name_en
                  };
                });
                this.WineryOptions.forEach(item => {
              const existingItem = this.AllWineryOptions.find(bItem => bItem.id === item.id);
              if (!existingItem) {
                this.AllWineryOptions.push(item);
              }
            });
              }
            });
        }
      },
      getGrapeList(keyword) {
            if (keyword) {
                this.$request.KingArea
                    .getGrapeList({
                        keyword,
                        page: 1,
                        limit: 99,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                          this.GrapeOptions = res.data.data.list.map((item) => {
                          return {
                              id: item.id,
                              name:  item.gname_cn + item.gname_en
                          };
                        });
                        this.GrapeOptions.forEach(item => {
                      const existingItem = this.AllGrapeOptions.find(bItem => bItem.id === item.id);
                      if (!existingItem) {
                        this.AllGrapeOptions.push(item);
                      }
                    });
                        }
                    });
            }
        },
      //产区
      getRegionList(keyword) {
        if (keyword) {
          this.$request.KingArea
            .getRegionList({
              keyword,
              page: 1,
              limit: 10,
            })
            .then((res) => {
              if (res.data.error_code == 0) {
                this.RegionOptions = res.data.data.list.map((item) => {
                  return {
                      id: item.id,
                      name: item.regions_name_cn + item.regions_name_en
                  };
                });
                this.RegionOptions.forEach(item => {
              const existingItem = this.AllRegionOptions.find(bItem => bItem.id === item.id);
              if (!existingItem) {
                this.AllRegionOptions.push(item);
              }
            });
              }
            });
        }
      },
      getProductCategory() {
              return new Promise((resolve, reject) => {
                  this.$request.KingArea
                      .getProductCategory({
                          pid: 1,
                      })
                      .then((res) => {
                          if (res.data.error_code == 0) {
                              this.originTypeList = res.data.data.list;
                              const list = res.data.data.list.map((item) => ({
                                  ...item,
                              }));
                              const info = list.reduce((map, node) => {
                                  map[node.id] = node;
                                  if (list.some((item) => item.fid === node.id)) {
                                      node.children = [];
                                  }
                                  return map;
                              }, {});
                              this.typeList = list.filter((node) => {
                                  info[node.fid] &&
                                      info[node.fid].children.push(node);
                                  return !node.fid;
                              });
                              console.log("typeList", this.typeList);
                            
                              resolve("ok");
                          }
                      })
                      .catch((error) => {
                          reject(error);
                      });
              });
          },
  
          getRecommendLabel() {
              this.$request.KingArea
                  .labelList({ page: 1, limit: 999, type: 2 })
                  .then((res) => {
                      if (res.data.error_code === 0) {
                          this.tagOptions = res.data.data.list;
                      }
                  });
          },
  
          handleClose(tag) {
          this.chooseOptions.splice(this.chooseOptions.indexOf(tag), 1);
        },
        countryChange(data){
          console.log('changeCount', data);
        },
        showInput() {
          this.inputVisible = true;
          this.$nextTick(_ => {
            this.$refs.saveTagInput.$refs.input.focus();
          });
        },
  
        handleInputConfirm() {
          let inputValue = this.inputValue;
          if (inputValue) {
            this.chooseOptions.push(inputValue);
          }
          this.inputVisible = false;
          this.inputValue = '';
        },
     
      // //编辑时返回的数据
      getEditData() {
        
          let add_methodnew = this.add_methodnew;
          let auto_add_typeNew = this.auto_add_typeNew;
          //  chooseOptions = this.auto_add_content;
          var resultArr;
         if(auto_add_typeNew == 1) {
          resultArr = this.chooseOptions.map(item => {
            let found = this.AllCountryOptions.find(bItem => bItem.id == item);
            return found ? { id:item, name: found.name } : item;
          });
         } else if(auto_add_typeNew == 2) {
          resultArr = this.chooseOptions.map(item => {
            let found = this.AllRegionOptions.find(bItem => bItem.id == item);
            return found ? { id:item, name: found.name } : item;
          });
         } else if(auto_add_typeNew == 3) {
          resultArr = this.chooseOptions.map(item => {
            let found = this.AllWineryOptions.find(bItem => bItem.id == item);
            return found ? { id:item, name: found.name } : item;
          });
         }else if(auto_add_typeNew == 4) {
          resultArr = this.chooseOptions.map(item => {
            let found = this.tagOptions.find(bItem => bItem.id == item);
            return found ? { id:item, name: found.name } : null;
          });
         }else if(auto_add_typeNew == 5) {
          resultArr= this.chooseOptions.map(item => {
            let found = this.originTypeList.find(bItem => bItem.id == item);
            return found ? { id:item, name: found.name } : item;
          });
         } else if(auto_add_typeNew == 6) {
          resultArr= this.chooseOptions.map(item => {
             return{name:item,
              id:0
            } 
          });
         } else if(auto_add_typeNew == 7) {
          resultArr = this.chooseOptions.map(item => {
            let found = this.AllGrapeOptions.find(bItem => bItem.id == item);
            return found ? { id:item, name: found.name } : item;
          });
         }else if(auto_add_typeNew == 8) {
          resultArr= [{name:this.count, id:0}];
         }
         resultArr = resultArr.filter(item => item !== null);
        console.log('soshijjslkjfslkd');
          return { add_methodnew, auto_add_typeNew,  resultArr};
      }
      
    },
  };
  </script>
  
  <style lang="scss" scoped>
  /deep/ .el-form-item {
    margin-bottom: 5px;
  }
  </style>
  
  
