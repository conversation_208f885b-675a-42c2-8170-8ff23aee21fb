<template>
    <div>
        <div class="QrCode">
            <img :src="qrUrl" alt="" />
        </div>
        <div>
            <span>
                二维码地址：
            </span>
            <el-link
                :underline="false"
                type="primary"
                style="width:80%"
                @click="copy(rowData.qrcode_url)"
                >{{ rowData.qrcode_url }}</el-link
            >
        </div>
    </div>
</template>

<script>
import QRCode from "qrcode";
export default {
    props: ["rowData"],
    data() {
        return {
            qrUrl: "",
            baseUrl: ""
        };
    },
    mounted() {
        console.log("数据", this.rowData);
        // this.baseUrl =
        //     process.env.NODE_ENV === "development"
        //         ? "https://test-h5.wineyun.com/pages/activity/activity"
        //         : "https://h5.vinehoo.com/pages/activity/activity";
        this.qrCode();
    },
    methods: {
        //复制
        copy(qrcode_url) {
            let allUrl = qrcode_url;
            let cInput = document.createElement("input");
            cInput.value = allUrl;
            document.body.appendChild(cInput);
            cInput.select();
            document.execCommand("copy");
            this.$Message.success("复制成功");
            document.body.removeChild(cInput);
        },
        async qrCode() {
            // let url = this.baseUrl + this.rowData.qrcode_url;
            let url = this.rowData.qrcode_url;
            this.qrUrl = await QRCode.toDataURL(url);
        }
    }
};
</script>

<style>
.QrCode {
    display: flex;
    justify-content: center;
}
</style>
