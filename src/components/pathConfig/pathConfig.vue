<template>
    <div>
        <!-- 是编辑的时候 -->
        <div class="YesEdit" v-if="isEdit">
            <el-form-item
                label="路径"
                :label-width="formLabelWidth"
                prop="path_id"
            >
                <!-- :disabled="isEdit" -->
                <el-select
                    size="mini"
                    filterable
                    v-model="path"
                    placeholder="请选择路径"
                    @change="editPathChange(path)"
                >
                    <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="(item, index) in pathOptions"
                        :key="index"
                        >{{ item.label }}</el-option
                    >
                </el-select>
            </el-form-item>
            <el-form-item
                label="客户端"
                :label-width="formLabelWidth"
                prop="client"
            >
                <el-checkbox-group v-model="EditClient">
                    <el-checkbox
                        label="0"
                        :disabled="isDisableClient.indexOf('0') == -1"
                        >ios</el-checkbox
                    >

                    <el-checkbox
                        label="1"
                        :disabled="isDisableClient.indexOf('1') == -1"
                        >安卓</el-checkbox
                    >

                    <el-checkbox
                        label="2"
                        :disabled="isDisableClient.indexOf('2') == -1"
                        >小程序</el-checkbox
                    >

                    <el-checkbox
                        label="3"
                        :disabled="isDisableClient.indexOf('3') == -1"
                        >h5</el-checkbox
                    >

                    <el-checkbox
                        v-if="!isPC"
                        label="4"
                        :disabled="isDisableClient.indexOf('4') == -1"
                        >PC</el-checkbox
                    >
                </el-checkbox-group>
            </el-form-item>

            <div class="addparams">
                <div v-for="(edit, index) in EditParam" :key="index">
                    <el-form
                        :model="edit"
                        :rules="getValidationRules(edit.title)"
                    >
                        <!-- 参数 -->
                        <el-form-item
                            :label="edit.title"
                            :label-width="formLabelWidth"
                            prop="value"
                            :required="edit.title === 'id'"
                        >
                            <el-input
                                size="mini"
                                autocomplete="off"
                                :placeholder="
                                    edit.title === 'id'
                                        ? '请输入数字'
                                        : '请输入value'
                                "
                                v-model="edit.value"
                                style="width: 60%"
                                @input="validateIdInput(edit, $event)"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
        <!-- 新增的时候 -->
        <div class="add" v-else>
            <el-form-item
                label="路径"
                :label-width="formLabelWidth"
                prop="path_id"
            >
                <el-select
                    filterable
                    @change="addSelectChange"
                    v-model="pathNum"
                    placeholder="请选择路径"
                    size="mini"
                >
                    <el-option
                        :label="item.label"
                        :value="item.value"
                        v-for="(item, index) in pathOptions"
                        :key="index"
                        >{{ item.label }}</el-option
                    >
                </el-select>
            </el-form-item>

            <!-- key、value参数 -->
            <div v-for="item in pathDetail" :key="item.id">
                <div v-if="item.id == pathNum">
                    <el-form-item
                        label="客户端"
                        :label-width="formLabelWidth"
                        prop="client"
                    >
                        <el-checkbox-group v-model="item.client">
                            <el-checkbox
                                ref="client"
                                label="0"
                                :disabled="
                                    originParam.client.indexOf('0') == -1
                                "
                                >ios</el-checkbox
                            >

                            <el-checkbox
                                ref="client"
                                label="1"
                                :disabled="
                                    originParam.client.indexOf('1') == -1
                                "
                                >安卓</el-checkbox
                            >

                            <el-checkbox
                                ref="client"
                                label="2"
                                :disabled="
                                    originParam.client.indexOf('2') == -1
                                "
                                >小程序</el-checkbox
                            >

                            <el-checkbox
                                ref="client"
                                label="3"
                                :disabled="
                                    originParam.client.indexOf('3') == -1
                                "
                                >h5</el-checkbox
                            >

                            <el-checkbox
                                v-if="!isPC"
                                ref="client"
                                label="4"
                                :disabled="
                                    originParam.client.indexOf('4') == -1
                                "
                                >PC</el-checkbox
                            >
                        </el-checkbox-group>
                    </el-form-item>

                    <div class="addparams">
                        <div v-for="(add, index) in addParam" :key="index">
                            <el-form
                                :model="add"
                                :rules="getValidationRules(add.title)"
                            >
                                <!-- <el-card> -->
                                <!-- 参数 -->
                                <el-form-item
                                    :label="add.title"
                                    :label-width="formLabelWidth"
                                    prop="value"
                                    :required="add.title === 'id'"
                                >
                                    <el-input
                                        size="mini"
                                        autocomplete="off"
                                        :placeholder="
                                            add.title === 'id'
                                                ? '请输入数字'
                                                : '请输入value'
                                        "
                                        v-model="add.value"
                                        style="width: 60%"
                                        @input="validateIdInput(add, $event)"
                                    ></el-input>
                                </el-form-item>
                                <!-- </el-card> -->
                            </el-form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ["path_id", "isEdit", "rowData", "isPC"],
    data() {
        return {
            pathOptions: [],
            pageAttr: {
                page: 1,
                limit: 999,
            },
            pathNum: "",
            originParam: [], //客户端路径原始数据，用于判断value输入框是否禁用
            pathDetail: [], //
            formLabelWidth: "150px",
            EditClient: [],
            EditParam: [],
            isDisableClient: [],
            addParam: [],

            path: 1,
        };
    },
    watch: {
        pathNum: {
            handler(newVal, oldVal) {
                if (
                    newVal != " " &&
                    newVal != undefined &&
                    this.isEdit != true
                ) {
                    let PathList = JSON.stringify(
                        this.pathDetail.find((item) => item.id == newVal)
                    );
                    let data = {
                        pid: JSON.parse(PathList).id,
                    };
                    this.$request.clientPath
                        .FilterClientPath(data)
                        .then((res) => {
                            console.log("路径过滤", res);
                            if (res.data.error_code == 0) {
                                this.addParam = res.data.data;
                            }
                        });
                }
            },
        },
    },
    mounted() {
        if (this.isEdit) {
            setTimeout(() => {
                console.log("传过来的rowData", this.rowData);
                if (this.rowData.path != undefined) {
                    this.path = this.rowData.path;
                } else {
                    this.path = this.rowData.path_id;
                }
                if (typeof this.rowData.client[0] == "string") {
                    this.EditClient = this.rowData.client.split(",");
                } else {
                    this.EditClient = this.rowData.client.map((item) => {
                        return String(item.id);
                    });
                }
                console.log("popip", this.pathDetail);
                this.isDisableClient = JSON.parse(
                    JSON.stringify(
                        this.pathDetail.find((item) => item.id == this.path)
                    )
                ).client.join(",");
                // this.isDisableClient = JSON.stringify(this.EditClient);
                // this.isDisableClient = JSON.parse(this.isDisableClient);
                if (this.rowData.ad_path_value_param != undefined) {
                    this.EditParam = this.rowData.ad_path_value_param;
                } else {
                    this.EditParam = this.rowData.la_path_value_param;
                }
            }, 1000);
        }

        this.getClientPath();
        this.pathNum = this.path_id; //路径id
    },
    methods: {
        // 获取验证规则
        getValidationRules(title) {
            if (title === "id") {
                return {
                    value: [
                        {
                            required: true,
                            message: "id参数不能为空",
                            trigger: "blur",
                        },
                        {
                            pattern: /^\d+$/,
                            message: "id参数只能输入数字",
                            trigger: "blur",
                        },
                    ],
                };
            }
            return {};
        },

        // 验证id输入
        validateIdInput(param, value) {
            if (param.title === "id") {
                // 只允许数字输入
                const numericValue = value.replace(/[^\d]/g, "");
                if (value !== numericValue) {
                    param.value = numericValue;
                    this.$message.warning("id参数只能输入数字");
                }
            }
        },

        //编辑时更换路径
        editPathChange(path) {
            this.EditClient = JSON.parse(
                JSON.stringify(this.pathDetail.find((item) => item.id == path))
            ).client;
            this.isDisableClient = JSON.parse(
                JSON.stringify(this.EditClient)
            ).join(",");
            if (path != " " && path != undefined && this.isEdit == true) {
                console.log("换了路径", path);
                let PathList = JSON.stringify(
                    this.pathDetail.find((item) => item.id == path)
                );
                let data = {
                    pid: JSON.parse(PathList).id,
                };
                this.$request.clientPath.FilterClientPath(data).then((res) => {
                    // console.log("路径过滤", res);
                    if (res.data.error_code == 0) {
                        this.EditParam = res.data.data;
                    }
                });
            }
        },
        //新增是判断value输入框是否禁用
        addSelectChange(val) {
            this.originParam = JSON.stringify(
                this.pathDetail.find((item) => item.id == this.pathNum)
            );
            // param
            this.originParam = JSON.parse(this.originParam);
        },
        //获取路径
        async getClientPath() {
            let res = await this.$request.clientPath.getClientPathList(
                this.pageAttr
            );
            // console.log("客户端路径列表", res);
            if (res.data.error_code == 0) {
                this.pathOptions = res.data.data.list.map((item) => {
                    item.label = item.path_name;
                    item.value = item.id;
                    return { label: item.label, value: item.value };
                });
                this.pathDetail = res.data.data.list;
                this.pathDetail.map((item) => {
                    item.client = item.client.split(",");
                    if (typeof item.param == "string") {
                        item.param = JSON.parse(item.param);
                    }
                });
                // console.log("更改后的客户端路径列表", this.pathDetail);
            }
        },

        //新增时返回的数据
        getAddData() {
            console.log("路径详情", typeof this.pathNum);
            if (this.pathNum != " ") {
                // 验证id参数
                const idParams = this.addParam.filter(
                    (item) => item.title === "id"
                );
                for (let param of idParams) {
                    if (!param.value || param.value.trim() === "") {
                        this.$message.error("id参数不能为空");
                        return false;
                    }
                    if (!/^\d+$/.test(param.value)) {
                        this.$message.error("id参数只能输入数字");
                        return false;
                    }
                }

                let client = [];
                client = this.pathDetail.find(
                    (item) => item.id == this.pathNum
                ).client;
                // console.log("过滤后的", this.addParam);
                let addParamBox = JSON.parse(JSON.stringify(this.addParam));
                let arr = addParamBox.map((item) => {
                    delete item.title;
                    item.peid = item.id;
                    delete item.id;
                    return item;
                });
                // console.log("222参数数组", arr);
                let pathNum1 = this.pathNum;
                return { client, arr, pathNum1 };
            } else {
                this.$Message.error("请选择路径");
            }
        },
        //编辑时返回的数据
        getEditData() {
            // 验证id参数
            const idParams = this.EditParam.filter(
                (item) => item.title === "id"
            );
            for (let param of idParams) {
                if (!param.value || param.value.trim() === "") {
                    this.$message.error("id参数不能为空");
                    return false;
                }
                if (!/^\d+$/.test(param.value)) {
                    this.$message.error("id参数只能输入数字");
                    return false;
                }
            }

            // console.log("111", this.EditParam);
            let EditParamArr = this.EditParam.map((item) => {
                delete item.title;
                return item;
            });
            let pathNum2 = this.path;
            let EditClientArr = this.EditClient;
            console.log(
                "修改的路径，客户端，参数",
                pathNum2,
                EditClientArr,
                EditParamArr
            );
            return { pathNum2, EditClientArr, EditParamArr };
        },
    },
};
</script>

<style lang="scss" scoped>
/deep/ .el-form-item {
    margin-bottom: 5px;
}
</style>
