<template>
    <div>
        <el-table
            :data="data"
            :border="showBorder"
            :size="size"
            :header-cell-style="headerCellStyle"
            :cell-style="cellStyle"
        >
            <slot></slot>
        </el-table>

        <VhPagination
            v-if="showPagination"
            :pageInfo="pageInfo"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
    </div>
</template>

<script>
export default {
    name: "VhTable",
    props: {
        data: {
            type: Array,
            default: () => [],
        },
        showBorder: {
            type: Boolean,
            default: true,
        },
        size: {
            type: String,
            default: "mini",
        },
        showPagination: {
            type: Boolean,
            default: true,
        },
        headerCellStyle: {
            type: Object,
            default: () => ({ "text-align": "center" }),
        },
        cellStyle: {
            type: Object,
            default: () => ({ "text-align": "center" }),
        },
        pageInfo: {
            type: Object,
            default: () => ({
                currentPage: 1,
                pageSizes: [10, 30, 50, 100, 200],
                pageSize: 10,
                total: 0,
            }),
        },
    },
    methods: {
        handleSizeChange(value) {
            this.$emit("size-change", value);
        },
        handleCurrentChange(value) {
            this.$emit("current-change", value);
        },
    },
};
</script>

<style lang="scss" scoped>
::deep .el-table th {
    text-align: center;
}
</style>
