<template>
    <div style="margin-top: 20px">
        <el-row v-if="pageInfo.total" type="flex" justify="center">
            <el-pagination
                :current-page="pageInfo.currentPage"
                :page-sizes="pageInfo.pageSizes"
                :page-size="pageInfo.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageInfo.total"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </el-row>
    </div>
</template>

<script>
export default {
    name: "VhPagination",
    props: {
        pageInfo: {
            type: Object,
            default: () => ({
                currentPage: 1,
                pageSizes: [10, 30, 50, 100, 200],
                pageSize: 10,
                total: 0,
            }),
        },
    },
    methods: {
        handleSizeChange(value) {
            this.$emit("size-change", value);
        },
        handleCurrentChange(value) {
            this.$emit("current-change", value);
        },
    },
    created() {
        console.log("VhPagination pageInfo", this.pageInfo);
    },
};
</script>

<style lang="scss" scoped></style>
